/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[11].oneOf[13].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[11].oneOf[13].use[3]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[11].oneOf[13].use[4]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[11].oneOf[13].use[5]!./app/[locale]/ui/components/components.scss ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
@charset "UTF-8";
.flex-box-with-4items {
  display: flex;
  gap: 25.5px;
  flex-wrap: wrap;
}
.flex-box-with-4items__card {
  border-radius: 6px;
  background-color: #fff;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  width: 300px;
  position: relative;
}
.flex-box-with-4items__card__image {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
  width: 100%;
  border-radius: 6px;
  overflow: hidden;
  position: relative;
}
.flex-box-with-4items__card__image__play {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  z-index: 1;
}
.flex-box-with-4items__card__info {
  padding: 20px 16px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 6px;
  width: 100%;
}
.flex-box-with-4items__card__info div {
  font-size: var(--font-large);
}
.flex-box-with-4items__card__info span {
  color: var(--text-mark);
}
.flex-box-with-4items__card__info--product {
  text-align: center;
  padding: 10px 16px 20px;
}
.flex-box-with-4items__card__info--product span {
  color: var(--text-description);
}
.flex-box-with-4items__card__info--pride {
  gap: 0;
}
.flex-box-with-4items__card--video:hover, .flex-box-with-4items__card--link:hover {
  box-shadow: 0px 6px 20px 0px rgba(0, 0, 0, 0.1);
}
.flex-box-with-4items__card--video:hover .flex-box-with-4items__card__info > div, .flex-box-with-4items__card--link:hover .flex-box-with-4items__card__info > div {
  color: var(--color-theme);
}
.flex-box-with-4items--detail .flex-box-with-4items__card {
  width: 290px;
}

.flex-box-with-2items {
  display: flex;
  gap: 30px;
  flex-wrap: wrap;
}
.flex-box-with-2items__item {
  width: 625px;
  border-radius: 4px;
  display: flex;
  justify-content: space-between;
  background-color: #fff;
}
.flex-box-with-2items__item:hover {
  cursor: pointer;
}
.flex-box-with-2items__item > div:first-of-type {
  border-radius: 4px;
  overflow: hidden;
  width: 240px;
  height: 160px;
}
.flex-box-with-2items__item > div:last-of-type {
  display: flex;
  flex-direction: column;
  flex: 1 1;
  padding: 20px;
}
.flex-box-with-2items__item > div:last-of-type h6 {
  font-size: var(--font-large);
  font-weight: var(--font-bold);
  margin: 0;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}
.flex-box-with-2items__item > div:last-of-type p {
  color: var(--text-description);
  margin-top: 6px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}
.flex-box-with-2items__item > div:last-of-type span {
  color: var(--text-mark);
  margin-top: 8px;
}
.flex-box-with-2items--product-detail {
  gap: 0;
  column-gap: 20px;
  row-gap: 30px;
}
.flex-box-with-2items--product-detail .flex-box-with-2items__item {
  width: 600px;
}

.dropdown-window {
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translate(-50%, 100%);
  z-index: 5;
}
.dropdown-window__above {
  display: flex;
  justify-content: center;
}
.dropdown-window__list {
  background-color: #fff;
  border-radius: 10px;
  overflow: hidden;
  padding: 10px 1.5px;
}
.dropdown-window__list__item {
  min-width: 122px;
  text-align: center;
  height: 40px;
  line-height: 40px;
  color: var(--text-description);
}
.dropdown-window__list__item:hover {
  background-color: #f6f6f6;
  color: var(--color-theme);
  cursor: pointer;
}
.dropdown-window__list__item__link {
  width: 100%;
  height: 100%;
  text-align: center;
  line-height: 40px;
}
.dropdown-window__placeholder {
  position: absolute;
  width: 100%;
  height: 24px;
  top: -24px;
  left: 0;
}
.dropdown-window__mask {
  position: fixed;
  width: 100vw;
  height: 200vh;
  top: 0;
  left: 0;
  z-index: 4;
}

.bread-crumbs {
  display: flex;
  gap: 5.5px;
  align-items: center;
}
.bread-crumbs__item {
  color: var(--text-description);
  font-size: var(--font-small);
}
.bread-crumbs__item--current {
  composes: bread-crumbs__item;
  color: var(--text-mark);
}

.banner {
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  height: 100%;
  width: calc(100vw - 10px);
}
.banner__image {
  width: 100%;
  height: 100%;
  object-fit: cover; /* 保持图像原始尺寸 */
  object-position: center; /* 将图像的中心点显示在容器的中心 */
}

.pagination {
  display: flex;
  gap: 10px;
  justify-content: center;
}
.pagination__page, .pagination__pagejumper {
  padding: 0 14px;
  height: 32px;
  color: var(--text-description);
  border-radius: 4px;
  background-color: #fff;
}
.pagination__page--active, .pagination__pagejumper--active {
  color: #fff;
  background-color: var(--color-theme);
}
.pagination__page:disabled, .pagination__pagejumper:disabled {
  opacity: 0.6;
}

.page-tabs {
  height: 220px;
  position: relative;
  background-color: rgb(179, 220, 252);
}
.page-tabs__content {
  width: var(--width-content);
  margin: auto;
  height: 100%;
  position: relative;
}
.page-tabs__content__title {
  display: flex;
  position: absolute;
  top: 31px;
  left: 20px;
  gap: 8px;
}
.page-tabs__content__title h1 {
  color: #fff;
}
.page-tabs__content__items {
  display: flex;
  gap: 14px;
  position: absolute;
  top: 50%;
  left: 0;
  width: 100%;
  justify-content: center;
  transform: translateY(-50%);
}
.page-tabs__content__items__item {
  height: 42px;
  padding: 0 30px;
  border-radius: 4px;
  font-size: var(--font-medium);
  color: var(--text-description);
  line-height: 42px;
  background-color: #fff;
}
.page-tabs__content__items__item--active {
  background-color: var(--color-theme);
  color: #fff;
}
.page-tabs__search {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -59%);
  width: 680px;
  height: 44px;
  display: flex;
  border-radius: 6px;
  overflow: hidden;
  background-color: rgba(255, 255, 255, 0.8);
}
.page-tabs__search input {
  border: none;
  flex: 1 1;
  padding: 0 20px;
  color: var(--text-dark);
}
.page-tabs__search input::placeholder {
  color: var(--text-mark);
}
.page-tabs__search button {
  padding: 0 20px;
  height: 44px;
  line-height: 44px;
  border-radius: 6px;
  color: #fff;
  background-color: var(--color-theme);
  font-size: var(--font-medium);
  display: flex;
  align-items: center;
  gap: 6px;
}

.nav-list {
  padding: 20px;
  background-color: #fff;
  width: 100vw;
}
.nav-list__item {
  display: flex;
  align-items: center;
  border-bottom: 1px solid var(--gray-4);
  height: 50px;
  line-height: 50px;
}
.nav-list__item:hover {
  cursor: pointer;
}
.nav-list__item:last-of-type {
  border-bottom: none;
}
.nav-list__item__icon {
  display: flex;
  align-items: center;
  justify-content: center;
}
.nav-list__item__title {
  font-weight: var(--font-bolder);
  margin-left: 10px;
}
.nav-list__item__button {
  width: 32px;
  height: 32px;
}
.nav-list__item--sub .nav-list__item__title {
  font-weight: normal;
}
.nav-list--footer {
  background-color: #282c30;
}
.nav-list--footer .nav-list__item {
  border-bottom: none;
  box-shadow: 0px 1px 0px 0px #3a3e42;
}
.nav-list--footer .nav-list__item__title {
  color: var(--gray-4);
}
.nav-list--footer .nav-list__item--sub .nav-list__item__title {
  color: var(--gray-2);
  padding-left: 20px;
}
.nav-list--footer .nav-list__item__icon {
  display: none;
}

.back-top {
  position: fixed;
  bottom: 267px;
  right: 20px;
  z-index: 5;
  padding: 10px;
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: #fff;
  border-radius: 6px;
}
.back-top span {
  font-size: var(--font-small);
  color: var(--text-description);
}
.back-top--hide {
  display: none;
}

.show-more {
  width: 100%;
  height: 49px;
  text-align: center;
  line-height: 49px;
  color: var(--text-description);
  background-color: #fff;
  margin-top: 15px;
  border-radius: 6px;
}

.cylan-certificates {
  width: 100%;
  white-space: nowrap;
  overflow: hidden;
  overflow-x: scroll;
  margin-top: 20px;
  padding: 0 16px;
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
  overflow: -moz-scrollbars-none; /* Firefox */
}
.cylan-certificates ::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Opera */
}
.cylan-certificates > div {
  display: inline-block;
  margin-right: 10px;
}
.cylan-certificates > div > div :first-of-type {
  box-shadow: 0px 6px 20px 0px rgba(0, 0, 0, 0.1);
  border-radius: 6px;
  overflow: hidden;
}
.cylan-certificates > div > div:last-of-type {
  margin-top: 15px;
  text-align: center;
  font-size: var(--font-medium);
  color: var(--text-description);
  white-space: wrap;
  display: -webkit-box;
  -webkit-line-clamp: 2; /* 最多显示 3 行 */
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  height: 40px;
}

@media (min-width: 451px) and (max-width: 1280px) {
  .flex-box-with-4items {
    gap: 20px;
  }
  .flex-box-with-4items--product {
    justify-content: space-between;
    gap: 0;
    row-gap: 23px;
    column-gap: 0;
  }
  .flex-box-with-4items__card {
    width: calc(25% - 15px);
  }
  .flex-box-with-4items__card__info div {
    font-size: var(--font-normal);
  }
  .flex-box-with-4items__card__info--pride div {
    font-size: var(--font-medium);
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
  }
  .flex-box-with-4items__card--product {
    width: calc(50% - 10px);
  }
  .flex-box-with-4items--detail .flex-box-with-4items__card {
    width: calc(25% - 15px);
  }
  .flex-box-with-2items {
    flex-direction: column;
    flex-wrap: nowrap;
    gap: 20px;
  }
  .flex-box-with-2items__item {
    width: 100%;
    height: 160px;
  }
  .flex-box-with-2items__item > div:first-of-type {
    width: 240px;
    height: 100%;
  }
  .flex-box-with-2items__item > div:last-of-type {
    flex: 1 1;
    height: 100%;
    padding: 20px;
    display: flex;
    flex-direction: column;
  }
  .flex-box-with-2items--product-detail .flex-box-with-2items__item {
    width: 100%;
  }
  .banner {
    width: calc(100vw - 10px);
  }
  .page-tabs {
    height: 176px;
  }
  .page-tabs__search {
    top: auto;
    bottom: 46px;
    transform: translate(-50%);
  }
}
@media (max-width: 450px) {
  .flex-box-with-4items {
    gap: 15px;
  }
  .flex-box-with-4items__card {
    width: 100%;
  }
  .flex-box-with-4items__card__info--product {
    padding: 0 20px 10px;
  }
  .flex-box-with-4items--detail .flex-box-with-4items__card {
    width: 100%;
  }
  .flex-box-with-2items {
    flex-direction: column;
    gap: 15px;
  }
  .flex-box-with-2items__item {
    width: 100%;
    height: 93px;
  }
  .flex-box-with-2items__item > div:first-of-type {
    width: 140px;
    height: 100%;
  }
  .flex-box-with-2items__item > div:last-of-type {
    flex: 1 1;
    height: 100%;
    padding: 7px 8px;
    flex-direction: column;
    gap: 0;
    justify-content: space-between;
  }
  .flex-box-with-2items--product-detail .flex-box-with-2items__item {
    width: 100%;
  }
  .back-top {
    bottom: 50px;
  }
  .banner {
    width: 100vw;
  }
  .page-tabs {
    height: 120px;
  }
  .page-tabs__search {
    top: auto;
    bottom: 46px;
    transform: translate(-50%);
  }
  .page-tabs__content__title {
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
  }
  .page-tabs__tabs-small {
    background-color: #fff;
    display: flex;
    padding: 0 16px;
    gap: 10px;
    width: 100vw;
    overflow-x: scroll;
    -ms-overflow-style: none; /* IE and Edge */
    scrollbar-width: none; /* Firefox */
    overflow: -moz-scrollbars-none; /* Firefox */
  }
  .page-tabs__tabs-small ::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Opera */
  }
  .page-tabs__tabs-small__tab {
    height: 48px;
    text-align: center;
    line-height: 48px;
    padding: 0 10px;
    flex-shrink: 0;
    color: var(--text-description);
    font-size: var(--font-medium);
  }
  .page-tabs__tabs-small__tab--active {
    color: var(--color-theme);
    position: relative;
  }
  .page-tabs__tabs-small__tab--active::after {
    content: " ";
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 26px;
    height: 3px;
    border-radius: 2px;
    background-color: var(--color-theme);
  }
}
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[11].oneOf[9].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[11].oneOf[9].use[3]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[11].oneOf[9].use[4]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[11].oneOf[9].use[5]!./app/[locale]/ui/home/<USER>
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
@charset "UTF-8";
.home_nav--scrolled__f5oaX {
  box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.1);
}

.home_nav__gr65i {
  z-index: 10;
  width: 100vw;
  background-color: #fff;
  height: 66px;
  position: fixed;
  top: 0;
  left: 0;
}
.home_nav__placeholder__R_bDj {
  height: 66px;
}
.home_nav__content__gXoig {
  display: flex;
  align-items: center;
  height: 66px;
  line-height: 66px;
  width: var(--width-content);
  margin: auto;
}
.home_nav__list__dmRBz {
  display: flex;
  gap: 34px;
  margin: auto;
}
.home_nav__list__item__Ti9E4 {
  height: 66px;
  line-height: 66px;
  display: flex;
  align-items: center;
  gap: 5.5px;
  font-size: var(--font-medium);
}
.home_nav__list__item__Ti9E4:hover {
  cursor: pointer;
}
.home_nav__list__item--link__dx88I:hover {
  color: var(--color-theme);
}
.home_nav__list__item--active__oPRJX {
  color: var(--color-theme);
  border-bottom: 2px solid var(--color-theme);
}
.home_nav__right__4GRXj {
  display: flex;
  gap: 10px;
  align-items: center;
}
.home_nav__right__language__YzU4O {
  width: 108px;
  display: flex;
  justify-content: center;
  gap: 8px;
  font-size: var(--font-medium);
}
.home_nav__right__language__text__yUNmB {
  color: var(--text-mark);
  font-size: var(--font-medium);
}
.home_nav__right__language__text__yUNmB:hover {
  cursor: pointer;
}
.home_nav__right__language__text--active__e4h1y {
  color: var(--text-dark);
}
.home_nav__right__language__text--active__e4h1y:hover {
  cursor: default;
}
.home_nav__right__search__QAvd_ {
  width: 44px;
  height: 44px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: auto;
}
.home_nav__right__menu__tMG4s {
  width: 49px;
  height: 50px;
}
.home_nav__drop__RNd3y {
  position: fixed;
  top: 66px;
  left: 0;
  z-index: 9;
}
.home_nav__mask__YVj5E {
  position: fixed;
  z-index: 8;
  left: 0;
  top: 0;
  width: 100vw;
  height: 1000vh;
  background-color: rgba(0, 0, 0, 0.3);
}

.home_banner-slider__UBj9I {
  position: relative;
  height: 500px;
  background-color: #c4c4c4;
  overflow: hidden;
}
.home_banner-slider__swiper__9Bl8q {
  width: 100%;
  height: 100%;
  max-width: 2560px;
  margin: auto;
}
.home_banner-slider__slide__2U7Uu {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgb(13, 114, 153);
}
.home_banner-slider__button__GKjGy {
  position: absolute;
  width: 44px;
  height: 44px;
  top: 50%;
  transform: translateY(-50%);
  z-index: 10;
  cursor: pointer;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}
.home_banner-slider__button__GKjGy:hover {
  background: rgba(0, 0, 0, 0.5);
  transform: translateY(-50%) scale(1.1);
}
.home_banner-slider__button__GKjGy.home_swiper-button-disabled__siaDk {
  opacity: 0.3;
  cursor: not-allowed;
}
.home_banner-slider__button__GKjGy.home_swiper-button-disabled__siaDk:hover {
  background: rgba(0, 0, 0, 0.3);
  transform: translateY(-50%) scale(1);
}
.home_banner-slider__button-prev__VeikB {
  left: 100px;
}
.home_banner-slider__button-next__UC5d2 {
  right: 100px;
}
.home_banner-slider__pagination__J58et {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 18px;
  z-index: 10;
}
.home_banner-slider__bullet__c3a9X {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.5);
  cursor: pointer;
  transition: all 0.3s ease;
  opacity: 1;
}
.home_banner-slider__bullet__c3a9X:hover {
  background-color: rgba(255, 255, 255, 0.8);
  transform: scale(1.2);
}
.home_banner-slider__bullet--active__5BpSZ {
  background-color: #fff;
  transform: scale(1.2);
}
@media (max-width: 768px) {
  .home_banner-slider__UBj9I {
    height: 300px;
  }
  .home_banner-slider__switcher__SoaxS {
    left: 20px;
    width: 36px;
    height: 36px;
  }
  .home_banner-slider__switcher--right___84yN {
    right: 20px;
  }
  .home_banner-slider__indicator__0OOU4 {
    bottom: 15px;
    gap: 12px;
  }
  .home_banner-slider__indicator__item__f8vBh {
    width: 10px;
    height: 10px;
  }
}
@media (max-width: 480px) {
  .home_banner-slider__UBj9I {
    height: 250px;
  }
  .home_banner-slider__switcher__SoaxS {
    left: 10px;
    width: 32px;
    height: 32px;
  }
  .home_banner-slider__switcher--right___84yN {
    right: 10px;
  }
}

.home_hot-spot__HmXBc {
  width: var(--width-content);
  margin: auto;
  margin-top: 34px;
}
.home_hot-spot__captain__P7sAg {
  display: flex;
  height: 31px;
  align-items: center;
  line-height: 31px;
  margin-bottom: 16px;
  position: relative;
}
.home_hot-spot__captain__P7sAg h2 {
  margin: 0;
}
.home_hot-spot__captain__more__hoe30 {
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  height: 31px;
  line-height: 31px;
  align-items: center;
  gap: 4px;
  font-size: var(--font-small);
  color: var(--text-description);
}
.home_hot-spot__news__mFPbX {
  display: flex;
  justify-content: space-between;
}
.home_hot-spot__news__left__bYNbF, .home_hot-spot__news__right__IYxxG {
  width: 625px;
  height: 350px;
}
.home_hot-spot__news__left__bYNbF {
  border-radius: 6px;
  background-color: #fff;
  overflow: hidden;
}
.home_hot-spot__news__right__IYxxG {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.home_hot-spot__news__item__i6svw {
  height: 160px;
  border-radius: 6px;
  background-color: #fff;
  display: flex;
  overflow: hidden;
}
.home_hot-spot__news__item__info__GSDkz {
  height: 100%;
}
.home_hot-spot__news__item__image__0Dj0A {
  width: 625px;
  height: 240px;
}
.home_hot-spot__news__item__image--right__scey9 {
  width: 240px;
  height: 160px;
}
.home_hot-spot__news__item__info__GSDkz {
  padding: 16.5px 20px;
  display: flex;
  flex-direction: column;
  gap: 6px;
}
.home_hot-spot__news__item__info__GSDkz div {
  color: var(--text-description);
  overflow: hidden;
  text-overflow: ellipsis;
}
.home_hot-spot__news__item__info__GSDkz span {
  display: block;
  font-size: var(--font-small);
}
.home_hot-spot__news__item--left__W7YL9 {
  width: 100%;
  height: 100%;
  display: block;
}
.home_hot-spot__news__item--left__W7YL9 .home_hot-spot__news__item__info__GSDkz div {
  white-space: nowrap;
}

.home_about__vPbFi {
  margin-top: 30px;
  position: relative;
}
.home_about__cover__SPvuD {
  position: absolute;
  top: 0;
  left: 0;
  height: 221px;
  width: 100%;
  background: linear-gradient(0deg, #f5f7fa 0%, #0078e7);
}
.home_about__content__EA9EW {
  position: relative;
  z-index: 1;
  width: var(--width-content);
  margin: auto;
  padding-top: 30px;
}
.home_about__content__EA9EW h3 {
  color: #fff;
}
.home_about__content__time__HcHq6 {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 97px;
  margin-top: 12px;
}
.home_about__content__time__item__n4W8C {
  color: #fff;
  text-align: center;
}
.home_about__content__time__item__n4W8C div:first-of-type {
  height: 42px;
  line-height: 42px;
  display: flex;
  align-items: center;
  gap: 4px;
}
.home_about__content__time__item__n4W8C div:first-of-type span:first-of-type {
  font-size: 35px;
  font-weight: 500;
}
.home_about__content__time__item__n4W8C div:first-of-type span:last-of-type {
  font-size: var(--font-medium);
}
.home_about__content__time--page__Azkeq .home_about__content__time__item__n4W8C {
  color: var(--text-dark);
}
.home_about__content__time--page__Azkeq .home_about__content__time__item__n4W8C div:last-of-type {
  color: var(--text-description);
}
.home_about__content__prides__zHCpT {
  margin-top: 34px;
}

.home_contacts__TRH4N {
  display: flex;
  justify-content: space-between;
  margin-top: 52px;
}
.home_contacts--page__0BV0w {
  margin-top: 0;
}
.home_contacts__info__pIGy0 {
  width: 625px;
  height: 249px;
  border-radius: 6px;
  background-color: #fff;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  gap: 14px;
  padding: 20px;
}
.home_contacts__info__items__qUSi9 {
  margin: auto 0;
  display: flex;
  flex-direction: column;
  gap: 14px;
  justify-content: center;
}
.home_contacts__info__title__3_UHT {
  font-size: var(--font-large);
}
.home_contacts__info__item__eDIm0 {
  display: flex;
  gap: 12px;
  color: var(--text-description);
}
.home_contacts__address___ZQdr {
  width: 625px;
  height: 249px;
  border-radius: 6px;
  overflow: hidden;
}

.home_footer__qefFZ {
  margin-top: 38px;
  background-color: #282c30;
}
.home_footer__qefFZ > div {
  width: var(--width-content);
  margin: auto;
}
.home_footer__logo__jG71u {
  margin-right: 48px;
}
.home_footer__links__q5uiZ {
  padding: 20px;
  display: flex;
  justify-content: center;
  gap: 51px;
}
.home_footer__links__item__gB0TO {
  display: flex;
  flex-direction: column;
  gap: 10px;
  width: 140px;
  align-items: flex-start;
}
.home_footer__links__item__gB0TO div {
  color: var(--gray-4);
  font-weight: var(--font-bolder);
}
.home_footer__links__item__gB0TO span {
  color: var(--gray-1);
  font-weight: 400;
}
.home_footer__links__follow__jv8nP {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 10px;
}
.home_footer__links__follow__jv8nP > div {
  color: var(--gray-1);
}
.home_footer__links__follow__weixin__yeCNp {
  position: relative;
}
.home_footer__links__follow__weixin__yeCNp > div {
  position: absolute;
  z-index: 3;
  left: 80%;
  bottom: 80%;
  background-color: #fff;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-bottom: 6px;
}
.home_footer__links__follow__weixin__yeCNp > div span {
  text-align: center;
  color: var(--text-description);
  padding-bottom: 4px;
  font-size: var(--font-small);
}
.home_footer__links__follow__weixin__yeCNp > div a {
  color: var(--color-theme);
  font-size: var(--font-small);
}
.home_footer__copyright__M6lua {
  text-align: center;
  font-size: var(--font-small);
  color: var(--gray);
  padding: 10px 0;
}
.home_footer__copyright__link__PBT0B:hover {
  color: #fff;
}

@media (min-width: 451px) and (max-width: 1280px) {
  .home_nav__content__gXoig {
    justify-content: space-between;
    padding: 0 15px;
  }
  .home_hot-spot__HmXBc {
    padding: 0 20px;
  }
  .home_hot-spot__captain__P7sAg h2 {
    text-align: left;
    margin: 0;
  }
  .home_hot-spot__news__left__bYNbF, .home_hot-spot__news__right__IYxxG {
    width: calc(50% - 10px);
  }
  .home_hot-spot__news__item__image__0Dj0A {
    width: 100%;
    height: 71%;
  }
  .home_hot-spot__news__item__image--right__scey9 {
    width: 50%;
    height: 100%;
  }
  .home_hot-spot__news__item__info__GSDkz {
    width: 50%;
    height: 100%;
  }
  .home_hot-spot__news__item__info__GSDkz h4 {
    font-size: var(--font-medium);
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .home_hot-spot__news__item__info__GSDkz div {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: normal;
  }
  .home_hot-spot__news__item--left__W7YL9 .home_hot-spot__news__item__info__GSDkz {
    height: 29%;
    width: 100%;
  }
  .home_hot-spot__news__item--left__W7YL9 .home_hot-spot__news__item__info__GSDkz h4 {
    display: block;
    font-size: 20px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    margin: 0;
    text-align: center;
    padding: 0 10px;
  }
  .home_hot-spot__news__item--left__W7YL9 .home_hot-spot__news__item__info__GSDkz div {
    white-space: normal;
  }
  .home_hot-spot__news__item--left__W7YL9 .home_hot-spot__news__item__info__GSDkz span {
    display: none;
  }
  .home_about__content__EA9EW {
    padding: 30px 20px;
  }
  .home_contacts__info__pIGy0, .home_contacts__address___ZQdr {
    width: calc(50% - 10px);
  }
  .home_footer__links__q5uiZ {
    gap: 0;
    width: 120px;
  }
  .home_banner-slider__UBj9I {
    height: 400px;
  }
}
@media (max-width: 450px) {
  .home_hot-spot__HmXBc {
    padding: 0 16px;
  }
  .home_footer__qefFZ {
    margin-top: 35px;
  }
  .home_footer__logo__jG71u {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px 0 15px 0;
  }
  .home_footer__links__follow__jv8nP {
    padding-left: 30px;
  }
  .home_about__vPbFi h3 {
    padding-left: 16px;
  }
  .home_about__content__time__HcHq6 {
    gap: 14px;
    justify-content: center;
  }
  .home_about__content__time__item__n4W8C {
    max-width: 140px;
  }
  .home_about__content__time__item__n4W8C div:first-of-type {
    justify-content: center;
  }
  .home_hot-spot__news__mFPbX {
    flex-wrap: wrap;
    gap: 15px;
  }
  .home_hot-spot__news__left__bYNbF, .home_hot-spot__news__right__IYxxG {
    width: 100%;
    height: auto;
  }
  .home_hot-spot__news__right__IYxxG {
    gap: 15px;
    justify-content: flex-start;
  }
  .home_hot-spot__news__item__i6svw {
    height: 93px;
  }
  .home_hot-spot__news__item__info__GSDkz {
    padding: 7px 8px;
    justify-content: center;
    gap: 14px;
  }
  .home_hot-spot__news__item__info__GSDkz h4 {
    font-size: var(--font-normal);
    display: -webkit-box;
    -webkit-line-clamp: 2; /* 最多显示 3 行 */
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .home_hot-spot__news__item__info__GSDkz div {
    display: none;
  }
  .home_hot-spot__news__item__image__0Dj0A {
    width: 100%;
    height: 132px;
  }
  .home_hot-spot__news__item__image--right__scey9 {
    width: 60%;
  }
  .home_hot-spot__news__item--left__W7YL9 {
    height: auto;
    display: flex;
    flex-direction: column;
  }
  .home_hot-spot__news__item--left__W7YL9 .home_hot-spot__news__item__info__GSDkz {
    height: auto;
    padding: 20px;
    display: flex;
    gap: 6px;
    justify-content: flex-start;
  }
  .home_hot-spot__news__item--left__W7YL9 .home_hot-spot__news__item__info__GSDkz h4 {
    font-size: 20px;
  }
  .home_hot-spot__news__item--left__W7YL9 .home_hot-spot__news__item__info__GSDkz div {
    display: block;
  }
  .home_nav__gr65i {
    height: 54px;
  }
  .home_nav__placeholder__R_bDj {
    height: 54px;
  }
  .home_nav__content__gXoig {
    justify-content: space-between;
    padding: 2px 15px;
    height: 54px;
  }
  .home_nav__list__item__Ti9E4 {
    height: 54px;
    line-height: 54px;
  }
  .home_nav__drop__RNd3y {
    top: 54px;
  }
  .home_contacts__TRH4N {
    margin-top: 20px;
    flex-direction: column;
    justify-content: flex-start;
    gap: 20px;
    padding: 16px;
  }
  .home_contacts__info__pIGy0, .home_contacts__address___ZQdr {
    width: 100%;
  }
  .home_contacts__address___ZQdr {
    height: 200px;
  }
  .home_banner-slider__UBj9I {
    height: 44vw;
  }
  .home_banner-slider__indicator__0OOU4 {
    bottom: 10px;
  }
  .home_banner-slider__indicator__item__f8vBh {
    width: 4px;
    height: 4px;
  }
  .home_banner-slider__indicator__item--active__Pkcak {
    background-color: transparent;
    border: 1px solid #fff;
  }
  .home_banner-slider__switcher__SoaxS {
    display: none;
  }
}
/*!****************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[11].oneOf[12].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[11].oneOf[12].use[3]!./node_modules/swiper/swiper.css ***!
  \****************************************************************************************************************************************************************************************************************************************************************************/
/**
 * Swiper 11.2.10
 * Most modern mobile touch slider and framework with hardware accelerated transitions
 * https://swiperjs.com
 *
 * Copyright 2014-2025 Vladimir Kharlampidi
 *
 * Released under the MIT License
 *
 * Released on: June 28, 2025
 */

/* FONT_START */
@font-face {
  font-family: 'swiper-icons';
  src: url('data:application/font-woff;charset=utf-8;base64, d09GRgABAAAAAAZgABAAAAAADAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABGRlRNAAAGRAAAABoAAAAci6qHkUdERUYAAAWgAAAAIwAAACQAYABXR1BPUwAABhQAAAAuAAAANuAY7+xHU1VCAAAFxAAAAFAAAABm2fPczU9TLzIAAAHcAAAASgAAAGBP9V5RY21hcAAAAkQAAACIAAABYt6F0cBjdnQgAAACzAAAAAQAAAAEABEBRGdhc3AAAAWYAAAACAAAAAj//wADZ2x5ZgAAAywAAADMAAAD2MHtryVoZWFkAAABbAAAADAAAAA2E2+eoWhoZWEAAAGcAAAAHwAAACQC9gDzaG10eAAAAigAAAAZAAAArgJkABFsb2NhAAAC0AAAAFoAAABaFQAUGG1heHAAAAG8AAAAHwAAACAAcABAbmFtZQAAA/gAAAE5AAACXvFdBwlwb3N0AAAFNAAAAGIAAACE5s74hXjaY2BkYGAAYpf5Hu/j+W2+MnAzMYDAzaX6QjD6/4//Bxj5GA8AuRwMYGkAPywL13jaY2BkYGA88P8Agx4j+/8fQDYfA1AEBWgDAIB2BOoAeNpjYGRgYNBh4GdgYgABEMnIABJzYNADCQAACWgAsQB42mNgYfzCOIGBlYGB0YcxjYGBwR1Kf2WQZGhhYGBiYGVmgAFGBiQQkOaawtDAoMBQxXjg/wEGPcYDDA4wNUA2CCgwsAAAO4EL6gAAeNpj2M0gyAACqxgGNWBkZ2D4/wMA+xkDdgAAAHjaY2BgYGaAYBkGRgYQiAHyGMF8FgYHIM3DwMHABGQrMOgyWDLEM1T9/w8UBfEMgLzE////P/5//f/V/xv+r4eaAAeMbAxwIUYmIMHEgKYAYjUcsDAwsLKxc3BycfPw8jEQA/gZBASFhEVExcQlJKWkZWTl5BUUlZRVVNXUNTQZBgMAAMR+E+gAEQFEAAAAKgAqACoANAA+AEgAUgBcAGYAcAB6AIQAjgCYAKIArAC2AMAAygDUAN4A6ADyAPwBBgEQARoBJAEuATgBQgFMAVYBYAFqAXQBfgGIAZIBnAGmAbIBzgHsAAB42u2NMQ6CUAyGW568x9AneYYgm4MJbhKFaExIOAVX8ApewSt4Bic4AfeAid3VOBixDxfPYEza5O+Xfi04YADggiUIULCuEJK8VhO4bSvpdnktHI5QCYtdi2sl8ZnXaHlqUrNKzdKcT8cjlq+rwZSvIVczNiezsfnP/uznmfPFBNODM2K7MTQ45YEAZqGP81AmGGcF3iPqOop0r1SPTaTbVkfUe4HXj97wYE+yNwWYxwWu4v1ugWHgo3S1XdZEVqWM7ET0cfnLGxWfkgR42o2PvWrDMBSFj/IHLaF0zKjRgdiVMwScNRAoWUoH78Y2icB/yIY09An6AH2Bdu/UB+yxopYshQiEvnvu0dURgDt8QeC8PDw7Fpji3fEA4z/PEJ6YOB5hKh4dj3EvXhxPqH/SKUY3rJ7srZ4FZnh1PMAtPhwP6fl2PMJMPDgeQ4rY8YT6Gzao0eAEA409DuggmTnFnOcSCiEiLMgxCiTI6Cq5DZUd3Qmp10vO0LaLTd2cjN4fOumlc7lUYbSQcZFkutRG7g6JKZKy0RmdLY680CDnEJ+UMkpFFe1RN7nxdVpXrC4aTtnaurOnYercZg2YVmLN/d/gczfEimrE/fs/bOuq29Zmn8tloORaXgZgGa78yO9/cnXm2BpaGvq25Dv9S4E9+5SIc9PqupJKhYFSSl47+Qcr1mYNAAAAeNptw0cKwkAAAMDZJA8Q7OUJvkLsPfZ6zFVERPy8qHh2YER+3i/BP83vIBLLySsoKimrqKqpa2hp6+jq6RsYGhmbmJqZSy0sraxtbO3sHRydnEMU4uR6yx7JJXveP7WrDycAAAAAAAH//wACeNpjYGRgYOABYhkgZgJCZgZNBkYGLQZtIJsFLMYAAAw3ALgAeNolizEKgDAQBCchRbC2sFER0YD6qVQiBCv/H9ezGI6Z5XBAw8CBK/m5iQQVauVbXLnOrMZv2oLdKFa8Pjuru2hJzGabmOSLzNMzvutpB3N42mNgZGBg4GKQYzBhYMxJLMlj4GBgAYow/P/PAJJhLM6sSoWKfWCAAwDAjgbRAAB42mNgYGBkAIIbCZo5IPrmUn0hGA0AO8EFTQAA');
  font-weight: 400;
  font-style: normal;
}
/* FONT_END */
:root {
  --swiper-theme-color: #007aff;
  /*
  --swiper-preloader-color: var(--swiper-theme-color);
  --swiper-wrapper-transition-timing-function: initial;
  */
}
:host {
  position: relative;
  display: block;
  margin-left: auto;
  margin-right: auto;
  z-index: 1;
}
.swiper {
  margin-left: auto;
  margin-right: auto;
  position: relative;
  overflow: hidden;
  list-style: none;
  padding: 0;
  /* Fix of Webkit flickering */
  z-index: 1;
  display: block;
}
.swiper-vertical > .swiper-wrapper {
  flex-direction: column;
}
.swiper-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
  z-index: 1;
  display: flex;
  transition-property: transform;
  transition-timing-function: var(--swiper-wrapper-transition-timing-function, initial);
  box-sizing: content-box;
}
.swiper-android .swiper-slide,
.swiper-ios .swiper-slide,
.swiper-wrapper {
  transform: translate3d(0px, 0, 0);
}
.swiper-horizontal {
  touch-action: pan-y;
}
.swiper-vertical {
  touch-action: pan-x;
}
.swiper-slide {
  flex-shrink: 0;
  width: 100%;
  height: 100%;
  position: relative;
  transition-property: transform;
  display: block;
}
.swiper-slide-invisible-blank {
  visibility: hidden;
}
/* Auto Height */
.swiper-autoheight,
.swiper-autoheight .swiper-slide {
  height: auto;
}
.swiper-autoheight .swiper-wrapper {
  align-items: flex-start;
  transition-property: transform, height;
}
.swiper-backface-hidden .swiper-slide {
  transform: translateZ(0);
  -webkit-backface-visibility: hidden;
          backface-visibility: hidden;
}
/* 3D Effects */
.swiper-3d.swiper-css-mode .swiper-wrapper {
  perspective: 1200px;
}
.swiper-3d .swiper-wrapper {
  transform-style: preserve-3d;
}
.swiper-3d {
  perspective: 1200px;
}
.swiper-3d .swiper-slide,
.swiper-3d .swiper-cube-shadow {
  transform-style: preserve-3d;
}
/* CSS Mode */
.swiper-css-mode > .swiper-wrapper {
  overflow: auto;
  scrollbar-width: none;
  /* For Firefox */
  -ms-overflow-style: none;
  /* For Internet Explorer and Edge */
}
.swiper-css-mode > .swiper-wrapper::-webkit-scrollbar {
  display: none;
}
.swiper-css-mode > .swiper-wrapper > .swiper-slide {
  scroll-snap-align: start start;
}
.swiper-css-mode.swiper-horizontal > .swiper-wrapper {
  scroll-snap-type: x mandatory;
}
.swiper-css-mode.swiper-vertical > .swiper-wrapper {
  scroll-snap-type: y mandatory;
}
.swiper-css-mode.swiper-free-mode > .swiper-wrapper {
  scroll-snap-type: none;
}
.swiper-css-mode.swiper-free-mode > .swiper-wrapper > .swiper-slide {
  scroll-snap-align: none;
}
.swiper-css-mode.swiper-centered > .swiper-wrapper::before {
  content: '';
  flex-shrink: 0;
  order: 9999;
}
.swiper-css-mode.swiper-centered > .swiper-wrapper > .swiper-slide {
  scroll-snap-align: center center;
  scroll-snap-stop: always;
}
.swiper-css-mode.swiper-centered.swiper-horizontal > .swiper-wrapper > .swiper-slide:first-child {
  -webkit-margin-start: var(--swiper-centered-offset-before);
          margin-inline-start: var(--swiper-centered-offset-before);
}
.swiper-css-mode.swiper-centered.swiper-horizontal > .swiper-wrapper::before {
  height: 100%;
  min-height: 1px;
  width: var(--swiper-centered-offset-after);
}
.swiper-css-mode.swiper-centered.swiper-vertical > .swiper-wrapper > .swiper-slide:first-child {
  -webkit-margin-before: var(--swiper-centered-offset-before);
          margin-block-start: var(--swiper-centered-offset-before);
}
.swiper-css-mode.swiper-centered.swiper-vertical > .swiper-wrapper::before {
  width: 100%;
  min-width: 1px;
  height: var(--swiper-centered-offset-after);
}
/* Slide styles start */
/* 3D Shadows */
.swiper-3d .swiper-slide-shadow,
.swiper-3d .swiper-slide-shadow-left,
.swiper-3d .swiper-slide-shadow-right,
.swiper-3d .swiper-slide-shadow-top,
.swiper-3d .swiper-slide-shadow-bottom,
.swiper-3d .swiper-slide-shadow,
.swiper-3d .swiper-slide-shadow-left,
.swiper-3d .swiper-slide-shadow-right,
.swiper-3d .swiper-slide-shadow-top,
.swiper-3d .swiper-slide-shadow-bottom {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 10;
}
.swiper-3d .swiper-slide-shadow {
  background: rgba(0, 0, 0, 0.15);
}
.swiper-3d .swiper-slide-shadow-left {
  background-image: linear-gradient(to left, rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0));
}
.swiper-3d .swiper-slide-shadow-right {
  background-image: linear-gradient(to right, rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0));
}
.swiper-3d .swiper-slide-shadow-top {
  background-image: linear-gradient(to top, rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0));
}
.swiper-3d .swiper-slide-shadow-bottom {
  background-image: linear-gradient(to bottom, rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0));
}
.swiper-lazy-preloader {
  width: 42px;
  height: 42px;
  position: absolute;
  left: 50%;
  top: 50%;
  margin-left: -21px;
  margin-top: -21px;
  z-index: 10;
  transform-origin: 50%;
  box-sizing: border-box;
  border: 4px solid var(--swiper-preloader-color, var(--swiper-theme-color));
  border-radius: 50%;
  border-top-color: transparent;
}
.swiper:not(.swiper-watch-progress) .swiper-lazy-preloader,
.swiper-watch-progress .swiper-slide-visible .swiper-lazy-preloader {
  animation: swiper-preloader-spin 1s infinite linear;
}
.swiper-lazy-preloader-white {
  --swiper-preloader-color: #fff;
}
.swiper-lazy-preloader-black {
  --swiper-preloader-color: #000;
}
@keyframes swiper-preloader-spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
/* Slide styles end */

/*!****************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[11].oneOf[12].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[11].oneOf[12].use[3]!./node_modules/swiper/modules/navigation.css ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************/
:root {
  --swiper-navigation-size: 44px;
  /*
  --swiper-navigation-top-offset: 50%;
  --swiper-navigation-sides-offset: 10px;
  --swiper-navigation-color: var(--swiper-theme-color);
  */
}
.swiper-button-prev,
.swiper-button-next {
  position: absolute;
  top: var(--swiper-navigation-top-offset, 50%);
  width: calc(var(--swiper-navigation-size) / 44 * 27);
  height: var(--swiper-navigation-size);
  margin-top: calc(0px - (var(--swiper-navigation-size) / 2));
  z-index: 10;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--swiper-navigation-color, var(--swiper-theme-color));
}
.swiper-button-prev.swiper-button-disabled,
.swiper-button-next.swiper-button-disabled {
  opacity: 0.35;
  cursor: auto;
  pointer-events: none;
}
.swiper-button-prev.swiper-button-hidden,
.swiper-button-next.swiper-button-hidden {
  opacity: 0;
  cursor: auto;
  pointer-events: none;
}
.swiper-navigation-disabled .swiper-button-prev,
.swiper-navigation-disabled .swiper-button-next {
  display: none !important;
}
.swiper-button-prev svg,
.swiper-button-next svg {
  width: 100%;
  height: 100%;
  object-fit: contain;
  transform-origin: center;
}
.swiper-rtl .swiper-button-prev svg,
.swiper-rtl .swiper-button-next svg {
  transform: rotate(180deg);
}
.swiper-button-prev,
.swiper-rtl .swiper-button-next {
  left: var(--swiper-navigation-sides-offset, 10px);
  right: auto;
}
.swiper-button-next,
.swiper-rtl .swiper-button-prev {
  right: var(--swiper-navigation-sides-offset, 10px);
  left: auto;
}
.swiper-button-lock {
  display: none;
}
/* Navigation font start */
.swiper-button-prev:after,
.swiper-button-next:after {
  font-family: swiper-icons;
  font-size: var(--swiper-navigation-size);
  text-transform: none !important;
  letter-spacing: 0;
  font-feature-settings: ;
  font-variant: initial;
  line-height: 1;
}
.swiper-button-prev:after,
.swiper-rtl .swiper-button-next:after {
  content: 'prev';
}
.swiper-button-next,
.swiper-rtl .swiper-button-prev {
  right: var(--swiper-navigation-sides-offset, 10px);
  left: auto;
}
.swiper-button-next:after,
.swiper-rtl .swiper-button-prev:after {
  content: 'next';
}
/* Navigation font end */

/*!****************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[11].oneOf[12].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[11].oneOf[12].use[3]!./node_modules/swiper/modules/pagination.css ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************/
:root {
  /*
  --swiper-pagination-color: var(--swiper-theme-color);
  --swiper-pagination-left: auto;
  --swiper-pagination-right: 8px;
  --swiper-pagination-bottom: 8px;
  --swiper-pagination-top: auto;
  --swiper-pagination-fraction-color: inherit;
  --swiper-pagination-progressbar-bg-color: rgba(0,0,0,0.25);
  --swiper-pagination-progressbar-size: 4px;
  --swiper-pagination-bullet-size: 8px;
  --swiper-pagination-bullet-width: 8px;
  --swiper-pagination-bullet-height: 8px;
  --swiper-pagination-bullet-border-radius: 50%;
  --swiper-pagination-bullet-inactive-color: #000;
  --swiper-pagination-bullet-inactive-opacity: 0.2;
  --swiper-pagination-bullet-opacity: 1;
  --swiper-pagination-bullet-horizontal-gap: 4px;
  --swiper-pagination-bullet-vertical-gap: 6px;
  */
}
.swiper-pagination {
  position: absolute;
  text-align: center;
  transition: 300ms opacity;
  transform: translate3d(0, 0, 0);
  z-index: 10;
}
.swiper-pagination.swiper-pagination-hidden {
  opacity: 0;
}
.swiper-pagination-disabled > .swiper-pagination,
.swiper-pagination.swiper-pagination-disabled {
  display: none !important;
}
/* Common Styles */
.swiper-pagination-fraction,
.swiper-pagination-custom,
.swiper-horizontal > .swiper-pagination-bullets,
.swiper-pagination-bullets.swiper-pagination-horizontal {
  bottom: var(--swiper-pagination-bottom, 8px);
  top: var(--swiper-pagination-top, auto);
  left: 0;
  width: 100%;
}
/* Bullets */
.swiper-pagination-bullets-dynamic {
  overflow: hidden;
  font-size: 0;
}
.swiper-pagination-bullets-dynamic .swiper-pagination-bullet {
  transform: scale(0.33);
  position: relative;
}
.swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active {
  transform: scale(1);
}
.swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active-main {
  transform: scale(1);
}
.swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active-prev {
  transform: scale(0.66);
}
.swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active-prev-prev {
  transform: scale(0.33);
}
.swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active-next {
  transform: scale(0.66);
}
.swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active-next-next {
  transform: scale(0.33);
}
.swiper-pagination-bullet {
  width: var(--swiper-pagination-bullet-width, var(--swiper-pagination-bullet-size, 8px));
  height: var(--swiper-pagination-bullet-height, var(--swiper-pagination-bullet-size, 8px));
  display: inline-block;
  border-radius: var(--swiper-pagination-bullet-border-radius, 50%);
  background: var(--swiper-pagination-bullet-inactive-color, #000);
  opacity: var(--swiper-pagination-bullet-inactive-opacity, 0.2);
}
button.swiper-pagination-bullet {
  border: none;
  margin: 0;
  padding: 0;
  box-shadow: none;
  -webkit-appearance: none;
          -moz-appearance: none;
       appearance: none;
}
.swiper-pagination-clickable .swiper-pagination-bullet {
  cursor: pointer;
}
.swiper-pagination-bullet:only-child {
  display: none !important;
}
.swiper-pagination-bullet-active {
  opacity: var(--swiper-pagination-bullet-opacity, 1);
  background: var(--swiper-pagination-color, var(--swiper-theme-color));
}
.swiper-vertical > .swiper-pagination-bullets,
.swiper-pagination-vertical.swiper-pagination-bullets {
  right: var(--swiper-pagination-right, 8px);
  left: var(--swiper-pagination-left, auto);
  top: 50%;
  transform: translate3d(0px, -50%, 0);
}
.swiper-vertical > .swiper-pagination-bullets .swiper-pagination-bullet,
.swiper-pagination-vertical.swiper-pagination-bullets .swiper-pagination-bullet {
  margin: var(--swiper-pagination-bullet-vertical-gap, 6px) 0;
  display: block;
}
.swiper-vertical > .swiper-pagination-bullets.swiper-pagination-bullets-dynamic,
.swiper-pagination-vertical.swiper-pagination-bullets.swiper-pagination-bullets-dynamic {
  top: 50%;
  transform: translateY(-50%);
  width: 8px;
}
.swiper-vertical > .swiper-pagination-bullets.swiper-pagination-bullets-dynamic .swiper-pagination-bullet,
.swiper-pagination-vertical.swiper-pagination-bullets.swiper-pagination-bullets-dynamic .swiper-pagination-bullet {
  display: inline-block;
  transition: 200ms transform,
        200ms top;
}
.swiper-horizontal > .swiper-pagination-bullets .swiper-pagination-bullet,
.swiper-pagination-horizontal.swiper-pagination-bullets .swiper-pagination-bullet {
  margin: 0 var(--swiper-pagination-bullet-horizontal-gap, 4px);
}
.swiper-horizontal > .swiper-pagination-bullets.swiper-pagination-bullets-dynamic,
.swiper-pagination-horizontal.swiper-pagination-bullets.swiper-pagination-bullets-dynamic {
  left: 50%;
  transform: translateX(-50%);
  white-space: nowrap;
}
.swiper-horizontal > .swiper-pagination-bullets.swiper-pagination-bullets-dynamic .swiper-pagination-bullet,
.swiper-pagination-horizontal.swiper-pagination-bullets.swiper-pagination-bullets-dynamic .swiper-pagination-bullet {
  transition: 200ms transform,
        200ms left;
}
.swiper-horizontal.swiper-rtl > .swiper-pagination-bullets-dynamic .swiper-pagination-bullet {
  transition: 200ms transform,
    200ms right;
}
/* Fraction */
.swiper-pagination-fraction {
  color: var(--swiper-pagination-fraction-color, inherit);
}
/* Progress */
.swiper-pagination-progressbar {
  background: var(--swiper-pagination-progressbar-bg-color, rgba(0, 0, 0, 0.25));
  position: absolute;
}
.swiper-pagination-progressbar .swiper-pagination-progressbar-fill {
  background: var(--swiper-pagination-color, var(--swiper-theme-color));
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  transform: scale(0);
  transform-origin: left top;
}
.swiper-rtl .swiper-pagination-progressbar .swiper-pagination-progressbar-fill {
  transform-origin: right top;
}
.swiper-horizontal > .swiper-pagination-progressbar,
.swiper-pagination-progressbar.swiper-pagination-horizontal,
.swiper-vertical > .swiper-pagination-progressbar.swiper-pagination-progressbar-opposite,
.swiper-pagination-progressbar.swiper-pagination-vertical.swiper-pagination-progressbar-opposite {
  width: 100%;
  height: var(--swiper-pagination-progressbar-size, 4px);
  left: 0;
  top: 0;
}
.swiper-vertical > .swiper-pagination-progressbar,
.swiper-pagination-progressbar.swiper-pagination-vertical,
.swiper-horizontal > .swiper-pagination-progressbar.swiper-pagination-progressbar-opposite,
.swiper-pagination-progressbar.swiper-pagination-horizontal.swiper-pagination-progressbar-opposite {
  width: var(--swiper-pagination-progressbar-size, 4px);
  height: 100%;
  left: 0;
  top: 0;
}
.swiper-pagination-lock {
  display: none;
}

