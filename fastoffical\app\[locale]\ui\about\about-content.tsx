'use client'
import styles from './about.module.scss'
import Image from 'next/image'
import PageTabs from '../components/page-tabs'
import { CompanyTime, Contacts } from '../home/<USER>'
import CylanCertificates from '../components/certificates'
import { useRef, useState, useEffect, useCallback } from 'react'
import { Suspense } from 'react'
import { usePathname, useRouter } from 'next/navigation'
import { useI18n, I18nProviderClient, useCurrentLocale } from '@/locales/client'

export default function AboutContentLayout() {
  return (
    <I18nProviderClient locale={useCurrentLocale()}>
      <AboutContent />
    </I18nProviderClient>
  )
}

function AboutContent() {
  const t = useI18n()

  const [tabs, setTabs] = useState([
    {
      id: '0',
      text: t('aboutCylan'),
    },
    {
      id: '1',
      text: t('cylanPrides'),
    },
    {
      id: '2',
      text: t('contactUs'),
    },
  ])
  const [currentTab, setCurrentTab] = useState(tabs[0].id)
  const [trigger, setTrigger] = useState(currentTab)
  const contentRef = useRef<HTMLDivElement>(null)

  const handleTabChange = (tab: string) => {
    setTrigger(tab)
  }

  return (
    <div className={styles.about}>
      <div className={`${styles.about__image} hide-on-small`}>
        <Image
          src={'/about-banner.webp'}
          width={1920}
          height={900}
          alt=""
          style={{
            width: '100%',
            height: '100%',
            objectFit: 'fill',
            objectPosition: 'center',
          }}
          unoptimized
        ></Image>
      </div>
      <h6 id="about-cylan"></h6>
      <div className="hide-on-small">
        <PageTabs
          title={t('aboutUs')}
          iconSrc="/about-icon.svg"
          currentTab={currentTab}
          tabs={tabs}
          showBanner={false}
          background="transparent"
          onTabChange={(tab: string) => {
            handleTabChange(tab)
          }}
        />
      </div>
      <div className="hide-on-medium hide-on-large">
        <PageTabs
          title={t('aboutUs')}
          iconSrc="/about-icon.svg"
          currentTab={currentTab}
          tabs={tabs}
          bannerMobileSrc="/about-banner-mobile.jpg"
          background="rgb(214,218,211)"
          onTabChange={(tab: string) => {
            handleTabChange(tab)
          }}
        />
      </div>
      <Suspense>
        <Content
          contentRef={contentRef}
          tabs={tabs}
          setCurrentTab={setCurrentTab}
          trigger={trigger}
        ></Content>
      </Suspense>
    </div>
  )
}

function Content({
  tabs,
  setCurrentTab = () => { },
  contentRef,
  trigger,
}: {
  tabs: Array<{ id: string; text: string }>
  setCurrentTab: Function
  contentRef: React.RefObject<HTMLDivElement>
  trigger: string
}) {
  const aboutCylanRef = useRef<HTMLDivElement>(null)
  const pridesRef = useRef<HTMLDivElement>(null)
  const contactRef = useRef<HTMLDivElement>(null)
  const isInitial = useRef<boolean>(true)
  const router = useRouter()
  const pathname = usePathname()

  const handleTabChange = useCallback(
    (tab: string) => {
      let jump: string
      if (tab === tabs[0].id) jump = 'about-cylan'
      else if (tab === tabs[1].id) jump = 'prides'
      else jump = 'contacts'

      setCurrentTab(tab)
      router.replace(`${pathname}/#${jump}`)
    },
    [tabs, setCurrentTab, router, pathname]
  )

  useEffect(() => {
    const handleScroll = () => {
      const scrollHeight =
        window.pageYOffset ||
        document.documentElement.scrollTop ||
        document.body.scrollTop
      const top = aboutCylanRef.current?.getBoundingClientRect().top
      if (top && scrollHeight < top) setCurrentTab(tabs[0].id)
    }

    window.addEventListener('scroll', handleScroll)
    return () => {
      window.removeEventListener('scroll', handleScroll)
    }
  }, [handleTabChange, tabs, setCurrentTab])

  useEffect(() => {
    if (isInitial.current) {
      isInitial.current = false
    } else {
      handleTabChange(trigger)
    }
  }, [trigger, handleTabChange])

  return (
    <I18nProviderClient locale={useCurrentLocale()}>
      <div ref={contentRef} className={styles.about__content}>
        <div ref={aboutCylanRef}>
          <Main />
        </div>

        <Career />
        <div ref={pridesRef}>
          <Prides />
        </div>
        <h6 id="contacts"></h6>

        <div
          ref={contactRef}
          className={`${styles.about__content__contacts} hide-on-small`}
        >
          <Contacts isAboutPage />
        </div>
      </div>
      <div
        className={`${styles.about__content__contacts} hide-on-medium hide-on-large `}
      >
        <Contacts isAboutPage />
      </div>
    </I18nProviderClient>
  )
}

function Main() {
  const t = useI18n()

  const overviewList = [
    t('companySetTime'),
    t('companyMainLocation'),
    t('companyCoreTech'),
    t('companyService'),
  ]

  const coreProductList = [
    t('companyCoreProductText1'),
    t('companyCoreProductText2'),
    t('companyCoreProductText3'),
    t('companyCoreProductText4'),
    t('companyCoreProductText5'),
    t('companyCoreProductText6'),
    t('companyCoreProductText7'),
    t('companyCoreProductText8'),
    t('companyCoreProductText9'),
    t('companyCoreProductText10'),
  ]

  const globalList = [
    t('companyGlobalText1'),
    t('companyGlobalText2'),
    t('companyGlobalText3'),
    t('companyGlobalText4'),
    t('companyGlobalText5'),
  ]

  return (
    <div className={styles.about__content__main}>
      {/* 顶部标题区：公司名 + 标语，占满整块卡片的上边区域 */}
      <div className={styles.about__content__main__header}>
        <h2>{t('aboutCylanTitle')}</h2>
        <span className={styles.about__content__main__header__dash}></span>
        <p className={styles.about__content__main__header__subtitle}>全球智能看护解决方案领导者</p>
      </div>

      {/* 内容栅格区：两列卡片，上面两块，下面一块通栏 */}
      <div className={styles.about__content__main__grid}>
        <section className={styles.about__content__main__panel}>
          <h5 className={`${styles.about__content__main__panel__title} ${styles['about__content__main__panel__title--title1']}`}>{t('companyReview')}</h5>

          <div className={styles.about__content__main__panel__line}></div>

          <ul className={styles.about__content__main__panel__list}>
            {overviewList.map((text, i) => (
              <li key={i}>{text}</li>
            ))}
          </ul>
        </section>

        <section className={`${styles.about__content__main__panel} ${styles['about__content__main__panel--emphasis']}`}>
          <h5 className={`${styles.about__content__main__panel__title} ${styles['about__content__main__panel__title--title2']}`}>{t('companyCoreProduct')}</h5>

          <div className={styles.about__content__main__panel__line}></div>

          <ul className={`${styles.about__content__main__panel__list} ${styles['about__content__main__panel__list--twoCols']}`}>
            {coreProductList.map((text, i) => (
              <li key={i}>{text}</li>
            ))}
          </ul>
        </section>

        <section className={`${styles.about__content__main__panel} ${styles['about__content__main__panel--span2']}`}>
          <h5 className={`${styles.about__content__main__panel__title} ${styles['about__content__main__panel__title--title3']}`}>{t('companyGlobal')}</h5>

          <div className={styles.about__content__main__panel__line}></div>

          <ul className={styles.about__content__main__panel__list}>
            {globalList.map((text, i) => (
              <li key={i}>{text}</li>
            ))}
          </ul>
        </section>
      </div>
    </div>
  )
}

function Career() {
  const t = useI18n()

  enum LineType {
    first,
    normal,
    last,
  }

  const items = [
    {
      title: '参加2024年香港春季电子展',
      time: '2024年4月',
      text: '2024年香港春季电子产品展及香港国际资讯科技博览会推出二代触摸屏视频通话智能摄像机参展',
      imageSrc: '/hotspot-image-1.png',
    },
    {
      title: '参加2024年香港春季电子展',
      time: '2024年4月',
      text: '2024年香港春季电子产品展及香港国际资讯科技博览会推出二代触摸屏视频通话智能摄像机参展',
      imageSrc: '/hotspot-image-1.png',
    },
    {
      title: '参加2024年香港春季电子展',
      time: '2024年4月',
      text: '2024年香港春季电子产品展及香港国际资讯科技博览会推出二代触摸屏视频通话智能摄像机参展',
      imageSrc: '/hotspot-image-1.png',
    },
    {
      title: '参加2024年香港春季电子展',
      time: '2024年4月',
      text: '2024年香港春季电子产品展及香港国际资讯科技博览会推出二代触摸屏视频通话智能摄像机参展',
      imageSrc: '/hotspot-image-1.png',
    },
    {
      title: '参加2024年香港春季电子展',
      time: '2024年4月',
      text: '2024年香港春季电子产品展及香港国际资讯科技博览会推出二代触摸屏视频通话智能摄像机参展',
      imageSrc: '/hotspot-image-1.png',
    },
  ]

  const TimeLineItem = ({
    linetype = LineType.first,
    isReverse = false,
    title,
    time,
    text,
    imageSrc,
  }: {
    linetype: LineType
    isReverse: boolean
    title: string
    time: string
    text: string
    imageSrc: string
  }) => {
    return (
      <>
        <div
          className={`${styles.about__content__career__timeline__item} ${isReverse
            ? styles['about__content__career__timeline__item--reverse']
            : ''
            }`}
        >
          <div className="hide-on-small">
            <div>
              <div>
                <Image src={imageSrc} width={240} height={160} alt=""></Image>
              </div>
              <h6>{title}</h6>
            </div>
          </div>
          <div>
            <div></div>
            <div>
              <div></div>
            </div>
            <div></div>
          </div>
          <div>
            <h4 className="hide-on-medium hide-on-large">{time}</h4>
            <div className="hide-on-medium hide-on-large">
              <Image
                src={imageSrc}
                width={285}
                height={190}
                alt=""
                style={{ width: '100%', height: '100%' }}
              ></Image>
            </div>
            <h4 className="hide-on-small">{time}</h4>
            <h6 className="hide-on-medium hide-on-large">{title}</h6>
            <p>{text}</p>
          </div>
        </div>
      </>
    )
  }

  return (
    <div className={styles.about__content__career}>
      <h1>{t('career')}</h1>
      <p>{t('careerDescription')}</p>
      <div className={styles.about__content__career__companytime}>
        <CompanyTime isAboutPage />
      </div>
      <h6 id="prides"></h6>

      {/* <div className={styles.about__content__career__timeline}>
        {items.map((item, index) => (
          <TimeLineItem
            key={index}
            isReverse={index % 2 === 1}
            {...item}
            linetype={
              index === 0
                ? LineType.first
                : index === items.length - 1
                ? LineType.last
                : LineType.normal
            }
          ></TimeLineItem>
        ))}
      </div> */}
    </div>
  )
}

function Prides() {
  const t = useI18n()

  enum ImageSize {
    small,
    normal,
    large,
  }
  const [showCoverList, setShowCoverList] = useState([
    {
      text: t('pride1'),
      show: false,
    },
    {
      text: t('pride2'),
      show: false,
    },
    {
      text: t('pride3'),
      show: false,
    },
    {
      text: t('pride4'),
      show: false,
    },
    {
      text: t('pride5'),
      show: false,
    },
    {
      text: t('pride6'),
      show: false,
    },
    {
      text: t('pride7'),
      show: false,
    },
    {
      text: t('pride8'),
      show: false,
    },
    {
      text: t('pride9'),
      show: false,
    },
  ])

  const ImageItem = ({
    src,
    size = ImageSize.normal,
    index,
  }: {
    src: string
    size?: ImageSize
    index: number
  }) => {
    return (
      <div
        onClick={() => {
          setShowCoverList(
            showCoverList.map((item, idx) => {
              if (idx === index) item.show = !item.show
              return item
            })
          )
        }}
        className={`${styles.about__content__prides__list__item} ${size === ImageSize.large
          ? styles['about__content__prides__list__item--large']
          : size === ImageSize.normal
            ? styles['about__content__prides__list__item--normal']
            : styles['about__content__prides__list__item--small']
          }`}
      >
        <Image
          src={src}
          width={size === ImageSize.large ? 478 : 231}
          height={size === ImageSize.small ? 154 : 326}
          alt=""
          style={{ height: '100%', width: '100%', objectFit: 'fill' }}
          unoptimized
        ></Image>
        {showCoverList[index].show && (
          <div className={styles.about__content__prides__list__item__cover}>
            {showCoverList[index].text}
          </div>
        )}
      </div>
    )
  }

  return (
    <>
      <div className={`${styles.about__content__prides}`}>
        <h1>{t('cylanPrides')}</h1>
        <div className="hide-on-small">
          <div className={styles.about__content__prides__list}>
            <ImageItem src="/pride-image-1.jpg" index={0} />
            <ImageItem
              src="/pride-image-2.jpg"
              size={ImageSize.large}
              index={1}
            />
            <ImageItem src="/pride-image-3.jpg" index={2} />
            <ImageItem src="/pride-image-4.jpg" index={3} />
          </div>
          <div className={styles.about__content__prides__list}>
            <ImageItem
              src="/pride-image-5.jpg"
              size={ImageSize.small}
              index={4}
            />
            <ImageItem
              src="/pride-image-6.jpg"
              size={ImageSize.small}
              index={5}
            />
            <ImageItem
              src="/pride-image-7.jpg"
              size={ImageSize.small}
              index={6}
            />
            <ImageItem
              src="/pride-image-8.jpg"
              size={ImageSize.small}
              index={7}
            />
            <ImageItem
              src="/pride-image-9.jpg"
              size={ImageSize.small}
              index={8}
            />
          </div>
        </div>

        <CylanCertificates />
      </div>
    </>
  )
}
