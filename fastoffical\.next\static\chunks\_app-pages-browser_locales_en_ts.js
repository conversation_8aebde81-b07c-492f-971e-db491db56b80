"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_locales_en_ts"],{

/***/ "(app-pages-browser)/./locales/en.ts":
/*!***********************!*\
  !*** ./locales/en.ts ***!
  \***********************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = ({\n    home: \"Home\",\n    productCenter: \"Product\",\n    prodoctVideos: \"Video\",\n    support: \"Service\",\n    aboutUs: \"About Us\",\n    productCamera: \"Camera\",\n    downloadClient: \"Download\",\n    chinese: \"Chinese\",\n    english: \"English\",\n    seeMore: \"More\",\n    year: \"Year\",\n    companiyTime: \"Company Establishment Date\",\n    deviceSells: \"Sales\",\n    activeUsers: \"Active Users\",\n    contactUs: \"Contact Us\",\n    pride1: \"Shenzhen Global Innovation and Entrepreneurship Exchange Excellent Innovation Product Award\",\n    pride2: \"European Union Intellectual Property Office Registration Certificate\",\n    pride3: \"Design Patent Certificate\",\n    pride4: \"RoHS\",\n    pride5: \"National High-Tech Enterprise Certificate\",\n    pride6: \"Member Unit of China Smart Home Industry Alliance\",\n    pride7: \"Intel Membership\",\n    pride8: \"Shenzhen Internet of Things Association Member Unit\",\n    pride9: \"China Smart City Excellent Product Award\",\n    cylanAddress: \"211, 2nd Floor, Folk Culture Industrial Park, Qunli 2nd Road, District 72, Xingdong Community, Bao'an District, Shenzhen City\",\n    aboutCylan: \"About Cylan\",\n    cylanPrides: \"Development History\",\n    followUs: \"Follow Us\",\n    imcamGongzhonghao: \"Imcam Official Wechat Account\",\n    downloadQRcode: \"Download QR Code\",\n    copyrightText: \"All rights reserved \\xa9 2025 Shenzhen Cylan Technology Co., Ltd.\",\n    copyrightLink: \" Guangdong ICP No. ********\",\n    all: \"All\",\n    imcamApp: \"Cylan application support\",\n    imcamAppTip: \"Enjoy smart life anytime, anywhere\",\n    miniProgram: \"WeChat Mini Program\",\n    download: \"Download\",\n    goToAppstore: \"Go to App Store\",\n    aboutCylanDescription: 'Shenzhen Cylan Technology Co., Ltd., possesses industry-leading capabilities in smart terminal hardware development and independently owns the smart home brand \"Im Cam\" With the vision of using technology to make home life more dreamful, it consistently practices the mission of making home life smarter and more convenient. Adhering to the core principles of quality innovation and prioritizing user value, it is dedicated to providing users with intelligent, comfortable, convenient, safe, and energy-saving smart home hardware devices.',\n    aboutCylanQuotesTitle1: \"Vision\",\n    aboutCylanQuotesTitle2: \"Mission\",\n    aboutCylanQuotesTitle3: \"Values\",\n    aboutCylanQuotesText1: \"Technology makes home more dreamful!\",\n    aboutCylanQuotesText2: \"Make home life smarter and more convenient!\",\n    aboutCylanQuotesText3: \"Intelligent, comfortable, convenient, safe, energy-saving.\",\n    career: \"Development History\",\n    careerDescription: \"Cylan was established in 2005 and has always been committed to becoming an innovator in the smart home industry since its inception. Cylan's development history is full of challenges and opportunities, but we have always adhered to the principles of innovation, quality, and customer priority. Currently, our two-way video call smart cameras have an annual sales volume of over 300,000 units, and the HomeGuard APP has accumulated more than 450,000 active users.\",\n    page404: \"404 - Page Not Found\",\n    page404Description: \"Sorry, the page you are trying to access seems to be non-existent. Please try the following:\",\n    page404Tip1: \"1. Check if the URL is correct.\",\n    page404Tip2: \"2. Return to the homepage or go back to the previous page.\",\n    backToHome: \"Back to Homepage\",\n    backPage: \"Back to Previous Page\",\n    backToTop: \"Back To Top\",\n    help: \"FAQ\",\n    detail: \"Detail\",\n    time: \"Time\",\n    prevArticle: \"Previous\",\n    nextArticle: \"Next\",\n    hot: \"Hot \",\n    imcam: \"Im Cam\",\n    androidApp: \"Android App\",\n    iosApp: \"iOS App\",\n    dangdang: \"铛铛看家\",\n    productTranslator: \"Translation Products\",\n    aboutCylanTitle: \"Cylan Tech – Global Leader in Intelligent Care Solutions\",\n    companyReview: \"​Company Profile\",\n    companySetTime: \"Founded: \",\n    companySetTimeText: \"2005\",\n    companyMainLocation: \"HQ: \",\n    companyMainLocationText: \"Shenzhen, China\",\n    companyCoreTech: \"Core Tech: \",\n    companyCoreTechText: \"AIoT \\xd7 AI Language Model Convergence\",\n    companyService: \"Users: \",\n    companyServiceText: \"Household & Enterprise Clients\",\n    companyCoreProduct: \"​Core Products\",\n    companyCoreProductText1: \"Smart Dual-Video Camera\",\n    companyCoreProductText2: \"✓ 1080P HD Calling + IR Night Vision\",\n    companyCoreProductText3: \"✓ AI Behavior Analysis (Scene-Specific Alerts, Intrusion Detection)\",\n    companyCoreProductText4: \"✓ Enterprise Encryption (3DES/AES)\",\n    companyCoreProductText5: \"✓ Cross-Platform (iOS/Android/Web)\",\n    companyCoreProductText6: \"Desktop Dual-Screen Translator\",\n    companyCoreProductText7: '✓ Dual 10\" HD LCDs',\n    companyCoreProductText8: \"✓ Dual Noise-Reduction MIC Arrays\",\n    companyCoreProductText9: \"✓ Real-Time Multilingual Translation\",\n    companyCoreProductText10: \"✓ Cross-Platform (iOS/Android/Web)\",\n    companyGlobal: \"Global Achievements\",\n    companyGlobalText1: \"• 50+ Countries (Focus: EU/NA, SEA, Middle East)\",\n    companyGlobalText2: \"• 800K+ Users Served\",\n    companyGlobalText3: \"• Key Scenarios: Home Care & Language Translation\",\n    companyGlobalText4: \"Home: Elderly/Infant Monitoring\",\n    companyGlobalText5: \"Enterprise: Smart Hotels, International Trade, Tourism, Airports\",\n    imMate: \"Im Mate\"\n});\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./locales/en.ts\n"));

/***/ })

}]);