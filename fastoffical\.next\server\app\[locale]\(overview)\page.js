/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/[locale]/(overview)/page";
exports.ids = ["app/[locale]/(overview)/page"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F%5Blocale%5D%2F(overview)%2Fpage&page=%2F%5Blocale%5D%2F(overview)%2Fpage&appPaths=%2F%5Blocale%5D%2F(overview)%2Fpage&pagePath=private-next-app-dir%2F%5Blocale%5D%2F(overview)%2Fpage.tsx&appDir=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F%5Blocale%5D%2F(overview)%2Fpage&page=%2F%5Blocale%5D%2F(overview)%2Fpage&appPaths=%2F%5Blocale%5D%2F(overview)%2Fpage&pagePath=private-next-app-dir%2F%5Blocale%5D%2F(overview)%2Fpage.tsx&appDir=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?9d97\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        '[locale]',\n        {\n        children: [\n        '(overview)',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/[locale]/(overview)/page.tsx */ \"(rsc)/./app/[locale]/(overview)/page.tsx\")), \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\(overview)\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/[locale]/layout.tsx */ \"(rsc)/./app/[locale]/layout.tsx\")), \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/[locale]/not-found.tsx */ \"(rsc)/./app/[locale]/not-found.tsx\")), \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\not-found.tsx\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\")), \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/not-found.tsx */ \"(rsc)/./app/not-found.tsx\")), \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\not-found.tsx\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\(overview)\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/[locale]/(overview)/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/[locale]/(overview)/page\",\n        pathname: \"/[locale]\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F%5Blocale%5D%2F(overview)%2Fpage&page=%2F%5Blocale%5D%2F(overview)%2Fpage&appPaths=%2F%5Blocale%5D%2F(overview)%2Fpage&pagePath=private-next-app-dir%2F%5Blocale%5D%2F(overview)%2Fpage.tsx&appDir=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Capp%5C%5Blocale%5D%5Cpage.module.scss&modules=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Cimage-component.js&modules=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Clink.js&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Capp%5C%5Blocale%5D%5Cpage.module.scss&modules=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Cimage-component.js&modules=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Clink.js&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/image-component.js */ \"(ssr)/./node_modules/next/dist/client/image-component.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/link.js */ \"(ssr)/./node_modules/next/dist/client/link.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9RCUzQSU1Qy0tLUFQcm9qZWN0cy0tLSU1Q2ltY2FtLW9mZmljaWFsc2l0ZSU1Q2Zhc3RvZmZpY2FsJTVDYXBwJTVDJTVCbG9jYWxlJTVEJTVDcGFnZS5tb2R1bGUuc2NzcyZtb2R1bGVzPUQlM0ElNUMtLS1BUHJvamVjdHMtLS0lNUNpbWNhbS1vZmZpY2lhbHNpdGUlNUNmYXN0b2ZmaWNhbCU1Q25vZGVfbW9kdWxlcyU1Q25leHQlNUNkaXN0JTVDY2xpZW50JTVDaW1hZ2UtY29tcG9uZW50LmpzJm1vZHVsZXM9RCUzQSU1Qy0tLUFQcm9qZWN0cy0tLSU1Q2ltY2FtLW9mZmljaWFsc2l0ZSU1Q2Zhc3RvZmZpY2FsJTVDbm9kZV9tb2R1bGVzJTVDbmV4dCU1Q2Rpc3QlNUNjbGllbnQlNUNsaW5rLmpzJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxzTkFBK0k7QUFDL0kiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9pbWNhbV9vZmZpY2lhbF9zaXRlLz84OGJjIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxcLS0tQVByb2plY3RzLS0tXFxcXGltY2FtLW9mZmljaWFsc2l0ZVxcXFxmYXN0b2ZmaWNhbFxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxpbWFnZS1jb21wb25lbnQuanNcIik7XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXC0tLUFQcm9qZWN0cy0tLVxcXFxpbWNhbS1vZmZpY2lhbHNpdGVcXFxcZmFzdG9mZmljYWxcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcbGluay5qc1wiKSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Capp%5C%5Blocale%5D%5Cpage.module.scss&modules=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Cimage-component.js&modules=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Clink.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Capp%5C%5Blocale%5D%5Cui%5Ccomponents%5Cback-to-top.tsx&modules=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Capp%5C%5Blocale%5D%5Cui%5Chome%5Cfooter.tsx&modules=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Capp%5C%5Blocale%5D%5Cui%5Chome%5Cnav.tsx&modules=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22app%5C%5C%5Blocale%5D%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Capp%5Cglobals.scss&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Capp%5C%5Blocale%5D%5Cui%5Ccomponents%5Cback-to-top.tsx&modules=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Capp%5C%5Blocale%5D%5Cui%5Chome%5Cfooter.tsx&modules=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Capp%5C%5Blocale%5D%5Cui%5Chome%5Cnav.tsx&modules=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22app%5C%5C%5Blocale%5D%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Capp%5Cglobals.scss&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/[locale]/ui/components/back-to-top.tsx */ \"(ssr)/./app/[locale]/ui/components/back-to-top.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/[locale]/ui/home/<USER>/ \"(ssr)/./app/[locale]/ui/home/<USER>"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/[locale]/ui/home/<USER>/ \"(ssr)/./app/[locale]/ui/home/<USER>"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9RCUzQSU1Qy0tLUFQcm9qZWN0cy0tLSU1Q2ltY2FtLW9mZmljaWFsc2l0ZSU1Q2Zhc3RvZmZpY2FsJTVDYXBwJTVDJTVCbG9jYWxlJTVEJTVDdWklNUNjb21wb25lbnRzJTVDYmFjay10by10b3AudHN4Jm1vZHVsZXM9RCUzQSU1Qy0tLUFQcm9qZWN0cy0tLSU1Q2ltY2FtLW9mZmljaWFsc2l0ZSU1Q2Zhc3RvZmZpY2FsJTVDYXBwJTVDJTVCbG9jYWxlJTVEJTVDdWklNUNob21lJTVDZm9vdGVyLnRzeCZtb2R1bGVzPUQlM0ElNUMtLS1BUHJvamVjdHMtLS0lNUNpbWNhbS1vZmZpY2lhbHNpdGUlNUNmYXN0b2ZmaWNhbCU1Q2FwcCU1QyU1QmxvY2FsZSU1RCU1Q3VpJTVDaG9tZSU1Q25hdi50c3gmbW9kdWxlcz1EJTNBJTVDLS0tQVByb2plY3RzLS0tJTVDaW1jYW0tb2ZmaWNpYWxzaXRlJTVDZmFzdG9mZmljYWwlNUNub2RlX21vZHVsZXMlNUNuZXh0JTVDZm9udCU1Q2dvb2dsZSU1Q3RhcmdldC5jc3MlM0YlN0IlMjJwYXRoJTIyJTNBJTIyYXBwJTVDJTVDJTVCbG9jYWxlJTVEJTVDJTVDbGF5b3V0LnRzeCUyMiUyQyUyMmltcG9ydCUyMiUzQSUyMkludGVyJTIyJTJDJTIyYXJndW1lbnRzJTIyJTNBJTVCJTdCJTIyc3Vic2V0cyUyMiUzQSU1QiUyMmxhdGluJTIyJTVEJTdEJTVEJTJDJTIydmFyaWFibGVOYW1lJTIyJTNBJTIyaW50ZXIlMjIlN0QmbW9kdWxlcz1EJTNBJTVDLS0tQVByb2plY3RzLS0tJTVDaW1jYW0tb2ZmaWNpYWxzaXRlJTVDZmFzdG9mZmljYWwlNUNhcHAlNUNnbG9iYWxzLnNjc3Mmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLG9NQUF5STtBQUN6SSw4S0FBOEg7QUFDOUgiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9pbWNhbV9vZmZpY2lhbF9zaXRlLz9jOTMxIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxcLS0tQVByb2plY3RzLS0tXFxcXGltY2FtLW9mZmljaWFsc2l0ZVxcXFxmYXN0b2ZmaWNhbFxcXFxhcHBcXFxcW2xvY2FsZV1cXFxcdWlcXFxcY29tcG9uZW50c1xcXFxiYWNrLXRvLXRvcC50c3hcIik7XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXC0tLUFQcm9qZWN0cy0tLVxcXFxpbWNhbS1vZmZpY2lhbHNpdGVcXFxcZmFzdG9mZmljYWxcXFxcYXBwXFxcXFtsb2NhbGVdXFxcXHVpXFxcXGhvbWVcXFxcZm9vdGVyLnRzeFwiKTtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxcLS0tQVByb2plY3RzLS0tXFxcXGltY2FtLW9mZmljaWFsc2l0ZVxcXFxmYXN0b2ZmaWNhbFxcXFxhcHBcXFxcW2xvY2FsZV1cXFxcdWlcXFxcaG9tZVxcXFxuYXYudHN4XCIpIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Capp%5C%5Blocale%5D%5Cui%5Ccomponents%5Cback-to-top.tsx&modules=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Capp%5C%5Blocale%5D%5Cui%5Chome%5Cfooter.tsx&modules=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Capp%5C%5Blocale%5D%5Cui%5Chome%5Cnav.tsx&modules=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22app%5C%5C%5Blocale%5D%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Capp%5Cglobals.scss&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Capp%5C%5Blocale%5D%5Cui%5Chome%5Cabout.tsx&modules=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Capp%5C%5Blocale%5D%5Cui%5Chome%5Cbanner-slider.tsx&modules=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Capp%5C%5Blocale%5D%5Cui%5Chome%5Chome.module.scss&modules=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Capp%5C%5Blocale%5D%5Cui%5Ccomponents%5Ccomponents.scss&modules=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Cimage-component.js&modules=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Clink.js&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Capp%5C%5Blocale%5D%5Cui%5Chome%5Cabout.tsx&modules=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Capp%5C%5Blocale%5D%5Cui%5Chome%5Cbanner-slider.tsx&modules=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Capp%5C%5Blocale%5D%5Cui%5Chome%5Chome.module.scss&modules=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Capp%5C%5Blocale%5D%5Cui%5Ccomponents%5Ccomponents.scss&modules=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Cimage-component.js&modules=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Clink.js&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/[locale]/ui/home/<USER>/ \"(ssr)/./app/[locale]/ui/home/<USER>"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/[locale]/ui/home/<USER>/ \"(ssr)/./app/[locale]/ui/home/<USER>"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/image-component.js */ \"(ssr)/./node_modules/next/dist/client/image-component.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/link.js */ \"(ssr)/./node_modules/next/dist/client/link.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Capp%5C%5Blocale%5D%5Cui%5Chome%5Cabout.tsx&modules=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Capp%5C%5Blocale%5D%5Cui%5Chome%5Cbanner-slider.tsx&modules=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Capp%5C%5Blocale%5D%5Cui%5Chome%5Chome.module.scss&modules=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Capp%5C%5Blocale%5D%5Cui%5Ccomponents%5Ccomponents.scss&modules=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Cimage-component.js&modules=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Clink.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Capp%5Cnot-found.tsx&server=true!":
/*!**********************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Capp%5Cnot-found.tsx&server=true! ***!
  \**********************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/not-found.tsx */ \"(ssr)/./app/not-found.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9RCUzQSU1Qy0tLUFQcm9qZWN0cy0tLSU1Q2ltY2FtLW9mZmljaWFsc2l0ZSU1Q2Zhc3RvZmZpY2FsJTVDYXBwJTVDbm90LWZvdW5kLnRzeCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9pbWNhbV9vZmZpY2lhbF9zaXRlLz9mZGMwIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxcLS0tQVByb2plY3RzLS0tXFxcXGltY2FtLW9mZmljaWFsc2l0ZVxcXFxmYXN0b2ZmaWNhbFxcXFxhcHBcXFxcbm90LWZvdW5kLnRzeFwiKSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Capp%5Cnot-found.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./app/[locale]/ui/components/back-to-top.tsx":
/*!****************************************************!*\
  !*** ./app/[locale]/ui/components/back-to-top.tsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ BackToTopLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_scss__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./components.scss */ \"(ssr)/./app/[locale]/ui/components/components.scss\");\n/* harmony import */ var _locales_client__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/locales/client */ \"(ssr)/./locales/client.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction BackToTopLayout() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_locales_client__WEBPACK_IMPORTED_MODULE_4__.I18nProviderClient, {\n        locale: (0,_locales_client__WEBPACK_IMPORTED_MODULE_4__.useCurrentLocale)(),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(BackToTop, {}, void 0, false, {\n            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\back-to-top.tsx\",\n            lineNumber: 10,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\back-to-top.tsx\",\n        lineNumber: 9,\n        columnNumber: 5\n    }, this);\n}\nfunction BackToTop() {\n    const t = (0,_locales_client__WEBPACK_IMPORTED_MODULE_4__.useI18n)();\n    const locale = (0,_locales_client__WEBPACK_IMPORTED_MODULE_4__.useCurrentLocale)();\n    const [showBackTop, setShowBackTop] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    let scrollTimer = null;\n    let isScrolling = false;\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        const handleScroll = ()=>{\n            const scrollHeight = window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop;\n            setShowBackTop(scrollHeight > window.innerHeight);\n        };\n        window.addEventListener(\"scroll\", handleScroll);\n        return ()=>{\n            window.removeEventListener(\"scroll\", handleScroll);\n        };\n    }, []);\n    const scrollToTop = ()=>{\n        if (isScrolling) return; // 如果正在滚动,则不执行操作\n        isScrolling = true // 设置滚动标记为 true\n        ;\n        if (scrollTimer) {\n            clearTimeout(scrollTimer);\n        }\n        scrollTimer = setTimeout(()=>{\n            window.scrollTo({\n                top: 0,\n                behavior: \"smooth\"\n            });\n            isScrolling = false // 滚动结束,将标记设置为 false\n            ;\n        }, 100);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        className: `back-top ${showBackTop ? \"\" : \"back-top--hide\"}`,\n        onClick: scrollToTop,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                src: \"/back-top.svg\",\n                width: 26,\n                height: 26,\n                alt: \"Top\"\n            }, void 0, false, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\back-to-top.tsx\",\n                lineNumber: 56,\n                columnNumber: 7\n            }, this),\n            locale === \"zh\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"hide-on-small\",\n                children: t(\"backToTop\")\n            }, void 0, false, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\back-to-top.tsx\",\n                lineNumber: 58,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\back-to-top.tsx\",\n        lineNumber: 52,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/[locale]/ui/components/back-to-top.tsx\n");

/***/ }),

/***/ "(ssr)/./app/[locale]/ui/components/certificates.tsx":
/*!*****************************************************!*\
  !*** ./app/[locale]/ui/components/certificates.tsx ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CylanCertificatesLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _locales_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/locales/client */ \"(ssr)/./locales/client.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction CylanCertificatesLayout() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_locales_client__WEBPACK_IMPORTED_MODULE_2__.I18nProviderClient, {\n        locale: (0,_locales_client__WEBPACK_IMPORTED_MODULE_2__.useCurrentLocale)(),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CylanCertificates, {}, void 0, false, {\n            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\certificates.tsx\",\n            lineNumber: 8,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\certificates.tsx\",\n        lineNumber: 7,\n        columnNumber: 5\n    }, this);\n}\nfunction CylanCertificates() {\n    const t = (0,_locales_client__WEBPACK_IMPORTED_MODULE_2__.useI18n)();\n    const certificates = [\n        {\n            imageSrc: \"/pride-image-1.jpg\",\n            title: t(\"pride1\"),\n            imageWidth: 120\n        },\n        {\n            imageSrc: \"/pride-image-2.jpg\",\n            title: t(\"pride2\"),\n            imageWidth: 242\n        },\n        {\n            imageSrc: \"/pride-image-3.jpg\",\n            title: t(\"pride3\"),\n            imageWidth: 120\n        },\n        {\n            imageSrc: \"/pride-image-4.jpg\",\n            title: t(\"pride4\"),\n            imageWidth: 120\n        },\n        {\n            imageSrc: \"/pride-image-5.jpg\",\n            title: t(\"pride5\"),\n            imageWidth: 240\n        },\n        {\n            imageSrc: \"/pride-image-6.jpg\",\n            title: t(\"pride6\"),\n            imageWidth: 240\n        },\n        {\n            imageSrc: \"/pride-image-7.jpg\",\n            title: t(\"pride7\"),\n            imageWidth: 240\n        },\n        {\n            imageSrc: \"/pride-image-8.jpg\",\n            title: t(\"pride8\"),\n            imageWidth: 240\n        },\n        {\n            imageSrc: \"/pride-image-9.jpg\",\n            title: t(\"pride9\"),\n            imageWidth: 240\n        }\n    ];\n    const CertificateItem = ({ imageSrc, title, imageWidth })=>{\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                width: imageWidth\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                        src: imageSrc,\n                        height: 160,\n                        width: imageWidth,\n                        alt: \"\",\n                        unoptimized: true\n                    }, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\certificates.tsx\",\n                        lineNumber: 76,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\certificates.tsx\",\n                    lineNumber: 75,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: title\n                }, void 0, false, {\n                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\certificates.tsx\",\n                    lineNumber: 84,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\certificates.tsx\",\n            lineNumber: 74,\n            columnNumber: 7\n        }, this);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `cylan-certificates hide-on-medium hide-on-large`,\n        children: certificates.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CertificateItem, {\n                ...item\n            }, index, false, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\certificates.tsx\",\n                lineNumber: 92,\n                columnNumber: 9\n            }, this))\n    }, void 0, false, {\n        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\certificates.tsx\",\n        lineNumber: 90,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/[locale]/ui/components/certificates.tsx\n");

/***/ }),

/***/ "(ssr)/./app/[locale]/ui/components/dropdown-window.tsx":
/*!********************************************************!*\
  !*** ./app/[locale]/ui/components/dropdown-window.tsx ***!
  \********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DropdownWindow)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n\n\n\nfunction DropdownWindow({ children, list, show = false, onClick = ()=>{}, onClickMask = ()=>{} }) {\n    const handleClick = (id)=>{\n        onClick(id);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            position: \"relative\"\n        },\n        children: [\n            children,\n            show && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"dropdown-window\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"dropdown-window__above\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                    src: \"/dropdown-window-above.svg\",\n                                    width: 15,\n                                    height: 8,\n                                    alt: \"drop\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\dropdown-window.tsx\",\n                                    lineNumber: 34,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\dropdown-window.tsx\",\n                                lineNumber: 33,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"dropdown-window__list\",\n                                children: list.map((item, index)=>{\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: item.link ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            href: item.link,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"dropdown-window__list__item\",\n                                                onClick: ()=>{\n                                                    handleClick(item.link);\n                                                },\n                                                children: item.text\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\dropdown-window.tsx\",\n                                                lineNumber: 47,\n                                                columnNumber: 25\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\dropdown-window.tsx\",\n                                            lineNumber: 46,\n                                            columnNumber: 23\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"dropdown-window__list__item\",\n                                            onClick: ()=>{\n                                                handleClick(item?.id);\n                                            },\n                                            children: item.text\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\dropdown-window.tsx\",\n                                            lineNumber: 57,\n                                            columnNumber: 23\n                                        }, this)\n                                    }, index, false, {\n                                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\dropdown-window.tsx\",\n                                        lineNumber: 44,\n                                        columnNumber: 19\n                                    }, this);\n                                })\n                            }, void 0, false, {\n                                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\dropdown-window.tsx\",\n                                lineNumber: 41,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"dropdown-window__placeholder\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\dropdown-window.tsx\",\n                                lineNumber: 70,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\dropdown-window.tsx\",\n                        lineNumber: 32,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"dropdown-window__mask hide-on-medium hide-on-large\",\n                        onClick: ()=>{\n                            onClickMask();\n                        }\n                    }, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\dropdown-window.tsx\",\n                        lineNumber: 72,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\dropdown-window.tsx\",\n        lineNumber: 28,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/[locale]/ui/components/dropdown-window.tsx\n");

/***/ }),

/***/ "(ssr)/./app/[locale]/ui/components/flex-4items-box.tsx":
/*!********************************************************!*\
  !*** ./app/[locale]/ui/components/flex-4items-box.tsx ***!
  \********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Flex4ItemsBox),\n/* harmony export */   itemsMode: () => (/* binding */ itemsMode)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_scss__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./components.scss */ \"(ssr)/./app/[locale]/ui/components/components.scss\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n\n\n\n\n\nvar itemsMode;\n(function(itemsMode) {\n    itemsMode[\"normal\"] = \"\";\n    itemsMode[\"product\"] = \"product\";\n    itemsMode[\"pride\"] = \"pride\";\n})(itemsMode || (itemsMode = {}));\nfunction Flex4ItemsBox({ infos, imageSize, imageBox, mode = \"\", gap = 0, isDetail = false }) {\n    const renderCard = ({ imageSrc, title, tip = \"\", link = \"\", videoSrc = \"\" })=>{\n        const isLinkCard = !!link;\n        const isVideoCard = !!videoSrc;\n        const cardClassName = `flex-box-with-4items__card ${mode === \"product\" ? \"flex-box-with-4items__card--product\" : \"\"} ${isLinkCard ? \"flex-box-with-4items__card--link\" : \"\"} ${isVideoCard ? \"flex-box-with-4items__card--video\" : \"\"}`;\n        const infoClassName = `${mode === \"\" ? \"\" : `flex-box-with-4items__card__info--${mode}`} flex-box-with-4items__card__info`;\n        const cardContent = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-box-with-4items__card__image\",\n                    style: {\n                        aspectRatio: imageBox.width / imageBox.height\n                    },\n                    children: isVideoCard ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"video\", {\n                        width: imageSize.width,\n                        height: imageSize.height,\n                        controls: true,\n                        poster: imageSrc,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"source\", {\n                                src: videoSrc,\n                                type: \"video/mp4\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\flex-4items-box.tsx\",\n                                lineNumber: 76,\n                                columnNumber: 15\n                            }, this),\n                            \"Your browser does not support the video tag.\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\flex-4items-box.tsx\",\n                        lineNumber: 70,\n                        columnNumber: 13\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                        unoptimized: true,\n                        src: imageSrc,\n                        width: imageSize.width,\n                        height: imageSize.height,\n                        alt: \"image\",\n                        style: {\n                            objectFit: \"fill\",\n                            width: \"100%\",\n                            height: \"auto\"\n                        }\n                    }, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\flex-4items-box.tsx\",\n                        lineNumber: 80,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\flex-4items-box.tsx\",\n                    lineNumber: 65,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: infoClassName,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: title\n                        }, void 0, false, {\n                            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\flex-4items-box.tsx\",\n                            lineNumber: 95,\n                            columnNumber: 11\n                        }, this),\n                        mode === \"pride\" ? \"\" : tip ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: tip\n                        }, void 0, false, {\n                            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\flex-4items-box.tsx\",\n                            lineNumber: 96,\n                            columnNumber: 50\n                        }, this) : \"\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\flex-4items-box.tsx\",\n                    lineNumber: 94,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true);\n        return isLinkCard ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n            href: link,\n            className: cardClassName,\n            children: cardContent\n        }, void 0, false, {\n            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\flex-4items-box.tsx\",\n            lineNumber: 102,\n            columnNumber: 7\n        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: cardClassName,\n            children: cardContent\n        }, void 0, false, {\n            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\flex-4items-box.tsx\",\n            lineNumber: 106,\n            columnNumber: 7\n        }, this);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `${`flex-box-with-4items`} ${mode === \"product\" ? \"flex-box-with-4items--product\" : \"\"} ${isDetail ? \"flex-box-with-4items--detail\" : \"\"}`,\n        style: gap ? {\n            gap\n        } : {},\n        children: infos.map((info, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((react__WEBPACK_IMPORTED_MODULE_2___default().Fragment), {\n                children: renderCard(info)\n            }, `${info.link}-${index}`, false, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\flex-4items-box.tsx\",\n                lineNumber: 117,\n                columnNumber: 9\n            }, this))\n    }, void 0, false, {\n        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\flex-4items-box.tsx\",\n        lineNumber: 110,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/[locale]/ui/components/flex-4items-box.tsx\n");

/***/ }),

/***/ "(ssr)/./app/[locale]/ui/components/nav-list.tsx":
/*!*************************************************!*\
  !*** ./app/[locale]/ui/components/nav-list.tsx ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NavListLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _locales_client__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/locales/client */ \"(ssr)/./locales/client.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction NavListLayout({ isFooter = false, onClick = ()=>{} }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_locales_client__WEBPACK_IMPORTED_MODULE_4__.I18nProviderClient, {\n        locale: (0,_locales_client__WEBPACK_IMPORTED_MODULE_4__.useCurrentLocale)(),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NavList, {\n            isFooter: isFooter,\n            onClick: onClick\n        }, void 0, false, {\n            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\nav-list.tsx\",\n            lineNumber: 51,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\nav-list.tsx\",\n        lineNumber: 50,\n        columnNumber: 5\n    }, this);\n}\nfunction NavList({ isFooter = false, onClick }) {\n    const t = (0,_locales_client__WEBPACK_IMPORTED_MODULE_4__.useI18n)();\n    const _items = [\n        {\n            iconSrc: \"/menu-home-icon.svg\",\n            title: t(\"home\"),\n            id: 0,\n            link: \"/\"\n        },\n        {\n            iconSrc: \"/menu-product-icon.svg\",\n            title: t(\"productCenter\"),\n            id: 1,\n            active: false\n        },\n        {\n            subNavs: [\n                {\n                    title: t(\"productCamera\"),\n                    link: \"/product\",\n                    id: 1\n                }\n            ]\n        },\n        // {\n        //   iconSrc: '/menu-videos-icon.svg',\n        //   title: '产品视频',\n        //   link: '/videos',\n        //   id: 2,\n        // },\n        {\n            iconSrc: \"/menu-support-icon.svg\",\n            title: t(\"support\"),\n            link: \"/support\",\n            id: 3\n        },\n        // {\n        //   iconSrc: '/menu-news-icon.svg',\n        //   title: '新闻资讯',\n        //   link: '/news',\n        //   id: 4,\n        // },\n        {\n            iconSrc: \"/menu-about-icon.svg\",\n            title: t(\"aboutCylan\"),\n            link: \"/about\",\n            id: 5\n        }\n    ];\n    const [items, setItems] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(_items);\n    const handleClick = (id, isLink)=>{\n        if (isLink) {\n            onClick && onClick();\n            return;\n        }\n        const newItems = [\n            ...items\n        ];\n        newItems.forEach((item)=>{\n            if (item.id === id) item.active = !item.active;\n        });\n        setItems(newItems);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `nav-list ${isFooter ? \"nav-list--footer\" : \"\"}`,\n        children: items.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NavItem, {\n                navItemPros: item,\n                navItemStatus: items,\n                onClick: handleClick\n            }, index, false, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\nav-list.tsx\",\n                lineNumber: 123,\n                columnNumber: 9\n            }, this))\n    }, void 0, false, {\n        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\nav-list.tsx\",\n        lineNumber: 121,\n        columnNumber: 5\n    }, this);\n}\nfunction NavItem({ navItemPros, navItemStatus, onClick }) {\n    if (navItemPros.subNavs) {\n        const subNavs = navItemPros.subNavs;\n        const id = subNavs[0].id;\n        const mainItem = navItemStatus.find((item)=>item?.id === id);\n        const handleClick = ()=>{\n            const isLink = true;\n            onClick(id, isLink);\n        };\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: mainItem?.active && subNavs.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                    href: item.link,\n                    onClick: handleClick,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"nav-list__item nav-list__item--sub\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"nav-list__item__icon\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\nav-list.tsx\",\n                                lineNumber: 158,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"nav-list__item__title\",\n                                children: item.title\n                            }, void 0, false, {\n                                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\nav-list.tsx\",\n                                lineNumber: 159,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\nav-list.tsx\",\n                        lineNumber: 157,\n                        columnNumber: 15\n                    }, this)\n                }, index, false, {\n                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\nav-list.tsx\",\n                    lineNumber: 156,\n                    columnNumber: 13\n                }, this))\n        }, void 0, false);\n    } else {\n        const { iconSrc, title, link, id, active } = navItemPros;\n        const handleClick = ()=>{\n            const isLink = !!link;\n            onClick(id, isLink);\n        };\n        const Item = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"nav-list__item__icon\",\n                        children: iconSrc && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            src: iconSrc,\n                            width: 24,\n                            height: 24,\n                            alt: \"\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\nav-list.tsx\",\n                            lineNumber: 177,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\nav-list.tsx\",\n                        lineNumber: 175,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"nav-list__item__title\",\n                        children: title\n                    }, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\nav-list.tsx\",\n                        lineNumber: 180,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            flex: 1\n                        }\n                    }, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\nav-list.tsx\",\n                        lineNumber: 181,\n                        columnNumber: 9\n                    }, this),\n                    !link && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"nav-list__item__button\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            src: `/menu-arrow-${active ? \"up\" : \"down\"}.svg`,\n                            width: 32,\n                            height: 32,\n                            alt: \"\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\nav-list.tsx\",\n                            lineNumber: 184,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\nav-list.tsx\",\n                        lineNumber: 183,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true);\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                onClick: handleClick,\n                className: \"nav-list__item\",\n                children: [\n                    !link && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Item, {}, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\nav-list.tsx\",\n                        lineNumber: 198,\n                        columnNumber: 21\n                    }, this),\n                    link && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                        style: {\n                            width: \"100%\",\n                            height: \"100%\"\n                        },\n                        className: \"nav-list__item\",\n                        href: link,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Item, {}, void 0, false, {\n                            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\nav-list.tsx\",\n                            lineNumber: 205,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\nav-list.tsx\",\n                        lineNumber: 200,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\nav-list.tsx\",\n                lineNumber: 197,\n                columnNumber: 9\n            }, this)\n        }, void 0, false);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/[locale]/ui/components/nav-list.tsx\n");

/***/ }),

/***/ "(ssr)/./app/[locale]/ui/home/<USER>":
/*!****************************************!*\
  !*** ./app/[locale]/ui/home/<USER>
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CompanyTime: () => (/* binding */ CompanyTime),\n/* harmony export */   Contacts: () => (/* binding */ Contacts),\n/* harmony export */   \"default\": () => (/* binding */ ABoutLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _home_module_scss__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./home.module.scss */ \"(ssr)/./app/[locale]/ui/home/<USER>");\n/* harmony import */ var _home_module_scss__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(_home_module_scss__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _components_flex_4items_box__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../components/flex-4items-box */ \"(ssr)/./app/[locale]/ui/components/flex-4items-box.tsx\");\n/* harmony import */ var _components_certificates__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../components/certificates */ \"(ssr)/./app/[locale]/ui/components/certificates.tsx\");\n/* harmony import */ var _locales_client__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/locales/client */ \"(ssr)/./locales/client.ts\");\n/* __next_internal_client_entry_do_not_use__ default,CompanyTime,Contacts auto */ \n\n\n\n\n\n\nfunction ABoutLayout() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_locales_client__WEBPACK_IMPORTED_MODULE_5__.I18nProviderClient, {\n        locale: (0,_locales_client__WEBPACK_IMPORTED_MODULE_5__.useCurrentLocale)(),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(About, {}, void 0, false, {\n            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\about.tsx\",\n            lineNumber: 15,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\about.tsx\",\n        lineNumber: 14,\n        columnNumber: 5\n    }, this);\n}\nfunction About() {\n    const t = (0,_locales_client__WEBPACK_IMPORTED_MODULE_5__.useI18n)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (_home_module_scss__WEBPACK_IMPORTED_MODULE_6___default().about),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_home_module_scss__WEBPACK_IMPORTED_MODULE_6___default().about__cover)\n            }, void 0, false, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\about.tsx\",\n                lineNumber: 25,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_home_module_scss__WEBPACK_IMPORTED_MODULE_6___default().about__content),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        children: t(\"aboutUs\")\n                    }, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\about.tsx\",\n                        lineNumber: 27,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CompanyTime, {}, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\about.tsx\",\n                        lineNumber: 28,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Prides, {}, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\about.tsx\",\n                        lineNumber: 29,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Contacts, {}, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\about.tsx\",\n                        lineNumber: 30,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\about.tsx\",\n                lineNumber: 26,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\about.tsx\",\n        lineNumber: 24,\n        columnNumber: 5\n    }, this);\n}\nfunction CompanyTime({ isAboutPage = false }) {\n    const t = (0,_locales_client__WEBPACK_IMPORTED_MODULE_5__.useI18n)();\n    const locale = (0,_locales_client__WEBPACK_IMPORTED_MODULE_5__.useCurrentLocale)();\n    const TimeItem = ({ num, unit, text })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: (_home_module_scss__WEBPACK_IMPORTED_MODULE_6___default().about__content__time__item),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: num\n                        }, void 0, false, {\n                            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\about.tsx\",\n                            lineNumber: 55,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: unit\n                        }, void 0, false, {\n                            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\about.tsx\",\n                            lineNumber: 56,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\about.tsx\",\n                    lineNumber: 54,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: text\n                }, void 0, false, {\n                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\about.tsx\",\n                    lineNumber: 58,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\about.tsx\",\n            lineNumber: 53,\n            columnNumber: 5\n        }, this);\n    const Line = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                height: 46,\n                width: 0,\n                opacity: 0.1,\n                border: `1px solid ${isAboutPage ? \"var(--gray)\" : \"#fff\"}`\n            }\n        }, void 0, false, {\n            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\about.tsx\",\n            lineNumber: 63,\n            columnNumber: 5\n        }, this);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `${(_home_module_scss__WEBPACK_IMPORTED_MODULE_6___default().about__content__time)} ${isAboutPage ? (_home_module_scss__WEBPACK_IMPORTED_MODULE_6___default()[\"about__content__time--page\"]) : \"\"}`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TimeItem, {\n                num: 2005,\n                unit: t(\"year\"),\n                text: t(\"companiyTime\")\n            }, void 0, false, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\about.tsx\",\n                lineNumber: 79,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Line, {}, void 0, false, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\about.tsx\",\n                lineNumber: 80,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TimeItem, {\n                num: locale === \"zh\" ? 45 : 450,\n                unit: locale === \"zh\" ? \"万+\" : \"K+\",\n                text: t(\"deviceSells\")\n            }, void 0, false, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\about.tsx\",\n                lineNumber: 81,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Line, {}, void 0, false, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\about.tsx\",\n                lineNumber: 86,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TimeItem, {\n                num: locale === \"zh\" ? 90 : 900,\n                unit: locale === \"zh\" ? \"万+\" : \"K+\",\n                text: t(\"activeUsers\")\n            }, void 0, false, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\about.tsx\",\n                lineNumber: 87,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\about.tsx\",\n        lineNumber: 74,\n        columnNumber: 5\n    }, this);\n}\nfunction Prides() {\n    const t = (0,_locales_client__WEBPACK_IMPORTED_MODULE_5__.useI18n)();\n    const PridesInfo = {\n        infos: [\n            {\n                imageSrc: \"/pride-image-6.jpg\",\n                title: t(\"pride6\")\n            },\n            {\n                imageSrc: \"/pride-image-9.jpg\",\n                title: t(\"pride9\")\n            },\n            {\n                imageSrc: \"/pride-image-5.jpg\",\n                title: t(\"pride5\")\n            },\n            {\n                imageSrc: \"/pride-image-2.jpg\",\n                title: t(\"pride2\")\n            }\n        ],\n        imageSize: {\n            width: 300,\n            height: 200\n        },\n        imageBox: {\n            width: 300,\n            height: 200\n        },\n        mode: _components_flex_4items_box__WEBPACK_IMPORTED_MODULE_3__.itemsMode.pride\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (_home_module_scss__WEBPACK_IMPORTED_MODULE_6___default().about__prides),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"hide-on-small\",\n                style: {\n                    marginTop: 30\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_flex_4items_box__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    ...PridesInfo\n                }, void 0, false, {\n                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\about.tsx\",\n                    lineNumber: 143,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\about.tsx\",\n                lineNumber: 142,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_certificates__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\about.tsx\",\n                lineNumber: 146,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\about.tsx\",\n        lineNumber: 141,\n        columnNumber: 5\n    }, this);\n}\nfunction Contacts({ isAboutPage = false }) {\n    const t = (0,_locales_client__WEBPACK_IMPORTED_MODULE_5__.useI18n)();\n    const ContactItem = ({ iconUrl, text })=>{\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: (_home_module_scss__WEBPACK_IMPORTED_MODULE_6___default().contacts__info__item),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    src: iconUrl,\n                    width: 18,\n                    height: 20,\n                    alt: \"icon\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\about.tsx\",\n                    lineNumber: 163,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        display: \"flex\",\n                        flexDirection: \"column\",\n                        gap: 4\n                    },\n                    children: text.split(`/n`).map((t, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: t\n                        }, index, false, {\n                            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\about.tsx\",\n                            lineNumber: 166,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\about.tsx\",\n                    lineNumber: 164,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\about.tsx\",\n            lineNumber: 162,\n            columnNumber: 7\n        }, this);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `${(_home_module_scss__WEBPACK_IMPORTED_MODULE_6___default().contacts)} ${isAboutPage ? (_home_module_scss__WEBPACK_IMPORTED_MODULE_6___default()[\"contacts--page\"]) : \"\"}`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_home_module_scss__WEBPACK_IMPORTED_MODULE_6___default().contacts__info),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_home_module_scss__WEBPACK_IMPORTED_MODULE_6___default().contacts__info__title),\n                        children: t(\"contactUs\")\n                    }, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\about.tsx\",\n                        lineNumber: 180,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_home_module_scss__WEBPACK_IMPORTED_MODULE_6___default().contacts__info__items),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ContactItem, {\n                                iconUrl: \"/contact-position.svg\",\n                                text: t(\"cylanAddress\")\n                            }, void 0, false, {\n                                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\about.tsx\",\n                                lineNumber: 182,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ContactItem, {\n                                iconUrl: \"/contact-email.svg\",\n                                text: \"<EMAIL>\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\about.tsx\",\n                                lineNumber: 186,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ContactItem, {\n                                iconUrl: \"/contact-phone.svg\",\n                                text: \"+86-0755-83073491\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\about.tsx\",\n                                lineNumber: 187,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\about.tsx\",\n                        lineNumber: 181,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\about.tsx\",\n                lineNumber: 179,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_home_module_scss__WEBPACK_IMPORTED_MODULE_6___default().contacts__address),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                    href: \"https://map.baidu.com/poi/%E5%90%88%E6%88%90%E5%8F%B7%E6%B7%B1%E5%9C%B3%E6%B0%91%E4%BF%97%E6%96%87%E5%8C%96%E4%BA%A7%E4%B8%9A%E5%9B%AD/@12682764.738888016,2566016.5718568345,14z?uid=eb63e5cd850d1ef4a3acc4a1&ugc_type=3&ugc_ver=1&device_ratio=1&compat=1&pcevaname=pc4.1&querytype=detailConInfo\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        src: \"/address-image.webp\",\n                        width: 625,\n                        height: 249,\n                        alt: \"address\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\about.tsx\",\n                        lineNumber: 192,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\about.tsx\",\n                    lineNumber: 191,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\about.tsx\",\n                lineNumber: 190,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\about.tsx\",\n        lineNumber: 174,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(ssr)/./app/[locale]/ui/home/<USER>");

/***/ }),

/***/ "(ssr)/./app/[locale]/ui/home/<USER>":
/*!************************************************!*\
  !*** ./app/[locale]/ui/home/<USER>
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ BannerSlider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var swiper_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! swiper/react */ \"(ssr)/./node_modules/swiper/swiper-react.mjs\");\n/* harmony import */ var swiper_modules__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! swiper/modules */ \"(ssr)/./node_modules/swiper/modules/index.mjs\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _locales_client__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/locales/client */ \"(ssr)/./locales/client.ts\");\n/* harmony import */ var swiper_css__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! swiper/css */ \"(ssr)/./node_modules/swiper/swiper.css\");\n/* harmony import */ var swiper_css_navigation__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! swiper/css/navigation */ \"(ssr)/./node_modules/swiper/modules/navigation.css\");\n/* harmony import */ var swiper_css_pagination__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! swiper/css/pagination */ \"(ssr)/./node_modules/swiper/modules/pagination.css\");\n/* harmony import */ var _home_module_scss__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./home.module.scss */ \"(ssr)/./app/[locale]/ui/home/<USER>");\n/* harmony import */ var _home_module_scss__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(_home_module_scss__WEBPACK_IMPORTED_MODULE_9__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n// Import Swiper styles\n\n\n\n\nfunction BannerSlider() {\n    const locale = (0,_locales_client__WEBPACK_IMPORTED_MODULE_5__.useCurrentLocale)();\n    const images = [\n        locale === \"zh\" ? \"/home/<USER>" : \"/home/<USER>",\n        locale === \"zh\" ? \"/home/<USER>" : \"/home/<USER>"\n    ];\n    const urls = [\n        \"/product/c31\",\n        \"/product/t1pro\"\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (_home_module_scss__WEBPACK_IMPORTED_MODULE_9___default()[\"banner-slider\"]),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(swiper_react__WEBPACK_IMPORTED_MODULE_1__.Swiper, {\n                modules: [\n                    swiper_modules__WEBPACK_IMPORTED_MODULE_2__.Navigation,\n                    swiper_modules__WEBPACK_IMPORTED_MODULE_2__.Pagination,\n                    swiper_modules__WEBPACK_IMPORTED_MODULE_2__.Autoplay\n                ],\n                spaceBetween: 0,\n                slidesPerView: 1,\n                navigation: {\n                    nextEl: `.${(_home_module_scss__WEBPACK_IMPORTED_MODULE_9___default()[\"banner-slider__button-next\"])}`,\n                    prevEl: `.${(_home_module_scss__WEBPACK_IMPORTED_MODULE_9___default()[\"banner-slider__button-prev\"])}`\n                },\n                pagination: {\n                    el: `.${(_home_module_scss__WEBPACK_IMPORTED_MODULE_9___default()[\"banner-slider__pagination\"])}`,\n                    clickable: true,\n                    bulletClass: (_home_module_scss__WEBPACK_IMPORTED_MODULE_9___default()[\"banner-slider__bullet\"]),\n                    bulletActiveClass: (_home_module_scss__WEBPACK_IMPORTED_MODULE_9___default()[\"banner-slider__bullet--active\"])\n                },\n                autoplay: {\n                    delay: 5000,\n                    disableOnInteraction: false,\n                    pauseOnMouseEnter: true\n                },\n                loop: images.length > 1,\n                className: (_home_module_scss__WEBPACK_IMPORTED_MODULE_9___default()[\"banner-slider__swiper\"]),\n                children: images.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(swiper_react__WEBPACK_IMPORTED_MODULE_1__.SwiperSlide, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (_home_module_scss__WEBPACK_IMPORTED_MODULE_9___default()[\"banner-slider__slide\"]),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                style: {\n                                    height: \"100%\",\n                                    width: \"100%\"\n                                },\n                                href: urls[index],\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    src: item,\n                                    width: 1920,\n                                    height: 500,\n                                    alt: `Banner ${index + 1}`,\n                                    unoptimized: true,\n                                    style: {\n                                        width: \"100%\",\n                                        height: \"100%\",\n                                        objectFit: \"cover\",\n                                        objectPosition: \"center\"\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\banner-slider.tsx\",\n                                    lineNumber: 59,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\banner-slider.tsx\",\n                                lineNumber: 55,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\banner-slider.tsx\",\n                            lineNumber: 54,\n                            columnNumber: 13\n                        }, this)\n                    }, index, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\banner-slider.tsx\",\n                        lineNumber: 53,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\banner-slider.tsx\",\n                lineNumber: 30,\n                columnNumber: 7\n            }, this),\n            images.length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `${(_home_module_scss__WEBPACK_IMPORTED_MODULE_9___default()[\"banner-slider__button-prev\"])} ${(_home_module_scss__WEBPACK_IMPORTED_MODULE_9___default()[\"banner-slider__button\"])}`,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            src: \"/slider-left.svg\",\n                            width: 44,\n                            height: 44,\n                            alt: \"Previous\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\banner-slider.tsx\",\n                            lineNumber: 82,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\banner-slider.tsx\",\n                        lineNumber: 81,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `${(_home_module_scss__WEBPACK_IMPORTED_MODULE_9___default()[\"banner-slider__button-next\"])} ${(_home_module_scss__WEBPACK_IMPORTED_MODULE_9___default()[\"banner-slider__button\"])}`,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            src: \"/slider-right.svg\",\n                            width: 44,\n                            height: 44,\n                            alt: \"Next\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\banner-slider.tsx\",\n                            lineNumber: 90,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\banner-slider.tsx\",\n                        lineNumber: 89,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_home_module_scss__WEBPACK_IMPORTED_MODULE_9___default()[\"banner-slider__pagination\"])\n                    }, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\banner-slider.tsx\",\n                        lineNumber: 99,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\banner-slider.tsx\",\n        lineNumber: 29,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/[locale]/ui/home/<USER>");

/***/ }),

/***/ "(ssr)/./app/[locale]/ui/home/<USER>":
/*!*****************************************!*\
  !*** ./app/[locale]/ui/home/<USER>
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ FooterLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _home_module_scss__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./home.module.scss */ \"(ssr)/./app/[locale]/ui/home/<USER>");\n/* harmony import */ var _home_module_scss__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(_home_module_scss__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _components_nav_list__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../components/nav-list */ \"(ssr)/./app/[locale]/ui/components/nav-list.tsx\");\n/* harmony import */ var _locales_client__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/locales/client */ \"(ssr)/./locales/client.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\nfunction FooterLayout() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_locales_client__WEBPACK_IMPORTED_MODULE_5__.I18nProviderClient, {\n        locale: (0,_locales_client__WEBPACK_IMPORTED_MODULE_5__.useCurrentLocale)(),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Footer, {}, void 0, false, {\n            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\footer.tsx\",\n            lineNumber: 13,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\footer.tsx\",\n        lineNumber: 12,\n        columnNumber: 5\n    }, this);\n}\nfunction Footer() {\n    const t = (0,_locales_client__WEBPACK_IMPORTED_MODULE_5__.useI18n)();\n    const renderLink = ({ link, text })=>{\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n            href: link,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                children: text\n            }, void 0, false, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\footer.tsx\",\n                lineNumber: 29,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\footer.tsx\",\n            lineNumber: 28,\n            columnNumber: 7\n        }, this);\n    };\n    const FooterItem = ({ title, links })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: (_home_module_scss__WEBPACK_IMPORTED_MODULE_6___default().footer__links__item),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: title\n                }, void 0, false, {\n                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\footer.tsx\",\n                    lineNumber: 42,\n                    columnNumber: 7\n                }, this),\n                links.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((react__WEBPACK_IMPORTED_MODULE_1___default().Fragment), {\n                        children: renderLink({\n                            link: item.link,\n                            text: item.text\n                        })\n                    }, `${item.link}-${index}`, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\footer.tsx\",\n                        lineNumber: 44,\n                        columnNumber: 9\n                    }, this))\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\footer.tsx\",\n            lineNumber: 41,\n            columnNumber: 5\n        }, this);\n    const productLinks = [\n        {\n            link: \"/product?tab=01\",\n            text: t(\"productCamera\")\n        },\n        {\n            link: \"/product?tab=02\",\n            text: t(\"productTranslator\")\n        }\n    ];\n    const supportLinks = [\n        {\n            link: \"/support/download_client\",\n            text: t(\"downloadClient\")\n        },\n        {\n            link: \"/support/help\",\n            text: t(\"help\")\n        }\n    ];\n    const aboutLinks = [\n        {\n            link: \"/about#about-cylan\",\n            text: t(\"aboutCylan\")\n        },\n        {\n            link: \"/about#prides\",\n            text: t(\"cylanPrides\")\n        },\n        {\n            link: \"/about#contacts\",\n            text: t(\"contactUs\")\n        }\n    ];\n    const Follow = ()=>{\n        const [isShowPop, setIsShowpop] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: (_home_module_scss__WEBPACK_IMPORTED_MODULE_6___default().footer__links__follow),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: t(\"followUs\")\n                }, void 0, false, {\n                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\footer.tsx\",\n                    lineNumber: 93,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    onMouseEnter: ()=>{\n                        setIsShowpop(true);\n                    },\n                    onMouseLeave: ()=>{\n                        setIsShowpop(false);\n                    },\n                    className: (_home_module_scss__WEBPACK_IMPORTED_MODULE_6___default().footer__links__follow__weixin),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            src: \"/weixin.svg\",\n                            width: 20,\n                            height: 20,\n                            alt: \"weixin\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\footer.tsx\",\n                            lineNumber: 103,\n                            columnNumber: 11\n                        }, this),\n                        isShowPop && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    src: \"/support/imcam-gongzhonghao.jpg\",\n                                    width: 140,\n                                    height: 140,\n                                    alt: \"\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\footer.tsx\",\n                                    lineNumber: 111,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: t(\"imcamGongzhonghao\")\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\footer.tsx\",\n                                    lineNumber: 117,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    className: \"hide-on-medium hide-on-large\",\n                                    href: \"/support/imcam-gongzhonghao.jpg\",\n                                    download: true,\n                                    children: t(\"downloadQRcode\")\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\footer.tsx\",\n                                    lineNumber: 118,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\footer.tsx\",\n                            lineNumber: 110,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\footer.tsx\",\n                    lineNumber: 94,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\footer.tsx\",\n            lineNumber: 92,\n            columnNumber: 7\n        }, this);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (_home_module_scss__WEBPACK_IMPORTED_MODULE_6___default().footer),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `${(_home_module_scss__WEBPACK_IMPORTED_MODULE_6___default().footer__logo)} hide-on-medium hide-on-large`,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    href: \"/\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        src: \"/cylan_logo-white.png\",\n                        width: 125,\n                        height: 44,\n                        alt: \"logo\",\n                        unoptimized: true\n                    }, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\footer.tsx\",\n                        lineNumber: 136,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\footer.tsx\",\n                    lineNumber: 135,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\footer.tsx\",\n                lineNumber: 134,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `${(_home_module_scss__WEBPACK_IMPORTED_MODULE_6___default().footer__links)} hide-on-small`,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `${(_home_module_scss__WEBPACK_IMPORTED_MODULE_6___default().footer__logo)}`,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            href: \"/\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                src: \"/cylan_logo-white.png\",\n                                width: 125,\n                                height: 44,\n                                alt: \"logo\",\n                                unoptimized: true\n                            }, void 0, false, {\n                                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\footer.tsx\",\n                                lineNumber: 148,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\footer.tsx\",\n                            lineNumber: 147,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\footer.tsx\",\n                        lineNumber: 146,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FooterItem, {\n                        title: t(\"productCenter\"),\n                        links: productLinks\n                    }, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\footer.tsx\",\n                        lineNumber: 157,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FooterItem, {\n                        title: t(\"support\"),\n                        links: supportLinks\n                    }, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\footer.tsx\",\n                        lineNumber: 159,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FooterItem, {\n                        title: t(\"aboutUs\"),\n                        links: aboutLinks\n                    }, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\footer.tsx\",\n                        lineNumber: 161,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Follow, {}, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\footer.tsx\",\n                        lineNumber: 162,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\footer.tsx\",\n                lineNumber: 145,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"hide-on-large hide-on-medium\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_nav_list__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        isFooter: true\n                    }, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\footer.tsx\",\n                        lineNumber: 165,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Follow, {}, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\footer.tsx\",\n                        lineNumber: 166,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\footer.tsx\",\n                lineNumber: 164,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_home_module_scss__WEBPACK_IMPORTED_MODULE_6___default().footer__copyright),\n                children: [\n                    t(\"copyrightText\"),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        className: (_home_module_scss__WEBPACK_IMPORTED_MODULE_6___default().footer__copyright__link),\n                        href: \"https://beian.miit.gov.cn/\",\n                        target: \"_blank\",\n                        children: t(\"copyrightLink\")\n                    }, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\footer.tsx\",\n                        lineNumber: 170,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\footer.tsx\",\n                lineNumber: 168,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\footer.tsx\",\n        lineNumber: 133,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/[locale]/ui/home/<USER>");

/***/ }),

/***/ "(ssr)/./app/[locale]/ui/home/<USER>":
/*!**************************************!*\
  !*** ./app/[locale]/ui/home/<USER>
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NavLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _home_module_scss__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./home.module.scss */ \"(ssr)/./app/[locale]/ui/home/<USER>");\n/* harmony import */ var _home_module_scss__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(_home_module_scss__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _components_dropdown_window__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../components/dropdown-window */ \"(ssr)/./app/[locale]/ui/components/dropdown-window.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_nav_list__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../components/nav-list */ \"(ssr)/./app/[locale]/ui/components/nav-list.tsx\");\n/* harmony import */ var _locales_client__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/locales/client */ \"(ssr)/./locales/client.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\n\nfunction NavLayout() {\n    const selectedLayoutSegment = (0,next_navigation__WEBPACK_IMPORTED_MODULE_5__.useSelectedLayoutSegment)();\n    const isNotFoundPage = selectedLayoutSegment === \"__DEFAULT__\";\n    if (isNotFoundPage) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {}, void 0, false, {\n        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\nav.tsx\",\n        lineNumber: 21,\n        columnNumber: 30\n    }, this);\n    else return(// eslint-disable-next-line react-hooks/rules-of-hooks\n    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_locales_client__WEBPACK_IMPORTED_MODULE_7__.I18nProviderClient, {\n        locale: (0,_locales_client__WEBPACK_IMPORTED_MODULE_7__.useCurrentLocale)(),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Nav, {}, void 0, false, {\n            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\nav.tsx\",\n            lineNumber: 26,\n            columnNumber: 9\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\nav.tsx\",\n        lineNumber: 25,\n        columnNumber: 7\n    }, this));\n}\nfunction Nav() {\n    const t = (0,_locales_client__WEBPACK_IMPORTED_MODULE_7__.useI18n)();\n    const [isScrolled, setIsScrolled] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\n    const [isShowMenu, setShowMenu] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)(()=>{\n        // 监听滚动事件\n        const handleScroll = ()=>{\n            // 获取当前滚动位置\n            const scrollTop = window.scrollY;\n            // 根据滚动位置是否大于0来判断是否添加投影效果\n            setIsScrolled(scrollTop > 0);\n        };\n        window.addEventListener(\"scroll\", handleScroll);\n        return ()=>{\n            window.removeEventListener(\"scroll\", handleScroll);\n        };\n    }, []);\n    const handleNavListClick = ()=>{\n        setShowMenu(false);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            position: \"relative\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_home_module_scss__WEBPACK_IMPORTED_MODULE_8___default().nav__placeholder)\n            }, void 0, false, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\nav.tsx\",\n                lineNumber: 57,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `${(_home_module_scss__WEBPACK_IMPORTED_MODULE_8___default().nav)} ${isScrolled ? (_home_module_scss__WEBPACK_IMPORTED_MODULE_8___default()[\"nav--scrolled\"]) : \"\"} ${isShowMenu ? (_home_module_scss__WEBPACK_IMPORTED_MODULE_8___default()[\"nav--scrolled\"]) : \"\"}`,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_home_module_scss__WEBPACK_IMPORTED_MODULE_8___default().nav__content),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                            href: \"/\",\n                            style: {\n                                height: 44\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                src: \"/cylan_logo.png\",\n                                width: 125,\n                                height: 44,\n                                alt: \"Cylan Logo\",\n                                unoptimized: true\n                            }, void 0, false, {\n                                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\nav.tsx\",\n                                lineNumber: 65,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\nav.tsx\",\n                            lineNumber: 64,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: `${(_home_module_scss__WEBPACK_IMPORTED_MODULE_8___default().nav__list)} hide-on-small hide-on-medium`,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NavListItem, {\n                                    title: t(\"home\"),\n                                    link: \"/\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\nav.tsx\",\n                                    lineNumber: 76,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NavListItem, {\n                                    title: t(\"productCenter\"),\n                                    showArrow: true,\n                                    link: \"/product\",\n                                    links: [\n                                        {\n                                            link: \"/product?tab=01\",\n                                            text: t(\"productCamera\")\n                                        },\n                                        {\n                                            link: \"/product?tab=02\",\n                                            text: t(\"productTranslator\")\n                                        }\n                                    ]\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\nav.tsx\",\n                                    lineNumber: 77,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NavListItem, {\n                                    title: t(\"support\"),\n                                    showArrow: true,\n                                    link: \"/support\",\n                                    links: [\n                                        {\n                                            link: \"/support/download_client\",\n                                            text: t(\"downloadClient\")\n                                        },\n                                        {\n                                            link: \"/support/help\",\n                                            text: t(\"help\")\n                                        }\n                                    ]\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\nav.tsx\",\n                                    lineNumber: 93,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NavListItem, {\n                                    title: t(\"aboutUs\"),\n                                    link: \"/about\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\nav.tsx\",\n                                    lineNumber: 109,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\nav.tsx\",\n                            lineNumber: 75,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (_home_module_scss__WEBPACK_IMPORTED_MODULE_8___default().nav__right),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Language, {}, void 0, false, {\n                                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\nav.tsx\",\n                                    lineNumber: 114,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>{\n                                        setShowMenu(!isShowMenu);\n                                    },\n                                    className: `${(_home_module_scss__WEBPACK_IMPORTED_MODULE_8___default().nav__right__menu)} hide-on-large`,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        src: \"/menu.svg\",\n                                        width: 49,\n                                        height: 50,\n                                        alt: \"menu\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\nav.tsx\",\n                                        lineNumber: 122,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\nav.tsx\",\n                                    lineNumber: 116,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\nav.tsx\",\n                            lineNumber: 113,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\nav.tsx\",\n                    lineNumber: 62,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\nav.tsx\",\n                lineNumber: 58,\n                columnNumber: 7\n            }, this),\n            isShowMenu && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `${(_home_module_scss__WEBPACK_IMPORTED_MODULE_8___default().nav__drop)} hide-on-large`,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_nav_list__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            onClick: ()=>handleNavListClick()\n                        }, void 0, false, {\n                            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\nav.tsx\",\n                            lineNumber: 135,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\nav.tsx\",\n                        lineNumber: 134,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_home_module_scss__WEBPACK_IMPORTED_MODULE_8___default().nav__mask),\n                        onClick: ()=>{\n                            setShowMenu(false);\n                        }\n                    }, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\nav.tsx\",\n                        lineNumber: 137,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\nav.tsx\",\n        lineNumber: 56,\n        columnNumber: 5\n    }, this);\n}\nfunction ArrowDown() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        src: \"/arrow-down.svg\",\n        height: 10,\n        width: 16,\n        alt: \"\"\n    }, void 0, false, {\n        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\nav.tsx\",\n        lineNumber: 150,\n        columnNumber: 10\n    }, this);\n}\nfunction NavListItem({ title, link = \"/\", showArrow = false, links = [] }) {\n    // 鼠标悬停\n    const [isHovered, setIsHovered] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\n    const handleMouseEnter = ()=>{\n        setIsHovered(true);\n    };\n    const handleMouseLeave = ()=>{\n        setIsHovered(false);\n    };\n    let pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_5__.usePathname)();\n    if (pathname.includes(\"/zh/\") || pathname.includes(\"/en/\")) {\n        pathname = pathname.replace(/\\/zh|\\/en/, \"\");\n    } else {\n        pathname = \"/\";\n    }\n    pathname = \"/\" + pathname.split(\"/\")[1];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        onMouseEnter: handleMouseEnter,\n        onMouseLeave: handleMouseLeave,\n        children: showArrow ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dropdown_window__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            onClick: ()=>setIsHovered(false),\n            list: links,\n            show: isHovered,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                href: link,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: `${(_home_module_scss__WEBPACK_IMPORTED_MODULE_8___default().nav__list__item)} ${pathname === link.split(\"?\")[0] ? (_home_module_scss__WEBPACK_IMPORTED_MODULE_8___default()[\"nav__list__item--active\"]) : \"\"}`,\n                    children: [\n                        title,\n                        \" \",\n                        showArrow && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ArrowDown, {}, void 0, false, {\n                            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\nav.tsx\",\n                            lineNumber: 202,\n                            columnNumber: 37\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\nav.tsx\",\n                    lineNumber: 196,\n                    columnNumber: 13\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\nav.tsx\",\n                lineNumber: 195,\n                columnNumber: 11\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\nav.tsx\",\n            lineNumber: 190,\n            columnNumber: 9\n        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n            href: link,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `${(_home_module_scss__WEBPACK_IMPORTED_MODULE_8___default().nav__list__item)} ${(_home_module_scss__WEBPACK_IMPORTED_MODULE_8___default()[\"nav__list__item--link\"])} ${pathname === `/${link.split(\"/\")[1]}` ? (_home_module_scss__WEBPACK_IMPORTED_MODULE_8___default()[\"nav__list__item--active\"]) : \"\"}`,\n                children: [\n                    title,\n                    \" \",\n                    showArrow && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ArrowDown, {}, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\nav.tsx\",\n                        lineNumber: 215,\n                        columnNumber: 35\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\nav.tsx\",\n                lineNumber: 208,\n                columnNumber: 11\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\nav.tsx\",\n            lineNumber: 207,\n            columnNumber: 9\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\nav.tsx\",\n        lineNumber: 188,\n        columnNumber: 5\n    }, this);\n}\nfunction Language() {\n    const changeLocale = (0,_locales_client__WEBPACK_IMPORTED_MODULE_7__.useChangeLocale)();\n    const currentLocal = (0,_locales_client__WEBPACK_IMPORTED_MODULE_7__.useCurrentLocale)();\n    const t = (0,_locales_client__WEBPACK_IMPORTED_MODULE_7__.useI18n)();\n    const LangItem = ({ lang, isActive = false })=>{\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n            onClick: ()=>{\n                if (isActive) return;\n                changeLocale(lang);\n            },\n            className: `${isActive ? (_home_module_scss__WEBPACK_IMPORTED_MODULE_8___default()[\"nav__right__language__text--active\"]) : \"\"} ${(_home_module_scss__WEBPACK_IMPORTED_MODULE_8___default().nav__right__language__text)}`,\n            children: lang === \"zh\" ? t(\"chinese\") : t(\"english\")\n        }, void 0, false, {\n            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\nav.tsx\",\n            lineNumber: 236,\n            columnNumber: 7\n        }, this);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (_home_module_scss__WEBPACK_IMPORTED_MODULE_8___default().nav__right__language),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LangItem, {\n                lang: \"zh\",\n                isActive: currentLocal === \"zh\"\n            }, void 0, false, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\nav.tsx\",\n                lineNumber: 250,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                children: \"/\"\n            }, void 0, false, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\nav.tsx\",\n                lineNumber: 251,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LangItem, {\n                lang: \"en\",\n                isActive: currentLocal === \"en\"\n            }, void 0, false, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\nav.tsx\",\n                lineNumber: 252,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\nav.tsx\",\n        lineNumber: 249,\n        columnNumber: 5\n    }, this);\n}\nfunction Search() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (_home_module_scss__WEBPACK_IMPORTED_MODULE_8___default().nav__right__search),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n            src: \"/search-icon.svg\",\n            width: 20,\n            height: 20,\n            alt: \"Search icon\"\n        }, void 0, false, {\n            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\nav.tsx\",\n            lineNumber: 260,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\nav.tsx\",\n        lineNumber: 259,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/[locale]/ui/home/<USER>");

/***/ }),

/***/ "(ssr)/./app/not-found.tsx":
/*!***************************!*\
  !*** ./app/not-found.tsx ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NotFound)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_error__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/error */ \"(ssr)/./node_modules/next/error.js\");\n/* harmony import */ var next_error__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_error__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n// Render the default Next.js 404 page when a route\n// is requested that doesn't match the middleware and\n// therefore doesn't have a locale associated with it.\nfunction NotFound() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_error__WEBPACK_IMPORTED_MODULE_1___default()), {\n                statusCode: 404\n            }, void 0, false, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\not-found.tsx\",\n                lineNumber: 13,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\not-found.tsx\",\n            lineNumber: 12,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\not-found.tsx\",\n        lineNumber: 11,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9hcHAvbm90LWZvdW5kLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFFOEI7QUFFOUIsbURBQW1EO0FBQ25ELHFEQUFxRDtBQUNyRCxzREFBc0Q7QUFFdkMsU0FBU0M7SUFDdEIscUJBQ0UsOERBQUNDO1FBQUtDLE1BQUs7a0JBQ1QsNEVBQUNDO3NCQUNDLDRFQUFDSixtREFBS0E7Z0JBQUNLLFlBQVk7Ozs7Ozs7Ozs7Ozs7Ozs7QUFJM0IiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9pbWNhbV9vZmZpY2lhbF9zaXRlLy4vYXBwL25vdC1mb3VuZC50c3g/NWM4MCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCdcclxuXHJcbmltcG9ydCBFcnJvciBmcm9tICduZXh0L2Vycm9yJ1xyXG5cclxuLy8gUmVuZGVyIHRoZSBkZWZhdWx0IE5leHQuanMgNDA0IHBhZ2Ugd2hlbiBhIHJvdXRlXHJcbi8vIGlzIHJlcXVlc3RlZCB0aGF0IGRvZXNuJ3QgbWF0Y2ggdGhlIG1pZGRsZXdhcmUgYW5kXHJcbi8vIHRoZXJlZm9yZSBkb2Vzbid0IGhhdmUgYSBsb2NhbGUgYXNzb2NpYXRlZCB3aXRoIGl0LlxyXG5cclxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gTm90Rm91bmQoKSB7XHJcbiAgcmV0dXJuIChcclxuICAgIDxodG1sIGxhbmc9XCJlblwiPlxyXG4gICAgICA8Ym9keT5cclxuICAgICAgICA8RXJyb3Igc3RhdHVzQ29kZT17NDA0fSAvPlxyXG4gICAgICA8L2JvZHk+XHJcbiAgICA8L2h0bWw+XHJcbiAgKVxyXG59XHJcbiJdLCJuYW1lcyI6WyJFcnJvciIsIk5vdEZvdW5kIiwiaHRtbCIsImxhbmciLCJib2R5Iiwic3RhdHVzQ29kZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./app/not-found.tsx\n");

/***/ }),

/***/ "(ssr)/./locales/client.ts":
/*!***************************!*\
  !*** ./locales/client.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   I18nProviderClient: () => (/* binding */ I18nProviderClient),\n/* harmony export */   useChangeLocale: () => (/* binding */ useChangeLocale),\n/* harmony export */   useCurrentLocale: () => (/* binding */ useCurrentLocale),\n/* harmony export */   useI18n: () => (/* binding */ useI18n),\n/* harmony export */   useScopedI18n: () => (/* binding */ useScopedI18n)\n/* harmony export */ });\n/* harmony import */ var next_international_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-international/client */ \"(ssr)/./node_modules/next-international/dist/app/client/index.js\");\n/* harmony import */ var next_international_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_international_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst { useI18n, useScopedI18n, I18nProviderClient, useCurrentLocale, useChangeLocale } = (0,next_international_client__WEBPACK_IMPORTED_MODULE_0__.createI18nClient)({\n    en: ()=>__webpack_require__.e(/*! import() */ \"_ssr_locales_en_ts\").then(__webpack_require__.bind(__webpack_require__, /*! ./en */ \"(ssr)/./locales/en.ts\")),\n    zh: ()=>__webpack_require__.e(/*! import() */ \"_ssr_locales_zh_ts\").then(__webpack_require__.bind(__webpack_require__, /*! ./zh */ \"(ssr)/./locales/zh.ts\"))\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9sb2NhbGVzL2NsaWVudC50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBQTZEO0FBRXRELE1BQU0sRUFDWEMsT0FBTyxFQUNQQyxhQUFhLEVBQ2JDLGtCQUFrQixFQUNsQkMsZ0JBQWdCLEVBQ2hCQyxlQUFlLEVBQ2hCLEdBQUdMLDJFQUFnQkEsQ0FBQztJQUNuQk0sSUFBSSxJQUFNLG9KQUFjO0lBQ3hCQyxJQUFJLElBQU0sb0pBQWM7QUFDMUIsR0FBRyIsInNvdXJjZXMiOlsid2VicGFjazovL2ltY2FtX29mZmljaWFsX3NpdGUvLi9sb2NhbGVzL2NsaWVudC50cz9jZjQ1Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGNyZWF0ZUkxOG5DbGllbnQgfSBmcm9tIFwibmV4dC1pbnRlcm5hdGlvbmFsL2NsaWVudFwiO1xyXG5cclxuZXhwb3J0IGNvbnN0IHtcclxuICB1c2VJMThuLFxyXG4gIHVzZVNjb3BlZEkxOG4sXHJcbiAgSTE4blByb3ZpZGVyQ2xpZW50LFxyXG4gIHVzZUN1cnJlbnRMb2NhbGUsXHJcbiAgdXNlQ2hhbmdlTG9jYWxlLFxyXG59ID0gY3JlYXRlSTE4bkNsaWVudCh7XHJcbiAgZW46ICgpID0+IGltcG9ydChcIi4vZW5cIiksXHJcbiAgemg6ICgpID0+IGltcG9ydChcIi4vemhcIiksXHJcbn0pO1xyXG4iXSwibmFtZXMiOlsiY3JlYXRlSTE4bkNsaWVudCIsInVzZUkxOG4iLCJ1c2VTY29wZWRJMThuIiwiSTE4blByb3ZpZGVyQ2xpZW50IiwidXNlQ3VycmVudExvY2FsZSIsInVzZUNoYW5nZUxvY2FsZSIsImVuIiwiemgiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./locales/client.ts\n");

/***/ }),

/***/ "(rsc)/./app/[locale]/ui/components/components.scss":
/*!****************************************************!*\
  !*** ./app/[locale]/ui/components/components.scss ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"e6ee3d37fbc5\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvW2xvY2FsZV0vdWkvY29tcG9uZW50cy9jb21wb25lbnRzLnNjc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9pbWNhbV9vZmZpY2lhbF9zaXRlLy4vYXBwL1tsb2NhbGVdL3VpL2NvbXBvbmVudHMvY29tcG9uZW50cy5zY3NzPzUxMWEiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCJlNmVlM2QzN2ZiYzVcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/[locale]/ui/components/components.scss\n");

/***/ }),

/***/ "(ssr)/./app/[locale]/ui/components/components.scss":
/*!****************************************************!*\
  !*** ./app/[locale]/ui/components/components.scss ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"e6ee3d37fbc5\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9hcHAvW2xvY2FsZV0vdWkvY29tcG9uZW50cy9jb21wb25lbnRzLnNjc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9pbWNhbV9vZmZpY2lhbF9zaXRlLy4vYXBwL1tsb2NhbGVdL3VpL2NvbXBvbmVudHMvY29tcG9uZW50cy5zY3NzPzQ4MzAiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCJlNmVlM2QzN2ZiYzVcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./app/[locale]/ui/components/components.scss\n");

/***/ }),

/***/ "(rsc)/./app/globals.scss":
/*!**************************!*\
  !*** ./app/globals.scss ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"dabe1292f855\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5zY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vaW1jYW1fb2ZmaWNpYWxfc2l0ZS8uL2FwcC9nbG9iYWxzLnNjc3M/YWZhZCJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImRhYmUxMjkyZjg1NVwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.scss\n");

/***/ }),

/***/ "(rsc)/./app/[locale]/page.module.scss":
/*!***************************************!*\
  !*** ./app/[locale]/page.module.scss ***!
  \***************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"page404container\": \"page_page404container__c1LIB\",\n\t\"page404\": \"page_page404__1cE3u\",\n\t\"page404__content\": \"page_page404__content__oco9m\",\n\t\"page404__buttons\": \"page_page404__buttons__8oWnx\",\n\t\"page404__buttons__back\": \"page_page404__buttons__back__z0MU7\",\n\t\"page404__buttons__home\": \"page_page404__buttons__home__sx9Sv\"\n};\n\nmodule.exports.__checksum = \"346715d5ce1e\"\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvW2xvY2FsZV0vcGFnZS5tb2R1bGUuc2NzcyIsIm1hcHBpbmdzIjoiQUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUEseUJBQXlCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vaW1jYW1fb2ZmaWNpYWxfc2l0ZS8uL2FwcC9bbG9jYWxlXS9wYWdlLm1vZHVsZS5zY3NzPzQ0MWUiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gRXhwb3J0c1xubW9kdWxlLmV4cG9ydHMgPSB7XG5cdFwicGFnZTQwNGNvbnRhaW5lclwiOiBcInBhZ2VfcGFnZTQwNGNvbnRhaW5lcl9fYzFMSUJcIixcblx0XCJwYWdlNDA0XCI6IFwicGFnZV9wYWdlNDA0X18xY0UzdVwiLFxuXHRcInBhZ2U0MDRfX2NvbnRlbnRcIjogXCJwYWdlX3BhZ2U0MDRfX2NvbnRlbnRfX29jbzltXCIsXG5cdFwicGFnZTQwNF9fYnV0dG9uc1wiOiBcInBhZ2VfcGFnZTQwNF9fYnV0dG9uc19fOG9XbnhcIixcblx0XCJwYWdlNDA0X19idXR0b25zX19iYWNrXCI6IFwicGFnZV9wYWdlNDA0X19idXR0b25zX19iYWNrX196ME1VN1wiLFxuXHRcInBhZ2U0MDRfX2J1dHRvbnNfX2hvbWVcIjogXCJwYWdlX3BhZ2U0MDRfX2J1dHRvbnNfX2hvbWVfX3N4OVN2XCJcbn07XG5cbm1vZHVsZS5leHBvcnRzLl9fY2hlY2tzdW0gPSBcIjM0NjcxNWQ1Y2UxZVwiXG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/[locale]/page.module.scss\n");

/***/ }),

/***/ "(rsc)/./app/[locale]/ui/home/<USER>":
/*!***********************************************!*\
  !*** ./app/[locale]/ui/home/<USER>
  \***********************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"nav--scrolled\": \"home_nav--scrolled__f5oaX\",\n\t\"nav\": \"home_nav__gr65i\",\n\t\"nav__placeholder\": \"home_nav__placeholder__R_bDj\",\n\t\"nav__content\": \"home_nav__content__gXoig\",\n\t\"nav__list\": \"home_nav__list__dmRBz\",\n\t\"nav__list__item\": \"home_nav__list__item__Ti9E4\",\n\t\"nav__list__item--link\": \"home_nav__list__item--link__dx88I\",\n\t\"nav__list__item--active\": \"home_nav__list__item--active__oPRJX\",\n\t\"nav__right\": \"home_nav__right__4GRXj\",\n\t\"nav__right__language\": \"home_nav__right__language__YzU4O\",\n\t\"nav__right__language__text\": \"home_nav__right__language__text__yUNmB\",\n\t\"nav__right__language__text--active\": \"home_nav__right__language__text--active__e4h1y\",\n\t\"nav__right__search\": \"home_nav__right__search__QAvd_\",\n\t\"nav__right__menu\": \"home_nav__right__menu__tMG4s\",\n\t\"nav__drop\": \"home_nav__drop__RNd3y\",\n\t\"nav__mask\": \"home_nav__mask__YVj5E\",\n\t\"banner-slider\": \"home_banner-slider__UBj9I\",\n\t\"banner-slider__swiper\": \"home_banner-slider__swiper__9Bl8q\",\n\t\"banner-slider__slide\": \"home_banner-slider__slide__2U7Uu\",\n\t\"banner-slider__button\": \"home_banner-slider__button__GKjGy\",\n\t\"swiper-button-disabled\": \"home_swiper-button-disabled__siaDk\",\n\t\"banner-slider__button-prev\": \"home_banner-slider__button-prev__VeikB\",\n\t\"banner-slider__button-next\": \"home_banner-slider__button-next__UC5d2\",\n\t\"banner-slider__pagination\": \"home_banner-slider__pagination__J58et\",\n\t\"banner-slider__bullet\": \"home_banner-slider__bullet__c3a9X\",\n\t\"banner-slider__bullet--active\": \"home_banner-slider__bullet--active__5BpSZ\",\n\t\"banner-slider__switcher\": \"home_banner-slider__switcher__SoaxS\",\n\t\"banner-slider__switcher--right\": \"home_banner-slider__switcher--right___84yN\",\n\t\"banner-slider__indicator\": \"home_banner-slider__indicator__0OOU4\",\n\t\"banner-slider__indicator__item\": \"home_banner-slider__indicator__item__f8vBh\",\n\t\"hot-spot\": \"home_hot-spot__HmXBc\",\n\t\"hot-spot__captain\": \"home_hot-spot__captain__P7sAg\",\n\t\"hot-spot__captain__more\": \"home_hot-spot__captain__more__hoe30\",\n\t\"hot-spot__news\": \"home_hot-spot__news__mFPbX\",\n\t\"hot-spot__news__left\": \"home_hot-spot__news__left__bYNbF\",\n\t\"hot-spot__news__right\": \"home_hot-spot__news__right__IYxxG\",\n\t\"hot-spot__news__item\": \"home_hot-spot__news__item__i6svw\",\n\t\"hot-spot__news__item__info\": \"home_hot-spot__news__item__info__GSDkz\",\n\t\"hot-spot__news__item__image\": \"home_hot-spot__news__item__image__0Dj0A\",\n\t\"hot-spot__news__item__image--right\": \"home_hot-spot__news__item__image--right__scey9\",\n\t\"hot-spot__news__item--left\": \"home_hot-spot__news__item--left__W7YL9\",\n\t\"about\": \"home_about__vPbFi\",\n\t\"about__cover\": \"home_about__cover__SPvuD\",\n\t\"about__content\": \"home_about__content__EA9EW\",\n\t\"about__content__time\": \"home_about__content__time__HcHq6\",\n\t\"about__content__time__item\": \"home_about__content__time__item__n4W8C\",\n\t\"about__content__time--page\": \"home_about__content__time--page__Azkeq\",\n\t\"about__content__prides\": \"home_about__content__prides__zHCpT\",\n\t\"contacts\": \"home_contacts__TRH4N\",\n\t\"contacts--page\": \"home_contacts--page__0BV0w\",\n\t\"contacts__info\": \"home_contacts__info__pIGy0\",\n\t\"contacts__info__items\": \"home_contacts__info__items__qUSi9\",\n\t\"contacts__info__title\": \"home_contacts__info__title__3_UHT\",\n\t\"contacts__info__item\": \"home_contacts__info__item__eDIm0\",\n\t\"contacts__address\": \"home_contacts__address___ZQdr\",\n\t\"footer\": \"home_footer__qefFZ\",\n\t\"footer__logo\": \"home_footer__logo__jG71u\",\n\t\"footer__links\": \"home_footer__links__q5uiZ\",\n\t\"footer__links__item\": \"home_footer__links__item__gB0TO\",\n\t\"footer__links__follow\": \"home_footer__links__follow__jv8nP\",\n\t\"footer__links__follow__weixin\": \"home_footer__links__follow__weixin__yeCNp\",\n\t\"footer__copyright\": \"home_footer__copyright__M6lua\",\n\t\"footer__copyright__link\": \"home_footer__copyright__link__PBT0B\",\n\t\"banner-slider__indicator__item--active\": \"home_banner-slider__indicator__item--active__Pkcak\"\n};\n\nmodule.exports.__checksum = \"b3f4251f6407\"\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/[locale]/ui/home/<USER>");

/***/ }),

/***/ "(ssr)/./app/[locale]/ui/home/<USER>":
/*!***********************************************!*\
  !*** ./app/[locale]/ui/home/<USER>
  \***********************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"nav--scrolled\": \"home_nav--scrolled__f5oaX\",\n\t\"nav\": \"home_nav__gr65i\",\n\t\"nav__placeholder\": \"home_nav__placeholder__R_bDj\",\n\t\"nav__content\": \"home_nav__content__gXoig\",\n\t\"nav__list\": \"home_nav__list__dmRBz\",\n\t\"nav__list__item\": \"home_nav__list__item__Ti9E4\",\n\t\"nav__list__item--link\": \"home_nav__list__item--link__dx88I\",\n\t\"nav__list__item--active\": \"home_nav__list__item--active__oPRJX\",\n\t\"nav__right\": \"home_nav__right__4GRXj\",\n\t\"nav__right__language\": \"home_nav__right__language__YzU4O\",\n\t\"nav__right__language__text\": \"home_nav__right__language__text__yUNmB\",\n\t\"nav__right__language__text--active\": \"home_nav__right__language__text--active__e4h1y\",\n\t\"nav__right__search\": \"home_nav__right__search__QAvd_\",\n\t\"nav__right__menu\": \"home_nav__right__menu__tMG4s\",\n\t\"nav__drop\": \"home_nav__drop__RNd3y\",\n\t\"nav__mask\": \"home_nav__mask__YVj5E\",\n\t\"banner-slider\": \"home_banner-slider__UBj9I\",\n\t\"banner-slider__swiper\": \"home_banner-slider__swiper__9Bl8q\",\n\t\"banner-slider__slide\": \"home_banner-slider__slide__2U7Uu\",\n\t\"banner-slider__button\": \"home_banner-slider__button__GKjGy\",\n\t\"swiper-button-disabled\": \"home_swiper-button-disabled__siaDk\",\n\t\"banner-slider__button-prev\": \"home_banner-slider__button-prev__VeikB\",\n\t\"banner-slider__button-next\": \"home_banner-slider__button-next__UC5d2\",\n\t\"banner-slider__pagination\": \"home_banner-slider__pagination__J58et\",\n\t\"banner-slider__bullet\": \"home_banner-slider__bullet__c3a9X\",\n\t\"banner-slider__bullet--active\": \"home_banner-slider__bullet--active__5BpSZ\",\n\t\"banner-slider__switcher\": \"home_banner-slider__switcher__SoaxS\",\n\t\"banner-slider__switcher--right\": \"home_banner-slider__switcher--right___84yN\",\n\t\"banner-slider__indicator\": \"home_banner-slider__indicator__0OOU4\",\n\t\"banner-slider__indicator__item\": \"home_banner-slider__indicator__item__f8vBh\",\n\t\"hot-spot\": \"home_hot-spot__HmXBc\",\n\t\"hot-spot__captain\": \"home_hot-spot__captain__P7sAg\",\n\t\"hot-spot__captain__more\": \"home_hot-spot__captain__more__hoe30\",\n\t\"hot-spot__news\": \"home_hot-spot__news__mFPbX\",\n\t\"hot-spot__news__left\": \"home_hot-spot__news__left__bYNbF\",\n\t\"hot-spot__news__right\": \"home_hot-spot__news__right__IYxxG\",\n\t\"hot-spot__news__item\": \"home_hot-spot__news__item__i6svw\",\n\t\"hot-spot__news__item__info\": \"home_hot-spot__news__item__info__GSDkz\",\n\t\"hot-spot__news__item__image\": \"home_hot-spot__news__item__image__0Dj0A\",\n\t\"hot-spot__news__item__image--right\": \"home_hot-spot__news__item__image--right__scey9\",\n\t\"hot-spot__news__item--left\": \"home_hot-spot__news__item--left__W7YL9\",\n\t\"about\": \"home_about__vPbFi\",\n\t\"about__cover\": \"home_about__cover__SPvuD\",\n\t\"about__content\": \"home_about__content__EA9EW\",\n\t\"about__content__time\": \"home_about__content__time__HcHq6\",\n\t\"about__content__time__item\": \"home_about__content__time__item__n4W8C\",\n\t\"about__content__time--page\": \"home_about__content__time--page__Azkeq\",\n\t\"about__content__prides\": \"home_about__content__prides__zHCpT\",\n\t\"contacts\": \"home_contacts__TRH4N\",\n\t\"contacts--page\": \"home_contacts--page__0BV0w\",\n\t\"contacts__info\": \"home_contacts__info__pIGy0\",\n\t\"contacts__info__items\": \"home_contacts__info__items__qUSi9\",\n\t\"contacts__info__title\": \"home_contacts__info__title__3_UHT\",\n\t\"contacts__info__item\": \"home_contacts__info__item__eDIm0\",\n\t\"contacts__address\": \"home_contacts__address___ZQdr\",\n\t\"footer\": \"home_footer__qefFZ\",\n\t\"footer__logo\": \"home_footer__logo__jG71u\",\n\t\"footer__links\": \"home_footer__links__q5uiZ\",\n\t\"footer__links__item\": \"home_footer__links__item__gB0TO\",\n\t\"footer__links__follow\": \"home_footer__links__follow__jv8nP\",\n\t\"footer__links__follow__weixin\": \"home_footer__links__follow__weixin__yeCNp\",\n\t\"footer__copyright\": \"home_footer__copyright__M6lua\",\n\t\"footer__copyright__link\": \"home_footer__copyright__link__PBT0B\",\n\t\"banner-slider__indicator__item--active\": \"home_banner-slider__indicator__item--active__Pkcak\"\n};\n\nmodule.exports.__checksum = \"b3f4251f6407\"\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/[locale]/ui/home/<USER>");

/***/ }),

/***/ "(rsc)/./app/[locale]/(overview)/page.tsx":
/*!******************************************!*\
  !*** ./app/[locale]/(overview)/page.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home),\n/* harmony export */   generateMetadata: () => (/* binding */ generateMetadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _app_locale_ui_home_banner_slider__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/app/[locale]/ui/home/<USER>/ \"(rsc)/./app/[locale]/ui/home/<USER>");\n/* harmony import */ var _app_locale_ui_home_hot_spot__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/app/[locale]/ui/home/<USER>/ \"(rsc)/./app/[locale]/ui/home/<USER>");\n/* harmony import */ var _app_locale_ui_home_about__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/app/[locale]/ui/home/<USER>/ \"(rsc)/./app/[locale]/ui/home/<USER>");\n\n\n\n\nasync function generateMetadata({ params, searchParams }, parent) {\n    const locale = params.locale;\n    return {\n        title: locale === \"zh\" ? \"深圳市赛蓝科技有限公司-智能家居品牌-看家王智能摄像头-赛蓝加菲狗产品网站\" : \"Cylan - Cylan Clever Dog English Site\",\n        description: \"赛蓝科技 引领生活\",\n        icons: {\n            icon: \"/favicon.ico\"\n        }\n    };\n}\nfunction Home() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_locale_ui_home_banner_slider__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {}, void 0, false, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\(overview)\\\\page.tsx\",\n                lineNumber: 29,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_locale_ui_home_hot_spot__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\(overview)\\\\page.tsx\",\n                lineNumber: 30,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_locale_ui_home_about__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\(overview)\\\\page.tsx\",\n                lineNumber: 31,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/[locale]/(overview)/page.tsx\n");

/***/ }),

/***/ "(rsc)/./app/[locale]/layout.tsx":
/*!*********************************!*\
  !*** ./app/[locale]/layout.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   generateMetadata: () => (/* binding */ generateMetadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_locale_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\[locale]\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\[locale]\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_locale_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_locale_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _app_globals_scss__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/app/globals.scss */ \"(rsc)/./app/globals.scss\");\n/* harmony import */ var _app_locale_ui_home_footer__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/app/[locale]/ui/home/<USER>/ \"(rsc)/./app/[locale]/ui/home/<USER>");\n/* harmony import */ var _app_locale_ui_home_nav__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/app/[locale]/ui/home/<USER>/ \"(rsc)/./app/[locale]/ui/home/<USER>");\n/* harmony import */ var _app_locale_ui_components_back_to_top__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/[locale]/ui/components/back-to-top */ \"(rsc)/./app/[locale]/ui/components/back-to-top.tsx\");\n\n\n\n\n\n\nasync function generateMetadata({ params, searchParams }, parent) {\n    const locale = params.locale;\n    return {\n        description: \"赛蓝科技 引领生活\",\n        icons: {\n            icon: \"/favicon.ico\"\n        }\n    };\n}\nasync function RootLayout({ children, params }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: params.locale,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_app_locale_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5___default().className),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_locale_ui_home_nav__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\layout.tsx\",\n                        lineNumber: 34,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\layout.tsx\",\n                    lineNumber: 33,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                    children: children\n                }, void 0, false, {\n                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\layout.tsx\",\n                    lineNumber: 36,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_locale_ui_home_footer__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\layout.tsx\",\n                        lineNumber: 38,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\layout.tsx\",\n                    lineNumber: 37,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_locale_ui_components_back_to_top__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\layout.tsx\",\n                    lineNumber: 40,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\layout.tsx\",\n            lineNumber: 32,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\layout.tsx\",\n        lineNumber: 31,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/[locale]/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/[locale]/not-found.tsx":
/*!************************************!*\
  !*** ./app/[locale]/not-found.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NotFound)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _app_locale_page_module_scss__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/[locale]/page.module.scss */ \"(rsc)/./app/[locale]/page.module.scss\");\n/* harmony import */ var _app_locale_page_module_scss__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_app_locale_page_module_scss__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ \"(rsc)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _locales_server__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/locales/server */ \"(rsc)/./locales/server.ts\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(rsc)/./node_modules/next/dist/api/link.js\");\n\n\n\n\n\nasync function NotFound() {\n    const t = await (0,_locales_server__WEBPACK_IMPORTED_MODULE_2__.getI18n)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (_app_locale_page_module_scss__WEBPACK_IMPORTED_MODULE_4___default().page404container),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: (_app_locale_page_module_scss__WEBPACK_IMPORTED_MODULE_4___default().page404),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_app_locale_page_module_scss__WEBPACK_IMPORTED_MODULE_4___default().page404__image),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                        src: \"/404.png\",\n                        width: 331,\n                        height: 151,\n                        alt: \"\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\not-found.tsx\",\n                        lineNumber: 13,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\not-found.tsx\",\n                    lineNumber: 12,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_app_locale_page_module_scss__WEBPACK_IMPORTED_MODULE_4___default().page404__content),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: t(\"page404Description\")\n                        }, void 0, false, {\n                            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\not-found.tsx\",\n                            lineNumber: 16,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: t(\"page404Tip1\")\n                        }, void 0, false, {\n                            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\not-found.tsx\",\n                            lineNumber: 17,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: t(\"page404Tip2\")\n                        }, void 0, false, {\n                            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\not-found.tsx\",\n                            lineNumber: 18,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\not-found.tsx\",\n                    lineNumber: 15,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_app_locale_page_module_scss__WEBPACK_IMPORTED_MODULE_4___default().page404__buttons),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        href: \"/\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: (_app_locale_page_module_scss__WEBPACK_IMPORTED_MODULE_4___default().page404__buttons__home),\n                            children: t(\"backToHome\")\n                        }, void 0, false, {\n                            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\not-found.tsx\",\n                            lineNumber: 22,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\not-found.tsx\",\n                        lineNumber: 21,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\not-found.tsx\",\n                    lineNumber: 20,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\not-found.tsx\",\n            lineNumber: 11,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\not-found.tsx\",\n        lineNumber: 10,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/[locale]/not-found.tsx\n");

/***/ }),

/***/ "(rsc)/./app/[locale]/ui/components/back-to-top.tsx":
/*!****************************************************!*\
  !*** ./app/[locale]/ui/components/back-to-top.tsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\---AProjects---\imcam-officialsite\fastoffical\app\[locale]\ui\components\back-to-top.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./app/[locale]/ui/components/flex-4items-box.tsx":
/*!********************************************************!*\
  !*** ./app/[locale]/ui/components/flex-4items-box.tsx ***!
  \********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Flex4ItemsBox),\n/* harmony export */   itemsMode: () => (/* binding */ itemsMode)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ \"(rsc)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_scss__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./components.scss */ \"(rsc)/./app/[locale]/ui/components/components.scss\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/link */ \"(rsc)/./node_modules/next/dist/api/link.js\");\n\n\n\n\n\nvar itemsMode;\n(function(itemsMode) {\n    itemsMode[\"normal\"] = \"\";\n    itemsMode[\"product\"] = \"product\";\n    itemsMode[\"pride\"] = \"pride\";\n})(itemsMode || (itemsMode = {}));\nfunction Flex4ItemsBox({ infos, imageSize, imageBox, mode = \"\", gap = 0, isDetail = false }) {\n    const renderCard = ({ imageSrc, title, tip = \"\", link = \"\", videoSrc = \"\" })=>{\n        const isLinkCard = !!link;\n        const isVideoCard = !!videoSrc;\n        const cardClassName = `flex-box-with-4items__card ${mode === \"product\" ? \"flex-box-with-4items__card--product\" : \"\"} ${isLinkCard ? \"flex-box-with-4items__card--link\" : \"\"} ${isVideoCard ? \"flex-box-with-4items__card--video\" : \"\"}`;\n        const infoClassName = `${mode === \"\" ? \"\" : `flex-box-with-4items__card__info--${mode}`} flex-box-with-4items__card__info`;\n        const cardContent = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-box-with-4items__card__image\",\n                    style: {\n                        aspectRatio: imageBox.width / imageBox.height\n                    },\n                    children: isVideoCard ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"video\", {\n                        width: imageSize.width,\n                        height: imageSize.height,\n                        controls: true,\n                        poster: imageSrc,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"source\", {\n                                src: videoSrc,\n                                type: \"video/mp4\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\flex-4items-box.tsx\",\n                                lineNumber: 76,\n                                columnNumber: 15\n                            }, this),\n                            \"Your browser does not support the video tag.\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\flex-4items-box.tsx\",\n                        lineNumber: 70,\n                        columnNumber: 13\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                        unoptimized: true,\n                        src: imageSrc,\n                        width: imageSize.width,\n                        height: imageSize.height,\n                        alt: \"image\",\n                        style: {\n                            objectFit: \"fill\",\n                            width: \"100%\",\n                            height: \"auto\"\n                        }\n                    }, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\flex-4items-box.tsx\",\n                        lineNumber: 80,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\flex-4items-box.tsx\",\n                    lineNumber: 65,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: infoClassName,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: title\n                        }, void 0, false, {\n                            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\flex-4items-box.tsx\",\n                            lineNumber: 95,\n                            columnNumber: 11\n                        }, this),\n                        mode === \"pride\" ? \"\" : tip ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: tip\n                        }, void 0, false, {\n                            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\flex-4items-box.tsx\",\n                            lineNumber: 96,\n                            columnNumber: 50\n                        }, this) : \"\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\flex-4items-box.tsx\",\n                    lineNumber: 94,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true);\n        return isLinkCard ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n            href: link,\n            className: cardClassName,\n            children: cardContent\n        }, void 0, false, {\n            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\flex-4items-box.tsx\",\n            lineNumber: 102,\n            columnNumber: 7\n        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: cardClassName,\n            children: cardContent\n        }, void 0, false, {\n            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\flex-4items-box.tsx\",\n            lineNumber: 106,\n            columnNumber: 7\n        }, this);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `${`flex-box-with-4items`} ${mode === \"product\" ? \"flex-box-with-4items--product\" : \"\"} ${isDetail ? \"flex-box-with-4items--detail\" : \"\"}`,\n        style: gap ? {\n            gap\n        } : {},\n        children: infos.map((info, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((react__WEBPACK_IMPORTED_MODULE_2___default().Fragment), {\n                children: renderCard(info)\n            }, `${info.link}-${index}`, false, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\flex-4items-box.tsx\",\n                lineNumber: 117,\n                columnNumber: 9\n            }, this))\n    }, void 0, false, {\n        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\flex-4items-box.tsx\",\n        lineNumber: 110,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/[locale]/ui/components/flex-4items-box.tsx\n");

/***/ }),

/***/ "(rsc)/./app/[locale]/ui/components/show-more.tsx":
/*!**************************************************!*\
  !*** ./app/[locale]/ui/components/show-more.tsx ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ShowMore)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(rsc)/./node_modules/next/dist/api/link.js\");\n\n\nfunction ShowMore({ text, link }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n        href: link,\n        className: \"hide-on-medium hide-on-large\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n            className: `show-more`,\n            children: text\n        }, void 0, false, {\n            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\show-more.tsx\",\n            lineNumber: 12,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\show-more.tsx\",\n        lineNumber: 11,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvW2xvY2FsZV0vdWkvY29tcG9uZW50cy9zaG93LW1vcmUudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQTRCO0FBRWIsU0FBU0MsU0FBUyxFQUMvQkMsSUFBSSxFQUNKQyxJQUFJLEVBSUw7SUFDQyxxQkFDRSw4REFBQ0gsaURBQUlBO1FBQUNJLE1BQU1EO1FBQU1FLFdBQVU7a0JBQzFCLDRFQUFDQztZQUFPRCxXQUFXLENBQUMsU0FBUyxDQUFDO3NCQUFHSDs7Ozs7Ozs7Ozs7QUFHdkMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9pbWNhbV9vZmZpY2lhbF9zaXRlLy4vYXBwL1tsb2NhbGVdL3VpL2NvbXBvbmVudHMvc2hvdy1tb3JlLnRzeD8xZjE1Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBMaW5rIGZyb20gJ25leHQvbGluaydcclxuXHJcbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFNob3dNb3JlKHtcclxuICB0ZXh0LFxyXG4gIGxpbmssXHJcbn06IHtcclxuICB0ZXh0OiBzdHJpbmdcclxuICBsaW5rOiBzdHJpbmdcclxufSkge1xyXG4gIHJldHVybiAoXHJcbiAgICA8TGluayBocmVmPXtsaW5rfSBjbGFzc05hbWU9XCJoaWRlLW9uLW1lZGl1bSBoaWRlLW9uLWxhcmdlXCI+XHJcbiAgICAgIDxidXR0b24gY2xhc3NOYW1lPXtgc2hvdy1tb3JlYH0+e3RleHR9PC9idXR0b24+XHJcbiAgICA8L0xpbms+XHJcbiAgKVxyXG59XHJcbiJdLCJuYW1lcyI6WyJMaW5rIiwiU2hvd01vcmUiLCJ0ZXh0IiwibGluayIsImhyZWYiLCJjbGFzc05hbWUiLCJidXR0b24iXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/[locale]/ui/components/show-more.tsx\n");

/***/ }),

/***/ "(rsc)/./app/[locale]/ui/home/<USER>":
/*!****************************************!*\
  !*** ./app/[locale]/ui/home/<USER>
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   CompanyTime: () => (/* binding */ e0),
/* harmony export */   Contacts: () => (/* binding */ e1),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\---AProjects---\imcam-officialsite\fastoffical\app\[locale]\ui\home\about.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);
const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\---AProjects---\imcam-officialsite\fastoffical\app\[locale]\ui\home\about.tsx#CompanyTime`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\---AProjects---\imcam-officialsite\fastoffical\app\[locale]\ui\home\about.tsx#Contacts`);


/***/ }),

/***/ "(rsc)/./app/[locale]/ui/home/<USER>":
/*!************************************************!*\
  !*** ./app/[locale]/ui/home/<USER>
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\---AProjects---\imcam-officialsite\fastoffical\app\[locale]\ui\home\banner-slider.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./app/[locale]/ui/home/<USER>":
/*!*****************************************!*\
  !*** ./app/[locale]/ui/home/<USER>
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\---AProjects---\imcam-officialsite\fastoffical\app\[locale]\ui\home\footer.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./app/[locale]/ui/home/<USER>":
/*!*******************************************!*\
  !*** ./app/[locale]/ui/home/<USER>
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HotSpot)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _utils_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/utils/utils */ \"(rsc)/./utils/utils.ts\");\n/* harmony import */ var _data_news__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/data/news */ \"(rsc)/./data/news.ts\");\n/* harmony import */ var _home_module_scss__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./home.module.scss */ \"(rsc)/./app/[locale]/ui/home/<USER>");\n/* harmony import */ var _home_module_scss__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(_home_module_scss__WEBPACK_IMPORTED_MODULE_9__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(rsc)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/image */ \"(rsc)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _components_flex_4items_box__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../components/flex-4items-box */ \"(rsc)/./app/[locale]/ui/components/flex-4items-box.tsx\");\n/* harmony import */ var _components_show_more__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../components/show-more */ \"(rsc)/./app/[locale]/ui/components/show-more.tsx\");\n/* harmony import */ var _data_products__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/data/products */ \"(rsc)/./data/products.ts\");\n/* harmony import */ var _locales_server__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/locales/server */ \"(rsc)/./locales/server.ts\");\n\n\n\n\n\n\n\n\n\n\nasync function HotSpot() {\n    const t = await (0,_locales_server__WEBPACK_IMPORTED_MODULE_8__.getI18n)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_home_module_scss__WEBPACK_IMPORTED_MODULE_9___default())[`hot-spot`],\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Captain, {\n                        title: t(\"productCenter\"),\n                        link: \"/product\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\hot-spot.tsx\",\n                        lineNumber: 26,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Products, {}, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\hot-spot.tsx\",\n                        lineNumber: 27,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\hot-spot.tsx\",\n                lineNumber: 25,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_home_module_scss__WEBPACK_IMPORTED_MODULE_9___default())[`hot-spot`],\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Captain, {\n                        title: t(\"prodoctVideos\")\n                    }, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\hot-spot.tsx\",\n                        lineNumber: 30,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Videos, {}, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\hot-spot.tsx\",\n                        lineNumber: 31,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\hot-spot.tsx\",\n                lineNumber: 29,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\hot-spot.tsx\",\n        lineNumber: 20,\n        columnNumber: 5\n    }, this);\n}\nasync function Captain({ title, link }) {\n    const t = await (0,_locales_server__WEBPACK_IMPORTED_MODULE_8__.getI18n)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (_home_module_scss__WEBPACK_IMPORTED_MODULE_9___default()[\"hot-spot__captain\"]),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                children: title\n            }, void 0, false, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\hot-spot.tsx\",\n                lineNumber: 42,\n                columnNumber: 7\n            }, this),\n            link && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                href: link,\n                className: `${(_home_module_scss__WEBPACK_IMPORTED_MODULE_9___default()[\"hot-spot__captain__more\"])} hide-on-small`,\n                children: [\n                    t(\"seeMore\"),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        src: \"/arrow-right.svg\",\n                        width: 10,\n                        height: 17,\n                        alt: \"arrow\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\hot-spot.tsx\",\n                        lineNumber: 49,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\hot-spot.tsx\",\n                lineNumber: 44,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\hot-spot.tsx\",\n        lineNumber: 41,\n        columnNumber: 5\n    }, this);\n}\nasync function News() {\n    const t = await (0,_locales_server__WEBPACK_IMPORTED_MODULE_8__.getI18n)();\n    const ItemInfo = ({ title, content, time, isRight = false })=>{\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: (_home_module_scss__WEBPACK_IMPORTED_MODULE_9___default())[`hot-spot__news__item__info`],\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                    children: title\n                }, void 0, false, {\n                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\hot-spot.tsx\",\n                    lineNumber: 76,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: content\n                }, void 0, false, {\n                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\hot-spot.tsx\",\n                    lineNumber: 77,\n                    columnNumber: 9\n                }, this),\n                isRight && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"hide-on-small\",\n                    style: {\n                        flex: 1\n                    }\n                }, void 0, false, {\n                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\hot-spot.tsx\",\n                    lineNumber: 78,\n                    columnNumber: 21\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    children: time\n                }, void 0, false, {\n                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\hot-spot.tsx\",\n                    lineNumber: 79,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\hot-spot.tsx\",\n            lineNumber: 75,\n            columnNumber: 7\n        }, this);\n    };\n    const NewsItem = ({ title, content, time, isRight = false, id })=>{\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            href: `/article/${id}`,\n            className: `${(_home_module_scss__WEBPACK_IMPORTED_MODULE_9___default()[\"hot-spot__news__item\"])} ${isRight ? \"\" : (_home_module_scss__WEBPACK_IMPORTED_MODULE_9___default()[\"hot-spot__news__item--left\"])}`,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: `${(_home_module_scss__WEBPACK_IMPORTED_MODULE_9___default()[\"hot-spot__news__item__image\"])} ${(_home_module_scss__WEBPACK_IMPORTED_MODULE_9___default())[`hot-spot__news__item__image--${isRight ? \"right\" : \"left\"}`]}`,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        src: \"/hotspot-image-1.png\",\n                        width: isRight ? 240 : 625,\n                        height: isRight ? 160 : 240,\n                        alt: \"news\",\n                        style: {\n                            width: \"100%\",\n                            height: \"100%\",\n                            objectFit: \"cover\",\n                            objectPosition: \"center\"\n                        }\n                    }, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\hot-spot.tsx\",\n                        lineNumber: 107,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\hot-spot.tsx\",\n                    lineNumber: 103,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ItemInfo, {\n                    title: title,\n                    content: content,\n                    time: time,\n                    isRight: isRight\n                }, void 0, false, {\n                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\hot-spot.tsx\",\n                    lineNumber: 121,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\hot-spot.tsx\",\n            lineNumber: 98,\n            columnNumber: 7\n        }, this);\n    };\n    const newsItemsRes = await (0,_data_news__WEBPACK_IMPORTED_MODULE_2__[\"default\"])();\n    const newsItems = newsItemsRes.filter((_, index)=>index < 3).map((news, index)=>{\n        const itemLeft = {\n            title: news.title,\n            content: news.tip,\n            time: (0,_utils_utils__WEBPACK_IMPORTED_MODULE_1__.formatDate)(news.time, \"YYYY-MM-DD\"),\n            id: news.id\n        };\n        const itemRight = {\n            ...itemLeft,\n            isRight: true\n        };\n        if (index === 0) return itemLeft;\n        else return itemRight;\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_home_module_scss__WEBPACK_IMPORTED_MODULE_9___default()[\"hot-spot__news\"]),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_home_module_scss__WEBPACK_IMPORTED_MODULE_9___default()[\"hot-spot__news__left\"]),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NewsItem, {\n                            ...newsItems[0]\n                        }, void 0, false, {\n                            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\hot-spot.tsx\",\n                            lineNumber: 163,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\hot-spot.tsx\",\n                        lineNumber: 162,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_home_module_scss__WEBPACK_IMPORTED_MODULE_9___default()[\"hot-spot__news__right\"]),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NewsItem, {\n                                ...newsItems[1]\n                            }, void 0, false, {\n                                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\hot-spot.tsx\",\n                                lineNumber: 166,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NewsItem, {\n                                ...newsItems[2]\n                            }, void 0, false, {\n                                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\hot-spot.tsx\",\n                                lineNumber: 167,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\hot-spot.tsx\",\n                        lineNumber: 165,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\hot-spot.tsx\",\n                lineNumber: 161,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_show_more__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                text: \"查看更多新闻\",\n                link: \"/news\"\n            }, void 0, false, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\hot-spot.tsx\",\n                lineNumber: 170,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\nasync function Products() {\n    const locale = await (0,_locales_server__WEBPACK_IMPORTED_MODULE_8__.getCurrentLocale)();\n    const productDatas = await (0,_data_products__WEBPACK_IMPORTED_MODULE_7__.getProductDatas)();\n    const infos = productDatas.filter((item, index)=>[\n            \"c31\",\n            \"02\",\n            \"04\",\n            \"t1pro\"\n        ].includes(item.id)).map((item)=>{\n        const product = {\n            imageSrc: item.imageSrc,\n            title: locale === \"zh\" ? item.name : item.nameEn,\n            tip: locale === \"zh\" ? item.description : item.descriptionEn,\n            link: item.id === \"c31\" ? `/product/c31` : item.id === \"t1pro\" ? \"/product/t1pro\" : \"\"\n        };\n        return product;\n    });\n    const ProductsInfo = {\n        infos,\n        imageSize: {\n            width: 200,\n            height: 200\n        },\n        imageBox: {\n            width: 300,\n            height: 300\n        },\n        mode: _components_flex_4items_box__WEBPACK_IMPORTED_MODULE_5__.itemsMode.product\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_flex_4items_box__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                ...ProductsInfo\n            }, void 0, false, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\hot-spot.tsx\",\n                lineNumber: 217,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_show_more__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                text: \"查看更多产品\",\n                link: \"/product\"\n            }, void 0, false, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\hot-spot.tsx\",\n                lineNumber: 218,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\nasync function Videos() {\n    const t = await (0,_locales_server__WEBPACK_IMPORTED_MODULE_8__.getI18n)();\n    const locale = await (0,_locales_server__WEBPACK_IMPORTED_MODULE_8__.getCurrentLocale)();\n    const VideosInfo = {\n        infos: [\n            {\n                imageSrc: \"/videos/phone-call-device-cover.webp\",\n                title: locale === \"zh\" ? \"双向视频通话演示\" : \"Two-Way Video Call Demonstration\",\n                videoSrc: \"/videos/device-call-phone-video1.mp4\"\n            },\n            {\n                imageSrc: \"/videos/tx-video-call-cover.webp\",\n                title: locale === \"zh\" ? \"腾讯云音视频微通话（TWeCall）设备通话演示\" : \"Tencent Cloud Audio and Video WeCall (TWeCall) Device Call Demonstration\",\n                videoSrc: \"/videos/wx-twecall-video1.mp4\"\n            },\n            {\n                imageSrc: \"/videos/voice-wakeup.webp\",\n                title: locale === \"zh\" ? \"语音唤醒演示\" : \"Voice Wake-up Demonstration\",\n                videoSrc: locale === \"zh\" ? \"/videos/voice-wakeup-video1.mp4\" : \"/videos/voice-wakeup-en-video1.mp4\"\n            },\n            {\n                imageSrc: \"/videos/device-call-device.webp\",\n                title: locale === \"zh\" ? \"设备与设备通话演示\" : \"Device-to-Device Call Demonstration\",\n                videoSrc: \"/videos/device-call-device-video1.mp4\"\n            }\n        ],\n        imageSize: {\n            width: 300,\n            height: 168\n        },\n        imageBox: {\n            width: 300,\n            height: 168\n        },\n        mode: _components_flex_4items_box__WEBPACK_IMPORTED_MODULE_5__.itemsMode.normal\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_flex_4items_box__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            ...VideosInfo\n        }, void 0, false, {\n            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\hot-spot.tsx\",\n            lineNumber: 286,\n            columnNumber: 7\n        }, this)\n    }, void 0, false);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/[locale]/ui/home/<USER>");

/***/ }),

/***/ "(rsc)/./app/[locale]/ui/home/<USER>":
/*!**************************************!*\
  !*** ./app/[locale]/ui/home/<USER>
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\---AProjects---\imcam-officialsite\fastoffical\app\[locale]\ui\home\nav.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout)\n/* harmony export */ });\nasync function RootLayout({ children, params }) {\n    return children;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvbGF5b3V0LnRzeCIsIm1hcHBpbmdzIjoiOzs7O0FBQWUsZUFBZUEsV0FBVyxFQUN2Q0MsUUFBUSxFQUNSQyxNQUFNLEVBSU47SUFDQSxPQUFPRDtBQUNUIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vaW1jYW1fb2ZmaWNpYWxfc2l0ZS8uL2FwcC9sYXlvdXQudHN4Pzk5ODgiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgYXN5bmMgZnVuY3Rpb24gUm9vdExheW91dCh7XHJcbiAgY2hpbGRyZW4sXHJcbiAgcGFyYW1zLFxyXG59OiBSZWFkb25seTx7XHJcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZVxyXG4gIHBhcmFtczogeyBsb2NhbGU6IHN0cmluZyB9XHJcbn0+KSB7XHJcbiAgcmV0dXJuIGNoaWxkcmVuXHJcbn1cclxuIl0sIm5hbWVzIjpbIlJvb3RMYXlvdXQiLCJjaGlsZHJlbiIsInBhcmFtcyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/not-found.tsx":
/*!***************************!*\
  !*** ./app/not-found.tsx ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\---AProjects---\imcam-officialsite\fastoffical\app\not-found.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./data/news.ts":
/*!**********************!*\
  !*** ./data/news.ts ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ getNewsDatas),\n/* harmony export */   getHotNews: () => (/* binding */ getHotNews),\n/* harmony export */   getNewsData: () => (/* binding */ getNewsData),\n/* harmony export */   getPrevAndNextNewsData: () => (/* binding */ getPrevAndNextNewsData)\n/* harmony export */ });\n/* harmony import */ var _type__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./type */ \"(rsc)/./data/type.ts\");\n\nconst newsDatas = [\n    {\n        title: \"看家王双向视频通话摄像机二代大屏即将问世\",\n        tip: \"近期，看家王将推出一款全新的大屏摄像机摄像机摄像机摄像机摄像机摄像机\",\n        time: new Date(),\n        content: [\n            {\n                type: _type__WEBPACK_IMPORTED_MODULE_0__.ArticleContentType.title,\n                text: \"宣布今日发布新产品\"\n            },\n            {\n                type: _type__WEBPACK_IMPORTED_MODULE_0__.ArticleContentType.paragraph,\n                text: \"今日 Redmi 红米手机官方宣布，开年大作 Redmi K50 电竞版将于 2 月 16 日发布，官方称“为 K 系列用户，打造一部硬核的 Dream Phone”。Redmi K50 电竞版将搭载骁龙 8 Gen 1 处理器，支持 VRS 可变分辨率渲染技术，配备 LPDDR5 内存和 UFS3.1 闪存。\"\n            },\n            {\n                type: _type__WEBPACK_IMPORTED_MODULE_0__.ArticleContentType.image,\n                src: \"/product/product-c31.jpg\"\n            }\n        ],\n        id: \"01\",\n        category: _type__WEBPACK_IMPORTED_MODULE_0__.NewsCategory.cylan,\n        type: _type__WEBPACK_IMPORTED_MODULE_0__.ArticleType.news,\n        linkProductId: \"01\",\n        coverSrc: \"/hotspot-image-1.png\"\n    },\n    {\n        title: \"看家王双向视频通话摄像机二代大屏即将问世\",\n        tip: \"近期，看家王将推出一款全新的大屏摄像机摄像机摄像机摄像机摄像机摄像机\",\n        time: new Date(),\n        content: [\n            {\n                type: _type__WEBPACK_IMPORTED_MODULE_0__.ArticleContentType.paragraph,\n                text: \"今日 Redmi 红米手机官方宣布，开年大作 Redmi K50 电竞版将于 2 月 16 日发布，官方称“为 K 系列用户，打造一部硬核的 Dream Phone”。Redmi K50 电竞版将搭载骁龙 8 Gen 1 处理器，支持 VRS 可变分辨率渲染技术，配备 LPDDR5 内存和 UFS3.1 闪存。\"\n            },\n            {\n                type: _type__WEBPACK_IMPORTED_MODULE_0__.ArticleContentType.image,\n                src: \"/product/product-c31.jpg\"\n            }\n        ],\n        id: \"02\",\n        category: _type__WEBPACK_IMPORTED_MODULE_0__.NewsCategory.industry,\n        type: _type__WEBPACK_IMPORTED_MODULE_0__.ArticleType.news,\n        coverSrc: \"/hotspot-image-2.png\"\n    },\n    {\n        title: \"看家王双向视频通话摄像机二代大屏即将问世\",\n        tip: \"近期，看家王将推出一款全新的大屏摄像机摄像机摄像机摄像机摄像机摄像机\",\n        time: new Date(),\n        content: [\n            {\n                type: _type__WEBPACK_IMPORTED_MODULE_0__.ArticleContentType.paragraph,\n                text: \"今日 Redmi 红米手机官方宣布，开年大作 Redmi K50 电竞版将于 2 月 16 日发布，官方称“为 K 系列用户，打造一部硬核的 Dream Phone”。Redmi K50 电竞版将搭载骁龙 8 Gen 1 处理器，支持 VRS 可变分辨率渲染技术，配备 LPDDR5 内存和 UFS3.1 闪存。\"\n            },\n            {\n                type: _type__WEBPACK_IMPORTED_MODULE_0__.ArticleContentType.image,\n                src: \"/product/product-c31.jpg\"\n            }\n        ],\n        id: \"03\",\n        category: _type__WEBPACK_IMPORTED_MODULE_0__.NewsCategory.industry,\n        type: _type__WEBPACK_IMPORTED_MODULE_0__.ArticleType.news,\n        coverSrc: \"/hotspot-image-2.png\"\n    },\n    {\n        title: \"看家王双向视频通话摄像机二代大屏即将问世\",\n        tip: \"近期，看家王将推出一款全新的大屏摄像机摄像机摄像机摄像机摄像机摄像机\",\n        time: new Date(),\n        content: [\n            {\n                type: _type__WEBPACK_IMPORTED_MODULE_0__.ArticleContentType.paragraph,\n                text: \"今日 Redmi 红米手机官方宣布，开年大作 Redmi K50 电竞版将于 2 月 16 日发布，官方称“为 K 系列用户，打造一部硬核的 Dream Phone”。Redmi K50 电竞版将搭载骁龙 8 Gen 1 处理器，支持 VRS 可变分辨率渲染技术，配备 LPDDR5 内存和 UFS3.1 闪存。\"\n            },\n            {\n                type: _type__WEBPACK_IMPORTED_MODULE_0__.ArticleContentType.image,\n                src: \"/product/product-c31.jpg\"\n            }\n        ],\n        id: \"04\",\n        category: _type__WEBPACK_IMPORTED_MODULE_0__.NewsCategory.cylan,\n        type: _type__WEBPACK_IMPORTED_MODULE_0__.ArticleType.news,\n        coverSrc: \"/hotspot-image-2.png\"\n    },\n    {\n        title: \"看家王双向视频通话摄像机二代大屏即将问世\",\n        tip: \"近期，看家王将推出一款全新的大屏摄像机摄像机摄像机摄像机摄像机摄像机\",\n        time: new Date(),\n        content: [\n            {\n                type: _type__WEBPACK_IMPORTED_MODULE_0__.ArticleContentType.paragraph,\n                text: \"今日 Redmi 红米手机官方宣布，开年大作 Redmi K50 电竞版将于 2 月 16 日发布，官方称“为 K 系列用户，打造一部硬核的 Dream Phone”。Redmi K50 电竞版将搭载骁龙 8 Gen 1 处理器，支持 VRS 可变分辨率渲染技术，配备 LPDDR5 内存和 UFS3.1 闪存。\"\n            },\n            {\n                type: _type__WEBPACK_IMPORTED_MODULE_0__.ArticleContentType.image,\n                src: \"/product/product-c31.jpg\"\n            }\n        ],\n        id: \"05\",\n        category: _type__WEBPACK_IMPORTED_MODULE_0__.NewsCategory.cylan,\n        type: _type__WEBPACK_IMPORTED_MODULE_0__.ArticleType.news,\n        coverSrc: \"/hotspot-image-2.png\"\n    },\n    {\n        title: \"看家王双向视频通话摄像机二代大屏即将问世\",\n        tip: \"近期，看家王将推出一款全新的大屏摄像机摄像机摄像机摄像机摄像机摄像机\",\n        time: new Date(),\n        content: [\n            {\n                type: _type__WEBPACK_IMPORTED_MODULE_0__.ArticleContentType.paragraph,\n                text: \"今日 Redmi 红米手机官方宣布，开年大作 Redmi K50 电竞版将于 2 月 16 日发布，官方称“为 K 系列用户，打造一部硬核的 Dream Phone”。Redmi K50 电竞版将搭载骁龙 8 Gen 1 处理器，支持 VRS 可变分辨率渲染技术，配备 LPDDR5 内存和 UFS3.1 闪存。\"\n            },\n            {\n                type: _type__WEBPACK_IMPORTED_MODULE_0__.ArticleContentType.image,\n                src: \"/product/product-c31.jpg\"\n            }\n        ],\n        id: \"06\",\n        category: _type__WEBPACK_IMPORTED_MODULE_0__.NewsCategory.cylan,\n        type: _type__WEBPACK_IMPORTED_MODULE_0__.ArticleType.news,\n        coverSrc: \"/hotspot-image-2.png\"\n    },\n    {\n        title: \"看家王双向视频通话摄像机二代大屏即将问世\",\n        tip: \"近期，看家王将推出一款全新的大屏摄像机摄像机摄像机摄像机摄像机摄像机\",\n        time: new Date(),\n        content: [\n            {\n                type: _type__WEBPACK_IMPORTED_MODULE_0__.ArticleContentType.paragraph,\n                text: \"今日 Redmi 红米手机官方宣布，开年大作 Redmi K50 电竞版将于 2 月 16 日发布，官方称“为 K 系列用户，打造一部硬核的 Dream Phone”。Redmi K50 电竞版将搭载骁龙 8 Gen 1 处理器，支持 VRS 可变分辨率渲染技术，配备 LPDDR5 内存和 UFS3.1 闪存。\"\n            },\n            {\n                type: _type__WEBPACK_IMPORTED_MODULE_0__.ArticleContentType.image,\n                src: \"/product/product-c31.jpg\"\n            }\n        ],\n        id: \"07\",\n        category: _type__WEBPACK_IMPORTED_MODULE_0__.NewsCategory.cylan,\n        type: _type__WEBPACK_IMPORTED_MODULE_0__.ArticleType.news,\n        coverSrc: \"/hotspot-image-2.png\"\n    },\n    {\n        title: \"看家王双向视频通话摄像机二代大屏即将问世\",\n        tip: \"近期，看家王将推出一款全新的大屏摄像机摄像机摄像机摄像机摄像机摄像机\",\n        time: new Date(),\n        content: [\n            {\n                type: _type__WEBPACK_IMPORTED_MODULE_0__.ArticleContentType.paragraph,\n                text: \"今日 Redmi 红米手机官方宣布，开年大作 Redmi K50 电竞版将于 2 月 16 日发布，官方称“为 K 系列用户，打造一部硬核的 Dream Phone”。Redmi K50 电竞版将搭载骁龙 8 Gen 1 处理器，支持 VRS 可变分辨率渲染技术，配备 LPDDR5 内存和 UFS3.1 闪存。\"\n            },\n            {\n                type: _type__WEBPACK_IMPORTED_MODULE_0__.ArticleContentType.image,\n                src: \"/product/product-c31.jpg\"\n            }\n        ],\n        id: \"08\",\n        category: _type__WEBPACK_IMPORTED_MODULE_0__.NewsCategory.cylan,\n        type: _type__WEBPACK_IMPORTED_MODULE_0__.ArticleType.news,\n        coverSrc: \"/hotspot-image-2.png\"\n    },\n    {\n        title: \"看家王双向视频通话摄像机二代大屏即将问世\",\n        tip: \"近期，看家王将推出一款全新的大屏摄像机摄像机摄像机摄像机摄像机摄像机\",\n        time: new Date(),\n        content: [\n            {\n                type: _type__WEBPACK_IMPORTED_MODULE_0__.ArticleContentType.paragraph,\n                text: \"今日 Redmi 红米手机官方宣布，开年大作 Redmi K50 电竞版将于 2 月 16 日发布，官方称“为 K 系列用户，打造一部硬核的 Dream Phone”。Redmi K50 电竞版将搭载骁龙 8 Gen 1 处理器，支持 VRS 可变分辨率渲染技术，配备 LPDDR5 内存和 UFS3.1 闪存。\"\n            },\n            {\n                type: _type__WEBPACK_IMPORTED_MODULE_0__.ArticleContentType.image,\n                src: \"/product/product-c31.jpg\"\n            }\n        ],\n        id: \"09\",\n        category: _type__WEBPACK_IMPORTED_MODULE_0__.NewsCategory.cylan,\n        type: _type__WEBPACK_IMPORTED_MODULE_0__.ArticleType.news,\n        coverSrc: \"/hotspot-image-2.png\"\n    },\n    {\n        title: \"看家王双向视频通话摄像机二代大屏即将问世\",\n        tip: \"近期，看家王将推出一款全新的大屏摄像机摄像机摄像机摄像机摄像机摄像机\",\n        time: new Date(),\n        content: [\n            {\n                type: _type__WEBPACK_IMPORTED_MODULE_0__.ArticleContentType.paragraph,\n                text: \"今日 Redmi 红米手机官方宣布，开年大作 Redmi K50 电竞版将于 2 月 16 日发布，官方称“为 K 系列用户，打造一部硬核的 Dream Phone”。Redmi K50 电竞版将搭载骁龙 8 Gen 1 处理器，支持 VRS 可变分辨率渲染技术，配备 LPDDR5 内存和 UFS3.1 闪存。\"\n            },\n            {\n                type: _type__WEBPACK_IMPORTED_MODULE_0__.ArticleContentType.image,\n                src: \"/product/product-c31.jpg\"\n            }\n        ],\n        id: \"10\",\n        category: _type__WEBPACK_IMPORTED_MODULE_0__.NewsCategory.cylan,\n        type: _type__WEBPACK_IMPORTED_MODULE_0__.ArticleType.news,\n        coverSrc: \"/hotspot-image-2.png\"\n    }\n];\nasync function getNewsDatas() {\n    return new Promise((resolve)=>{\n        setTimeout(()=>{\n            resolve(newsDatas);\n        }, 10);\n    });\n}\nasync function getNewsData(id) {\n    return new Promise((resolve)=>{\n        const newsData = newsDatas.find((news)=>news.id === id);\n        if (newsData) resolve(newsData);\n        else resolve(false);\n    });\n}\nasync function getPrevAndNextNewsData(id) {\n    return new Promise((resolve)=>{\n        const index = newsDatas.findIndex((item)=>item.id === id);\n        if (index < 0) {\n            resolve(false);\n        } else {\n            resolve({\n                prev: index - 1,\n                prevLink: index - 1 < 0 ? \"\" : `/article/${newsDatas[index - 1].id}`,\n                prevTitle: index - 1 < 0 ? \"\" : newsDatas[index - 1].title,\n                next: index === newsDatas.length - 1 ? -1 : index + 1,\n                nextLink: index === newsDatas.length - 1 ? \"\" : `/article/${newsDatas[index + 1].id}`,\n                nextTitle: index === newsDatas.length - 1 ? \"\" : newsDatas[index + 1].title\n            });\n        }\n    });\n}\nasync function getHotNews() {\n    return new Promise((resolve)=>{\n        resolve(newsDatas.filter((_, index)=>index < 3));\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./data/news.ts\n");

/***/ }),

/***/ "(rsc)/./data/products.ts":
/*!**************************!*\
  !*** ./data/products.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getGroupDatas: () => (/* binding */ getGroupDatas),\n/* harmony export */   getProductData: () => (/* binding */ getProductData),\n/* harmony export */   getProductDatas: () => (/* binding */ getProductDatas)\n/* harmony export */ });\n/* harmony import */ var _type__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./type */ \"(rsc)/./data/type.ts\");\n\nconst productDatas = [\n    {\n        id: \"c31\",\n        groupId: \"01\",\n        groupName: \"摄像头产品\",\n        imageSrc: \"/product/product-c31.jpg\",\n        subImageSrcs: [\n            \"/product/product-c31.jpg\",\n            \"/product/product-c31.jpg\",\n            \"/product/product-c31.jpg\",\n            \"/product/product-c31.jpg\"\n        ],\n        name: \"视频通话智能摄像机\",\n        nameEn: \"Video Call Smart Camera\",\n        model: \"C31\",\n        properties: [\n            \"产品重要属性1\",\n            \"产品重要属性2\",\n            \"产品重要属性3\"\n        ],\n        description: \"C31/C30\",\n        descriptionEn: \"C31/C30\",\n        types: [\n            {\n                name: \"普通版\",\n                properties: [\n                    \"产品重要属性1\",\n                    \"产品重要属性2\",\n                    \"产品重要属性3\",\n                    \"产品重要属性3\"\n                ]\n            },\n            {\n                name: \"中级版\",\n                properties: [\n                    \"产品重要属性1\",\n                    \"产品重要属性2\",\n                    \"产品重要属性3\",\n                    \"产品重要属性1\",\n                    \"产品重要属性2\",\n                    \"产品重要属性3\"\n                ]\n            },\n            {\n                name: \"高级版\",\n                properties: [\n                    \"产品重要属性1\",\n                    \"产品重要属性2\",\n                    \"产品重要属性3\",\n                    \"产品重要属性1\",\n                    \"产品重要属性2\",\n                    \"产品重要属性3\",\n                    \"产品重要属性2\",\n                    \"产品重要属性3\"\n                ]\n            }\n        ],\n        information: [\n            {\n                type: _type__WEBPACK_IMPORTED_MODULE_0__.ArticleContentType.title,\n                text: \"只为了更好的产品\"\n            },\n            {\n                type: _type__WEBPACK_IMPORTED_MODULE_0__.ArticleContentType.paragraph,\n                text: \"今日 Redmi 红米手机官方宣布，开年大作 Redmi K50 电竞版将于 2 月 16 日发布，官方称“为 K 系列用户，打造一部硬核的 Dream Phone”。Redmi K50 电竞版将搭载骁龙 8 Gen 1 处理器，支持 VRS 可变分辨率渲染技术，配备 LPDDR5 内存和 UFS3.1 闪存。\"\n            },\n            {\n                type: _type__WEBPACK_IMPORTED_MODULE_0__.ArticleContentType.image,\n                src: \"/product/product-c31.jpg\"\n            }\n        ],\n        spec: [\n            {\n                type: \"尺寸与重量\",\n                detail: \"高度：约132.6mm；宽度：73mm；厚度：16mm；重量：275g\"\n            },\n            {\n                type: \"存储\",\n                detail: \"8G+128GB；8G+256GB；12G+128GB；12G+256GB\"\n            },\n            {\n                type: \"显示\",\n                detail: \"17.02CM/6.70英寸\"\n            },\n            {\n                type: \"摄像头\",\n                detail: \"前置：3200万像素，f/2.4光圈，FOV81，5P镜头；后置：5000万像素，广角与超广角摄像；\"\n            },\n            {\n                type: \"处理平台\",\n                detail: \"高通骁龙888；2.842GHz；8核\"\n            },\n            {\n                type: \"电池\",\n                detail: \"4500mAh；支持快充，最大功率65W；支持无线充电\"\n            },\n            {\n                type: \"生物识别\",\n                detail: \"支持指纹识别；支持面部解锁\"\n            },\n            {\n                type: \"数据功能\",\n                detail: \"双卡双待；2G3G4G5G；蓝牙5.2；USB3.1；全功能NFC；\"\n            }\n        ],\n        videos: [\n            {\n                videoId: \"01\"\n            }\n        ],\n        helps: [\n            {\n                helpId: \"01\"\n            }\n        ]\n    },\n    {\n        id: \"02\",\n        groupId: \"01\",\n        groupName: \"摄像头产品\",\n        imageSrc: \"/product/product-c41.jpg\",\n        subImageSrcs: [\n            \"/product/product-c31.jpg\",\n            \"/product/product-c31.jpg\",\n            \"/product/product-c31.jpg\",\n            \"/product/product-c31.jpg\"\n        ],\n        name: \"视频通话智能摄像机\",\n        nameEn: \"Video Call Smart Camera\",\n        model: \"C41\",\n        properties: [\n            \"产品重要属性1\",\n            \"产品重要属性2\",\n            \"产品重要属性3\"\n        ],\n        description: \"C41P\",\n        descriptionEn: \"C41P\",\n        types: [\n            {\n                name: \"普通版\",\n                properties: [\n                    \"产品重要属性1\",\n                    \"产品重要属性2\",\n                    \"产品重要属性3\",\n                    \"产品重要属性3\"\n                ]\n            },\n            {\n                name: \"中级版\",\n                properties: [\n                    \"产品重要属性1\",\n                    \"产品重要属性2\",\n                    \"产品重要属性3\",\n                    \"产品重要属性1\",\n                    \"产品重要属性2\",\n                    \"产品重要属性3\"\n                ]\n            },\n            {\n                name: \"高级版\",\n                properties: [\n                    \"产品重要属性1\",\n                    \"产品重要属性2\",\n                    \"产品重要属性3\",\n                    \"产品重要属性1\",\n                    \"产品重要属性2\",\n                    \"产品重要属性3\",\n                    \"产品重要属性2\",\n                    \"产品重要属性3\"\n                ]\n            }\n        ],\n        information: [\n            {\n                type: _type__WEBPACK_IMPORTED_MODULE_0__.ArticleContentType.paragraph,\n                text: \"今日 Redmi 红米手机官方宣布，开年大作 Redmi K50 电竞版将于 2 月 16 日发布，官方称“为 K 系列用户，打造一部硬核的 Dream Phone”。Redmi K50 电竞版将搭载骁龙 8 Gen 1 处理器，支持 VRS 可变分辨率渲染技术，配备 LPDDR5 内存和 UFS3.1 闪存。\"\n            },\n            {\n                type: _type__WEBPACK_IMPORTED_MODULE_0__.ArticleContentType.image,\n                src: \"/product/product-c31.jpg\"\n            }\n        ],\n        spec: [\n            {\n                type: \"尺寸与重量\",\n                detail: \"高度：约132.6mm；宽度：73mm；厚度：16mm；重量：275g\"\n            },\n            {\n                type: \"存储\",\n                detail: \"8G+128GB；8G+256GB；12G+128GB；12G+256GB\"\n            },\n            {\n                type: \"显示\",\n                detail: \"17.02CM/6.70英寸\"\n            },\n            {\n                type: \"摄像头\",\n                detail: \"前置：3200万像素，f/2.4光圈，FOV81，5P镜头；后置：5000万像素，广角与超广角摄像；\"\n            },\n            {\n                type: \"处理平台\",\n                detail: \"高通骁龙888；2.842GHz；8核\"\n            },\n            {\n                type: \"电池\",\n                detail: \"4500mAh；支持快充，最大功率65W；支持无线充电\"\n            },\n            {\n                type: \"生物识别\",\n                detail: \"支持指纹识别；支持面部解锁\"\n            },\n            {\n                type: \"数据功能\",\n                detail: \"双卡双待；2G3G4G5G；蓝牙5.2；USB3.1；全功能NFC；\"\n            }\n        ],\n        videos: [\n            {\n                videoId: \"01\"\n            }\n        ],\n        helps: [\n            {\n                helpId: \"01\"\n            }\n        ]\n    },\n    {\n        id: \"04\",\n        groupId: \"01\",\n        groupName: \"摄像头产品\",\n        imageSrc: \"/product/product-t30.jpg\",\n        subImageSrcs: [\n            \"/product/product-c31.jpg\",\n            \"/product/product-c31.jpg\",\n            \"/product/product-c31.jpg\",\n            \"/product/product-c31.jpg\"\n        ],\n        name: \"视频通话智能摄像机\",\n        nameEn: \"Video Call Smart Camera\",\n        model: \"C34\",\n        properties: [\n            \"产品重要属性1\",\n            \"产品重要属性2\",\n            \"产品重要属性3\"\n        ],\n        description: \"T30-T\",\n        descriptionEn: \"T30-T\",\n        types: [\n            {\n                name: \"普通版\",\n                properties: [\n                    \"产品重要属性1\",\n                    \"产品重要属性2\",\n                    \"产品重要属性3\",\n                    \"产品重要属性3\"\n                ]\n            },\n            {\n                name: \"中级版\",\n                properties: [\n                    \"产品重要属性1\",\n                    \"产品重要属性2\",\n                    \"产品重要属性3\",\n                    \"产品重要属性1\",\n                    \"产品重要属性2\",\n                    \"产品重要属性3\"\n                ]\n            },\n            {\n                name: \"高级版\",\n                properties: [\n                    \"产品重要属性1\",\n                    \"产品重要属性2\",\n                    \"产品重要属性3\",\n                    \"产品重要属性1\",\n                    \"产品重要属性2\",\n                    \"产品重要属性3\",\n                    \"产品重要属性2\",\n                    \"产品重要属性3\"\n                ]\n            }\n        ],\n        information: [\n            {\n                type: _type__WEBPACK_IMPORTED_MODULE_0__.ArticleContentType.paragraph,\n                text: \"今日 Redmi 红米手机官方宣布，开年大作 Redmi K50 电竞版将于 2 月 16 日发布，官方称“为 K 系列用户，打造一部硬核的 Dream Phone”。Redmi K50 电竞版将搭载骁龙 8 Gen 1 处理器，支持 VRS 可变分辨率渲染技术，配备 LPDDR5 内存和 UFS3.1 闪存。\"\n            },\n            {\n                type: _type__WEBPACK_IMPORTED_MODULE_0__.ArticleContentType.image,\n                src: \"/product/product-c31.jpg\"\n            }\n        ],\n        spec: [\n            {\n                type: \"尺寸与重量\",\n                detail: \"高度：约132.6mm；宽度：73mm；厚度：16mm；重量：275g\"\n            },\n            {\n                type: \"存储\",\n                detail: \"8G+128GB；8G+256GB；12G+128GB；12G+256GB\"\n            },\n            {\n                type: \"显示\",\n                detail: \"17.02CM/6.70英寸\"\n            },\n            {\n                type: \"摄像头\",\n                detail: \"前置：3200万像素，f/2.4光圈，FOV81，5P镜头；后置：5000万像素，广角与超广角摄像；\"\n            },\n            {\n                type: \"处理平台\",\n                detail: \"高通骁龙888；2.842GHz；8核\"\n            },\n            {\n                type: \"电池\",\n                detail: \"4500mAh；支持快充，最大功率65W；支持无线充电\"\n            },\n            {\n                type: \"生物识别\",\n                detail: \"支持指纹识别；支持面部解锁\"\n            },\n            {\n                type: \"数据功能\",\n                detail: \"双卡双待；2G3G4G5G；蓝牙5.2；USB3.1；全功能NFC；\"\n            }\n        ],\n        videos: [\n            {\n                videoId: \"01\"\n            }\n        ],\n        helps: [\n            {\n                helpId: \"01\"\n            }\n        ]\n    },\n    {\n        id: \"05\",\n        groupId: \"01\",\n        groupName: \"摄像头产品\",\n        imageSrc: \"/product/product-c21.jpg\",\n        subImageSrcs: [\n            \"/product/product-c31.jpg\",\n            \"/product/product-c31.jpg\",\n            \"/product/product-c31.jpg\",\n            \"/product/product-c31.jpg\"\n        ],\n        name: \"视频通话智能摄像机\",\n        nameEn: \"Video Call Smart Camera\",\n        model: \"C21\",\n        properties: [\n            \"产品重要属性1\",\n            \"产品重要属性2\",\n            \"产品重要属性3\"\n        ],\n        description: \"C21\",\n        descriptionEn: \"C21\",\n        types: [\n            {\n                name: \"普通版\",\n                properties: [\n                    \"产品重要属性1\",\n                    \"产品重要属性2\",\n                    \"产品重要属性3\",\n                    \"产品重要属性3\"\n                ]\n            },\n            {\n                name: \"中级版\",\n                properties: [\n                    \"产品重要属性1\",\n                    \"产品重要属性2\",\n                    \"产品重要属性3\",\n                    \"产品重要属性1\",\n                    \"产品重要属性2\",\n                    \"产品重要属性3\"\n                ]\n            },\n            {\n                name: \"高级版\",\n                properties: [\n                    \"产品重要属性1\",\n                    \"产品重要属性2\",\n                    \"产品重要属性3\",\n                    \"产品重要属性1\",\n                    \"产品重要属性2\",\n                    \"产品重要属性3\",\n                    \"产品重要属性2\",\n                    \"产品重要属性3\"\n                ]\n            }\n        ],\n        information: [\n            {\n                type: _type__WEBPACK_IMPORTED_MODULE_0__.ArticleContentType.paragraph,\n                text: \"今日 Redmi 红米手机官方宣布，开年大作 Redmi K50 电竞版将于 2 月 16 日发布，官方称“为 K 系列用户，打造一部硬核的 Dream Phone”。Redmi K50 电竞版将搭载骁龙 8 Gen 1 处理器，支持 VRS 可变分辨率渲染技术，配备 LPDDR5 内存和 UFS3.1 闪存。\"\n            },\n            {\n                type: _type__WEBPACK_IMPORTED_MODULE_0__.ArticleContentType.image,\n                src: \"/product/product-c31.jpg\"\n            }\n        ],\n        spec: [\n            {\n                type: \"尺寸与重量\",\n                detail: \"高度：约132.6mm；宽度：73mm；厚度：16mm；重量：275g\"\n            },\n            {\n                type: \"存储\",\n                detail: \"8G+128GB；8G+256GB；12G+128GB；12G+256GB\"\n            },\n            {\n                type: \"显示\",\n                detail: \"17.02CM/6.70英寸\"\n            },\n            {\n                type: \"摄像头\",\n                detail: \"前置：3200万像素，f/2.4光圈，FOV81，5P镜头；后置：5000万像素，广角与超广角摄像；\"\n            },\n            {\n                type: \"处理平台\",\n                detail: \"高通骁龙888；2.842GHz；8核\"\n            },\n            {\n                type: \"电池\",\n                detail: \"4500mAh；支持快充，最大功率65W；支持无线充电\"\n            },\n            {\n                type: \"生物识别\",\n                detail: \"支持指纹识别；支持面部解锁\"\n            },\n            {\n                type: \"数据功能\",\n                detail: \"双卡双待；2G3G4G5G；蓝牙5.2；USB3.1；全功能NFC；\"\n            }\n        ],\n        videos: [\n            {\n                videoId: \"01\"\n            }\n        ],\n        helps: [\n            {\n                helpId: \"01\"\n            }\n        ]\n    },\n    {\n        id: \"06\",\n        groupId: \"01\",\n        groupName: \"摄像头产品\",\n        imageSrc: \"/product/product-wx1.jpg\",\n        subImageSrcs: [\n            \"/product/product-c31.jpg\",\n            \"/product/product-c31.jpg\",\n            \"/product/product-c31.jpg\",\n            \"/product/product-c31.jpg\"\n        ],\n        name: \"视频通话智能摄像机\",\n        nameEn: \"Video Call Smart Camera\",\n        model: \"C31-WX\",\n        properties: [\n            \"产品重要属性1\",\n            \"产品重要属性2\",\n            \"产品重要属性3\"\n        ],\n        description: \"C31\",\n        descriptionEn: \"C31\",\n        types: [\n            {\n                name: \"普通版\",\n                properties: [\n                    \"产品重要属性1\",\n                    \"产品重要属性2\",\n                    \"产品重要属性3\",\n                    \"产品重要属性3\"\n                ]\n            },\n            {\n                name: \"中级版\",\n                properties: [\n                    \"产品重要属性1\",\n                    \"产品重要属性2\",\n                    \"产品重要属性3\",\n                    \"产品重要属性1\",\n                    \"产品重要属性2\",\n                    \"产品重要属性3\"\n                ]\n            },\n            {\n                name: \"高级版\",\n                properties: [\n                    \"产品重要属性1\",\n                    \"产品重要属性2\",\n                    \"产品重要属性3\",\n                    \"产品重要属性1\",\n                    \"产品重要属性2\",\n                    \"产品重要属性3\",\n                    \"产品重要属性2\",\n                    \"产品重要属性3\"\n                ]\n            }\n        ],\n        information: [\n            {\n                type: _type__WEBPACK_IMPORTED_MODULE_0__.ArticleContentType.paragraph,\n                text: \"今日 Redmi 红米手机官方宣布，开年大作 Redmi K50 电竞版将于 2 月 16 日发布，官方称“为 K 系列用户，打造一部硬核的 Dream Phone”。Redmi K50 电竞版将搭载骁龙 8 Gen 1 处理器，支持 VRS 可变分辨率渲染技术，配备 LPDDR5 内存和 UFS3.1 闪存。\"\n            },\n            {\n                type: _type__WEBPACK_IMPORTED_MODULE_0__.ArticleContentType.image,\n                src: \"/product/product-c31.jpg\"\n            }\n        ],\n        spec: [\n            {\n                type: \"尺寸与重量\",\n                detail: \"高度：约132.6mm；宽度：73mm；厚度：16mm；重量：275g\"\n            },\n            {\n                type: \"存储\",\n                detail: \"8G+128GB；8G+256GB；12G+128GB；12G+256GB\"\n            },\n            {\n                type: \"显示\",\n                detail: \"17.02CM/6.70英寸\"\n            },\n            {\n                type: \"摄像头\",\n                detail: \"前置：3200万像素，f/2.4光圈，FOV81，5P镜头；后置：5000万像素，广角与超广角摄像；\"\n            },\n            {\n                type: \"处理平台\",\n                detail: \"高通骁龙888；2.842GHz；8核\"\n            },\n            {\n                type: \"电池\",\n                detail: \"4500mAh；支持快充，最大功率65W；支持无线充电\"\n            },\n            {\n                type: \"生物识别\",\n                detail: \"支持指纹识别；支持面部解锁\"\n            },\n            {\n                type: \"数据功能\",\n                detail: \"双卡双待；2G3G4G5G；蓝牙5.2；USB3.1；全功能NFC；\"\n            }\n        ],\n        videos: [\n            {\n                videoId: \"01\"\n            }\n        ],\n        helps: [\n            {\n                helpId: \"01\"\n            }\n        ]\n    },\n    {\n        id: \"t1pro\",\n        groupId: \"02\",\n        groupName: \"翻译机\",\n        imageSrc: \"/product/product-t1pro.jpg\",\n        subImageSrcs: [\n            \"/product/product-t1pro.jpg\",\n            \"/product/product-c31.jpg\",\n            \"/product/product-c31.jpg\",\n            \"/product/product-c31.jpg\"\n        ],\n        name: \"桌面式双屏AI翻译机\",\n        nameEn: \"Desktop Dual-Screen AI Translator\",\n        model: \"T1 Pro\",\n        properties: [\n            \"产品重要属性1\",\n            \"产品重要属性2\",\n            \"产品重要属性3\"\n        ],\n        description: \"T1 Pro\",\n        descriptionEn: \"T1 Pro\",\n        types: [\n            {\n                name: \"普通版\",\n                properties: [\n                    \"产品重要属性1\",\n                    \"产品重要属性2\",\n                    \"产品重要属性3\",\n                    \"产品重要属性3\"\n                ]\n            },\n            {\n                name: \"中级版\",\n                properties: [\n                    \"产品重要属性1\",\n                    \"产品重要属性2\",\n                    \"产品重要属性3\",\n                    \"产品重要属性1\",\n                    \"产品重要属性2\",\n                    \"产品重要属性3\"\n                ]\n            },\n            {\n                name: \"高级版\",\n                properties: [\n                    \"产品重要属性1\",\n                    \"产品重要属性2\",\n                    \"产品重要属性3\",\n                    \"产品重要属性1\",\n                    \"产品重要属性2\",\n                    \"产品重要属性3\",\n                    \"产品重要属性2\",\n                    \"产品重要属性3\"\n                ]\n            }\n        ],\n        information: [\n            {\n                type: _type__WEBPACK_IMPORTED_MODULE_0__.ArticleContentType.paragraph,\n                text: \"今日 Redmi 红米手机官方宣布，开年大作 Redmi K50 电竞版将于 2 月 16 日发布，官方称“为 K 系列用户，打造一部硬核的 Dream Phone”。Redmi K50 电竞版将搭载骁龙 8 Gen 1 处理器，支持 VRS 可变分辨率渲染技术，配备 LPDDR5 内存和 UFS3.1 闪存。\"\n            },\n            {\n                type: _type__WEBPACK_IMPORTED_MODULE_0__.ArticleContentType.image,\n                src: \"/product/product-c31.jpg\"\n            }\n        ],\n        spec: [\n            {\n                type: \"尺寸与重量\",\n                detail: \"高度：约132.6mm；宽度：73mm；厚度：16mm；重量：275g\"\n            },\n            {\n                type: \"存储\",\n                detail: \"8G+128GB；8G+256GB；12G+128GB；12G+256GB\"\n            },\n            {\n                type: \"显示\",\n                detail: \"17.02CM/6.70英寸\"\n            },\n            {\n                type: \"摄像头\",\n                detail: \"前置：3200万像素，f/2.4光圈，FOV81，5P镜头；后置：5000万像素，广角与超广角摄像；\"\n            },\n            {\n                type: \"处理平台\",\n                detail: \"高通骁龙888；2.842GHz；8核\"\n            },\n            {\n                type: \"电池\",\n                detail: \"4500mAh；支持快充，最大功率65W；支持无线充电\"\n            },\n            {\n                type: \"生物识别\",\n                detail: \"支持指纹识别；支持面部解锁\"\n            },\n            {\n                type: \"数据功能\",\n                detail: \"双卡双待；2G3G4G5G；蓝牙5.2；USB3.1；全功能NFC；\"\n            }\n        ],\n        videos: [\n            {\n                videoId: \"01\"\n            }\n        ],\n        helps: [\n            {\n                helpId: \"01\"\n            }\n        ]\n    }\n];\nconst productGroups = [\n    {\n        id: \"01\",\n        name: \"摄像头产品\",\n        nameEn: \"Camera\"\n    },\n    {\n        id: \"02\",\n        name: \"翻译机\",\n        nameEn: \"Translator\"\n    }\n];\nasync function getProductDatas() {\n    return new Promise((resolve)=>{\n        resolve(productDatas);\n    });\n}\nasync function getProductData(id) {\n    return new Promise((resolve)=>{\n        const productData = productDatas.find((product)=>product.id === id);\n        if (productData) {\n            resolve(productData);\n        } else {\n            resolve(false);\n        }\n    });\n}\nasync function getGroupDatas() {\n    return new Promise((resolve)=>{\n        resolve(productGroups);\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./data/products.ts\n");

/***/ }),

/***/ "(rsc)/./data/type.ts":
/*!**********************!*\
  !*** ./data/type.ts ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ArticleContentType: () => (/* binding */ ArticleContentType),\n/* harmony export */   ArticleType: () => (/* binding */ ArticleType),\n/* harmony export */   NewsCategory: () => (/* binding */ NewsCategory)\n/* harmony export */ });\n/* 文章相关 */ var ArticleContentType;\n(function(ArticleContentType) {\n    ArticleContentType[ArticleContentType[\"paragraph\"] = 0] = \"paragraph\";\n    ArticleContentType[ArticleContentType[\"title\"] = 1] = \"title\";\n    ArticleContentType[ArticleContentType[\"image\"] = 2] = \"image\";\n})(ArticleContentType || (ArticleContentType = {}));\nvar ArticleType;\n(function(ArticleType) {\n    ArticleType[\"news\"] = \"/news\";\n    ArticleType[\"support\"] = \"/support\";\n    ArticleType[\"product\"] = \"/product\";\n})(ArticleType || (ArticleType = {}));\nvar NewsCategory;\n(function(NewsCategory) {\n    NewsCategory[NewsCategory[\"cylan\"] = 0] = \"cylan\";\n    NewsCategory[NewsCategory[\"industry\"] = 1] = \"industry\";\n})(NewsCategory || (NewsCategory = {}));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./data/type.ts\n");

/***/ }),

/***/ "(rsc)/./locales/server.ts":
/*!***************************!*\
  !*** ./locales/server.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getCurrentLocale: () => (/* binding */ getCurrentLocale),\n/* harmony export */   getI18n: () => (/* binding */ getI18n),\n/* harmony export */   getScopedI18n: () => (/* binding */ getScopedI18n),\n/* harmony export */   getStaticParams: () => (/* binding */ getStaticParams)\n/* harmony export */ });\n/* harmony import */ var next_international_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-international/server */ \"(rsc)/./node_modules/next-international/dist/app/server/index.js\");\n/* harmony import */ var next_international_server__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_international_server__WEBPACK_IMPORTED_MODULE_0__);\n\nconst { getI18n, getScopedI18n, getStaticParams, getCurrentLocale } = (0,next_international_server__WEBPACK_IMPORTED_MODULE_0__.createI18nServer)({\n    en: ()=>__webpack_require__.e(/*! import() */ \"_rsc_locales_en_ts\").then(__webpack_require__.bind(__webpack_require__, /*! ./en */ \"(rsc)/./locales/en.ts\")),\n    zh: ()=>__webpack_require__.e(/*! import() */ \"_rsc_locales_zh_ts\").then(__webpack_require__.bind(__webpack_require__, /*! ./zh */ \"(rsc)/./locales/zh.ts\"))\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9sb2NhbGVzL3NlcnZlci50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBNkQ7QUFFdEQsTUFBTSxFQUFFQyxPQUFPLEVBQUVDLGFBQWEsRUFBRUMsZUFBZSxFQUFFQyxnQkFBZ0IsRUFBRSxHQUFHSiwyRUFBZ0JBLENBQUM7SUFDNUZLLElBQUksSUFBTSxvSkFBYztJQUN4QkMsSUFBSSxJQUFNLG9KQUFjO0FBQzFCLEdBQUciLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9pbWNhbV9vZmZpY2lhbF9zaXRlLy4vbG9jYWxlcy9zZXJ2ZXIudHM/MmZlYyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBjcmVhdGVJMThuU2VydmVyIH0gZnJvbSBcIm5leHQtaW50ZXJuYXRpb25hbC9zZXJ2ZXJcIjtcclxuXHJcbmV4cG9ydCBjb25zdCB7IGdldEkxOG4sIGdldFNjb3BlZEkxOG4sIGdldFN0YXRpY1BhcmFtcywgZ2V0Q3VycmVudExvY2FsZSB9ID0gY3JlYXRlSTE4blNlcnZlcih7XHJcbiAgZW46ICgpID0+IGltcG9ydChcIi4vZW5cIiksXHJcbiAgemg6ICgpID0+IGltcG9ydChcIi4vemhcIiksXHJcbn0pO1xyXG4iXSwibmFtZXMiOlsiY3JlYXRlSTE4blNlcnZlciIsImdldEkxOG4iLCJnZXRTY29wZWRJMThuIiwiZ2V0U3RhdGljUGFyYW1zIiwiZ2V0Q3VycmVudExvY2FsZSIsImVuIiwiemgiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./locales/server.ts\n");

/***/ }),

/***/ "(rsc)/./utils/utils.ts":
/*!************************!*\
  !*** ./utils/utils.ts ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatDate: () => (/* binding */ formatDate)\n/* harmony export */ });\nfunction formatDate(date, formatStr) {\n    const year = date.getFullYear();\n    const month = String(date.getMonth() + 1).padStart(2, \"0\");\n    const day = String(date.getDate()).padStart(2, \"0\");\n    const hours = String(date.getHours()).padStart(2, \"0\");\n    const minutes = String(date.getMinutes()).padStart(2, \"0\");\n    const seconds = String(date.getSeconds()).padStart(2, \"0\");\n    return formatStr.replace(/YYYY/g, String(year)).replace(/MM/g, month).replace(/DD/g, day).replace(/HH/g, hours).replace(/mm/g, minutes).replace(/ss/g, seconds);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./utils/utils.ts\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__":
/*!**********************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__ ***!
  \**********************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"32x32\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9hcHAvZmF2aWNvbi5pY28/X19uZXh0X21ldGFkYXRhX18iLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQUEsRUFBaUY7O0FBRWpGLEVBQUUsaUVBQWU7QUFDakIsdUJBQXVCO0FBQ3ZCLHFCQUFxQiw4RkFBbUI7O0FBRXhDO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTCIsInNvdXJjZXMiOlsid2VicGFjazovL2ltY2FtX29mZmljaWFsX3NpdGUvLi9hcHAvZmF2aWNvbi5pY28/ZTEwMyJdLCJzb3VyY2VzQ29udGVudCI6WyIgIGltcG9ydCB7IGZpbGxNZXRhZGF0YVNlZ21lbnQgfSBmcm9tICduZXh0L2Rpc3QvbGliL21ldGFkYXRhL2dldC1tZXRhZGF0YS1yb3V0ZSdcblxuICBleHBvcnQgZGVmYXVsdCAocHJvcHMpID0+IHtcbiAgICBjb25zdCBpbWFnZURhdGEgPSB7XCJ0eXBlXCI6XCJpbWFnZS94LWljb25cIixcInNpemVzXCI6XCIzMngzMlwifVxuICAgIGNvbnN0IGltYWdlVXJsID0gZmlsbE1ldGFkYXRhU2VnbWVudChcIi5cIiwgcHJvcHMucGFyYW1zLCBcImZhdmljb24uaWNvXCIpXG5cbiAgICByZXR1cm4gW3tcbiAgICAgIC4uLmltYWdlRGF0YSxcbiAgICAgIHVybDogaW1hZ2VVcmwgKyBcIlwiLFxuICAgIH1dXG4gIH0iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/next-international","vendor-chunks/swiper"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F%5Blocale%5D%2F(overview)%2Fpage&page=%2F%5Blocale%5D%2F(overview)%2Fpage&appPaths=%2F%5Blocale%5D%2F(overview)%2Fpage&pagePath=private-next-app-dir%2F%5Blocale%5D%2F(overview)%2Fpage.tsx&appDir=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();