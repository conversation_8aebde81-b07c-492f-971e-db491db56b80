"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_locales_zh_ts",{

/***/ "(app-pages-browser)/./locales/zh.ts":
/*!***********************!*\
  !*** ./locales/zh.ts ***!
  \***********************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = ({\n    home: \"首页\",\n    productCenter: \"产品中心\",\n    prodoctVideos: \"产品视频\",\n    support: \"服务支持\",\n    aboutUs: \"关于我们\",\n    productCamera: \"摄像头产品\",\n    downloadClient: \"下载客户端\",\n    chinese: \"中文\",\n    english: \"英文\",\n    seeMore: \"查看更多\",\n    year: \"年\",\n    companiyTime: \"公司成立时间\",\n    deviceSells: \"设备销量\",\n    activeUsers: \"活跃用户\",\n    contactUs: \"联系我们\",\n    pride1: \"深圳全球创新创业交流会优秀创新产品奖\",\n    pride2: \"欧洲联盟知识产权局注册证书\",\n    pride3: \"外观设计专利证书\",\n    pride4: \"RoHS证书\",\n    pride5: \"国家高新技术企业证书\",\n    pride6: \"中国智能家居产业联盟会员单位\",\n    pride7: \"英特尔会员资格\",\n    pride8: \"深圳市物联网协会会员单位\",\n    pride9: \"中国智慧城市优秀产品奖\",\n    cylanAddress: \"深圳市宝安区新安街道兴东社区72区群力二路1号合成号深圳民俗文化产业园211\",\n    aboutCylan: \"关于赛蓝\",\n    cylanPrides: \"荣誉资质\",\n    followUs: \"关注我们\",\n    imcamGongzhonghao: \"看家王公众号\",\n    downloadQRcode: \"下载二维码\",\n    copyrightText: \"版权所有 \\xa9 2025 深圳市赛蓝科技有限公司\",\n    copyrightLink: \" 粤ICP备12047353号\",\n    all: \"全部\",\n    imcamApp: \"赛蓝应用支持\",\n    imcamAppTip: \"随时随地体验智能生活\",\n    miniProgram: \"微信小程序\",\n    download: \"下载\",\n    goToAppstore: \"去应用商店\",\n    aboutCylanDescription: \"深圳市赛蓝科技有限公司，具备行业领先的智能终端硬件开发能力，自主拥有的智能家居品牌“看家王”，以科技让家居更有梦想为品牌愿景，始终践行“让家居生活更智能、更便捷”的使命，坚持品质创新、用户价值为先的核心原则，致力于为用户提供智能、舒适、便捷、安全、节能的家庭智能硬件设备。\\n\\n  “看家王”依托于赛蓝云平台的核心优势，以移动物联网为基础，通过打造多条智能家居产业价值链条，将全方位的为智能家庭构建智能化、数字化、便捷化的生活方式。\\n  \\n  目前，“看家王”产品线已囊括智能摄像头、台灯、自动投食机等产品线，并以先导性迅速占领市场，引领2B业务发展。产品获得欧盟RoHS环保认证，不仅在国内畅销，更远销北美、南美、欧洲等海外市场，赢得了广大消费者的一致认可，直接为国内外消费者带来便携化、数字化、智能化生活体验。\",\n    aboutCylanQuotesTitle1: \"愿景\",\n    aboutCylanQuotesTitle2: \"使命\",\n    aboutCylanQuotesTitle3: \"价值观\",\n    aboutCylanQuotesText1: \"科技让家居更有梦想！\",\n    aboutCylanQuotesText2: \"让家居生活更智能、更便捷！\",\n    aboutCylanQuotesText3: \"智能、舒适、便捷、安全、节能\",\n    career: \"发展历程\",\n    careerDescription: \"赛蓝成立于2005年，自成立以来，赛蓝始终致力于成为智能家居行业的创新者。赛蓝的发展历程充满了挑战和机遇，但我们始终秉持着创新、质量和客户至上的理念。目前在双向视频通话智能摄像机产品有30W+年销量，看家王APP累计获得45万+活跃用户。\",\n    page404: \"404 - 页面不存在\",\n    page404Description: \"抱歉，您访问的页面好像不存在，试试以下操作\",\n    page404Tip1: \"1、检查网址是否正确\",\n    page404Tip2: \"2、回到首页或者向上一页\",\n    backToHome: \"返回到首页\",\n    backPage: \"返回上一页\",\n    backToTop: \"返回顶部\",\n    help: \"使用帮助\",\n    detail: \"详情\",\n    time: \"时间\",\n    prevArticle: \"上一篇\",\n    nextArticle: \"下一篇\",\n    hot: \"热门\",\n    imcam: \"看家王\",\n    androidApp: \"安卓应用\",\n    iosApp: \"iOS应用\",\n    dangdang: \"铛铛看家\",\n    productTranslator: \"翻译机产品\",\n    aboutCylanTitle: \"赛蓝科技（Cylan）——\",\n    aboutCylanTitleSub: \"全球智能看护解决方案领导者\",\n    companyReview: \"​​​公司概览\",\n    companySetTime: \"成立时间：\",\n    companySetTimeText: \"2005年\",\n    companyMainLocation: \"总部地点：\",\n    companyMainLocationText: \"中国深圳\",\n    companyCoreTech: \"核心技术：\",\n    companyCoreTechText: \"AIoT \\xd7 互联网通信AI大模型融合创新\",\n    companyService: \"服务对象：\",\n    companyServiceText: \"家庭及企业级用户\",\n    companyCoreProduct: \"​核心产品\",\n    companyCoreProductText1: \"智能双向视频摄像机\",\n    companyCoreProductText2: \"✓ 1080P高清视频通话 + 红外夜视\",\n    companyCoreProductText3: \"✓ AI行为分析（场景粒度分析推送、入侵警报）\",\n    companyCoreProductText4: \"✓ 企业级数据加密（3DES、AES加密）\",\n    companyCoreProductText5: \"✓ 多平台接入（iOS/Android/Web管理后台）\",\n    companyCoreProductText6: \"桌面式双屏翻译机\",\n    companyCoreProductText7: \"✓ 2个10”高清LCD\",\n    companyCoreProductText8: \"✓ 2个数字降噪MIC整列\",\n    companyCoreProductText9: \"✓ 支持多种语言实时互相翻译\",\n    companyCoreProductText10: \"✓ 多平台接入（iOS/Android/Web管理后台）\",\n    companyGlobal: \"全球化成果\",\n    companyGlobalText1: \"• 覆盖50+国家​​市场（欧美/东南亚/中东重点区域）\",\n    companyGlobalText2: \"• 累计服务80万+用户\",\n    companyGlobalText3: \"• 典型场景：家庭看护和语言翻译\",\n    companyGlobalText4: \"家庭：独居老人看护、婴幼儿监护等\",\n    companyGlobalText5: \"企业：智慧酒店、外贸企业、交通旅游、机场、国际交流场所等\",\n    imMate: \"悦办\"\n});\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./locales/zh.ts\n"));

/***/ })

});