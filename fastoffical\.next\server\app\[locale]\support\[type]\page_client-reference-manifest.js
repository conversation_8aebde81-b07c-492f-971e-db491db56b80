globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/[locale]/support/[type]/page"]={"moduleLoading":{"prefix":"/_next/","crossOrigin":null},"ssrModuleMapping":{"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/not-found.tsx":{"*":{"id":"(ssr)/./app/not-found.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/image-component.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/image-component.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/link.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/link.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/[locale]/ui/components/back-to-top.tsx":{"*":{"id":"(ssr)/./app/[locale]/ui/components/back-to-top.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/[locale]/ui/home/<USER>":{"*":{"id":"(ssr)/./app/[locale]/ui/home/<USER>","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/[locale]/ui/home/<USER>":{"*":{"id":"(ssr)/./app/[locale]/ui/home/<USER>","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/[locale]/ui/home/<USER>":{"*":{"id":"(ssr)/./app/[locale]/ui/home/<USER>","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/[locale]/ui/home/<USER>":{"*":{"id":"(ssr)/./app/[locale]/ui/home/<USER>","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/[locale]/ui/home/<USER>":{"*":{"id":"(ssr)/./app/[locale]/ui/home/<USER>","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/[locale]/ui/components/components.scss":{"*":{"id":"(ssr)/./app/[locale]/ui/components/components.scss","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/[locale]/support/page.tsx":{"*":{"id":"(ssr)/./app/[locale]/support/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/[locale]/ui/components/page-tabs.tsx":{"*":{"id":"(ssr)/./app/[locale]/ui/components/page-tabs.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/[locale]/ui/support/help.tsx":{"*":{"id":"(ssr)/./app/[locale]/ui/support/help.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/[locale]/ui/support/support.module.scss":{"*":{"id":"(ssr)/./app/[locale]/ui/support/support.module.scss","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"D:\\---AProjects---\\imcam-officialsite\\fastoffical\\node_modules\\next\\dist\\client\\components\\app-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\---AProjects---\\imcam-officialsite\\fastoffical\\node_modules\\next\\dist\\esm\\client\\components\\app-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\---AProjects---\\imcam-officialsite\\fastoffical\\node_modules\\next\\dist\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\---AProjects---\\imcam-officialsite\\fastoffical\\node_modules\\next\\dist\\esm\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\---AProjects---\\imcam-officialsite\\fastoffical\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\---AProjects---\\imcam-officialsite\\fastoffical\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\---AProjects---\\imcam-officialsite\\fastoffical\\node_modules\\next\\dist\\client\\components\\not-found-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\---AProjects---\\imcam-officialsite\\fastoffical\\node_modules\\next\\dist\\esm\\client\\components\\not-found-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\---AProjects---\\imcam-officialsite\\fastoffical\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\---AProjects---\\imcam-officialsite\\fastoffical\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\---AProjects---\\imcam-officialsite\\fastoffical\\node_modules\\next\\dist\\client\\components\\static-generation-searchparams-bailout-provider.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\---AProjects---\\imcam-officialsite\\fastoffical\\node_modules\\next\\dist\\esm\\client\\components\\static-generation-searchparams-bailout-provider.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\not-found.tsx":{"id":"(app-pages-browser)/./app/not-found.tsx","name":"*","chunks":["app/not-found","static/chunks/app/not-found.js"],"async":false},"D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\[locale]\\page.module.scss":{"id":"(app-pages-browser)/./app/[locale]/page.module.scss","name":"*","chunks":["app/[locale]/not-found","static/chunks/app/%5Blocale%5D/not-found.js"],"async":false},"D:\\---AProjects---\\imcam-officialsite\\fastoffical\\node_modules\\next\\dist\\client\\image-component.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/image-component.js","name":"*","chunks":["app/[locale]/support/[type]/page","static/chunks/app/%5Blocale%5D/support/%5Btype%5D/page.js"],"async":false},"D:\\---AProjects---\\imcam-officialsite\\fastoffical\\node_modules\\next\\dist\\esm\\client\\image-component.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/image-component.js","name":"*","chunks":["app/[locale]/support/[type]/page","static/chunks/app/%5Blocale%5D/support/%5Btype%5D/page.js"],"async":false},"D:\\---AProjects---\\imcam-officialsite\\fastoffical\\node_modules\\next\\dist\\client\\link.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/link.js","name":"*","chunks":["app/[locale]/support/[type]/page","static/chunks/app/%5Blocale%5D/support/%5Btype%5D/page.js"],"async":false},"D:\\---AProjects---\\imcam-officialsite\\fastoffical\\node_modules\\next\\dist\\esm\\client\\link.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/link.js","name":"*","chunks":["app/[locale]/support/[type]/page","static/chunks/app/%5Blocale%5D/support/%5Btype%5D/page.js"],"async":false},"D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\[locale]\\ui\\components\\back-to-top.tsx":{"id":"(app-pages-browser)/./app/[locale]/ui/components/back-to-top.tsx","name":"*","chunks":["app/[locale]/layout","static/chunks/app/%5Blocale%5D/layout.js"],"async":false},"D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\[locale]\\ui\\home\\footer.tsx":{"id":"(app-pages-browser)/./app/[locale]/ui/home/<USER>","name":"*","chunks":["app/[locale]/layout","static/chunks/app/%5Blocale%5D/layout.js"],"async":false},"D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\[locale]\\ui\\home\\nav.tsx":{"id":"(app-pages-browser)/./app/[locale]/ui/home/<USER>","name":"*","chunks":["app/[locale]/layout","static/chunks/app/%5Blocale%5D/layout.js"],"async":false},"D:\\---AProjects---\\imcam-officialsite\\fastoffical\\node_modules\\next\\font\\google\\target.css?{\"path\":\"app\\\\[locale]\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"app\\\\[locale]\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}","name":"*","chunks":["app/[locale]/layout","static/chunks/app/%5Blocale%5D/layout.js"],"async":false},"D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\globals.scss":{"id":"(app-pages-browser)/./app/globals.scss","name":"*","chunks":["app/[locale]/layout","static/chunks/app/%5Blocale%5D/layout.js"],"async":false},"D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\[locale]\\ui\\home\\about.tsx":{"id":"(app-pages-browser)/./app/[locale]/ui/home/<USER>","name":"*","chunks":["app/[locale]/(overview)/page","static/chunks/app/%5Blocale%5D/(overview)/page.js"],"async":false},"D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\[locale]\\ui\\home\\banner-slider.tsx":{"id":"(app-pages-browser)/./app/[locale]/ui/home/<USER>","name":"*","chunks":["app/[locale]/(overview)/page","static/chunks/app/%5Blocale%5D/(overview)/page.js"],"async":false},"D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\[locale]\\ui\\home\\home.module.scss":{"id":"(app-pages-browser)/./app/[locale]/ui/home/<USER>","name":"*","chunks":["app/[locale]/(overview)/page","static/chunks/app/%5Blocale%5D/(overview)/page.js"],"async":false},"D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\[locale]\\ui\\components\\components.scss":{"id":"(app-pages-browser)/./app/[locale]/ui/components/components.scss","name":"*","chunks":["app/[locale]/(overview)/page","static/chunks/app/%5Blocale%5D/(overview)/page.js"],"async":false},"D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\[locale]\\support\\page.tsx":{"id":"(app-pages-browser)/./app/[locale]/support/page.tsx","name":"*","chunks":["app/[locale]/support/page","static/chunks/app/%5Blocale%5D/support/page.js"],"async":false},"D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\[locale]\\ui\\components\\page-tabs.tsx":{"id":"(app-pages-browser)/./app/[locale]/ui/components/page-tabs.tsx","name":"*","chunks":["app/[locale]/support/[type]/page","static/chunks/app/%5Blocale%5D/support/%5Btype%5D/page.js"],"async":false},"D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\[locale]\\ui\\support\\help.tsx":{"id":"(app-pages-browser)/./app/[locale]/ui/support/help.tsx","name":"*","chunks":["app/[locale]/support/[type]/page","static/chunks/app/%5Blocale%5D/support/%5Btype%5D/page.js"],"async":false},"D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\[locale]\\ui\\support\\support.module.scss":{"id":"(app-pages-browser)/./app/[locale]/ui/support/support.module.scss","name":"*","chunks":["app/[locale]/support/[type]/page","static/chunks/app/%5Blocale%5D/support/%5Btype%5D/page.js"],"async":false}},"entryCSSFiles":{"D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\layout":[],"D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\not-found":[],"D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\[locale]\\not-found":["static/css/app/[locale]/not-found.css"],"D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\[locale]\\layout":["static/css/app/[locale]/layout.css"],"D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\[locale]\\(overview)\\page":["static/css/app/[locale]/(overview)/page.css"],"D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\[locale]\\[...rest]\\page":[],"D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\[locale]\\support\\page":[],"D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\[locale]\\support\\layout":[],"D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\[locale]\\support\\[type]\\page":["static/css/app/[locale]/support/[type]/page.css"]}}