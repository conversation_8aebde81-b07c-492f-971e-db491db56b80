﻿<html>
<head lang="en">
    <meta charset="UTF-8"/>
    <meta content="mobiSiteGalore" name="Generator" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"/>
    <meta name="apple-touch-fullscreen" content="YES" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="viewport" content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0" />
    <meta name="format-detection" content="telephone=no">
    <title>Im Mate App</title>
    <style type="text/css">
        html { background: #ffffff; }
        body {
            margin: 0;
            padding: 0;
            font-family: Arial, sans-serif; /* 添加默认字体 */
        }
        .mt10 { margin-top: 0.625em; }
        .mt20 { margin-top: 1.25em; }
        .mt60 { margin-top: 3.75em; }
        .mt100 { margin-top: 6.25em; }
        .section1 { width: 100%; text-align: center; }
        .section1 img {
            width: 4.625em;
            height: 4.625em;
        }
        @media screen and (min-width:250px) and (max-width:768px) {
            .section1 img { text-align: center; margin: 0 auto; }
            .section1 .img { text-align: center; margin: 0 auto; }
        }
        @media screen and (min-width:768px) {
            .section1 img { text-align: center; margin: 50px auto; }
        }
        .appName { font-size: 1em; color: #222222; }
        .tip {
            font-size: 1em;
            color: #555555;
        }
        .download {
            background: #3A85FD;
            width: 8em;
            margin: 0 auto;
            padding: 0.7em 1em;
            border-radius: 8px;
            color: #fff;
            cursor: pointer; /* 添加手型光标 */
        }
        .radio-group {
            margin-top: 1.25em;
        }
        .radio-group label {
            margin-right: 1.25em;
            font-size: 1em; /* 确保字体大小合适 */
        }
    </style>
    <script src="./js/jquery-1.8.0.min.js"></script>
</head>
<body>
    <section class="section1 mt60">
        <img class="img" src="image/logo_imcam.png" />
    </section>
    <section class="section1 mt10">
        <div class="appName">IM Mate</div>
        <div class="tip mt20">Please select the type of mobile phone</div>
    </section>
    <section class="section1 mt10 radio-group">
        <input type="radio" id="android" name="deviceType" value="Android" class="androidType" checked>
        <label for="android">Android</label>

        <input type="radio" id="ios" name="deviceType" value="iPhone" class="iosType">
        <label for="ios">iPad|iPhone|iPod</label>
    </section>
    <section class="section1 mt20 downloadBox">
        <div class="download">Confirm</div>
    </section>

    <script>
        var language = navigator.language || navigator.userLanguage;
        if (language.indexOf('zh') > -1) {
            $(".appName").text("悦办");
            $(".tip").text('请选择手机类型');
            $(".download").text('确 认');
            $("label[for='android']").text('安卓系统');
            $("label[for='ios']").text('iOS系统');
        } else {
            $(".appName").text("IM Mate");
            $(".tip").text('Please select the type of mobile phone');
            $(".download").text('Confirm');
            $("label[for='android']").text('Android');
            $("label[for='ios']").text('iOS');
        }

        $(".download").click(function (event) {
            var deviceType = $("input[name='deviceType']:checked").val();
            document.location.href = "http://www.jfgou.com/app/imate_download.html?phoneType=" + deviceType;
        });
    </script>
</body>
</html>