"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/about/page",{

/***/ "(app-pages-browser)/./app/[locale]/ui/about/about-content.tsx":
/*!*************************************************!*\
  !*** ./app/[locale]/ui/about/about-content.tsx ***!
  \*************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ AboutContentLayout; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _about_module_scss__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./about.module.scss */ \"(app-pages-browser)/./app/[locale]/ui/about/about.module.scss\");\n/* harmony import */ var _about_module_scss__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(_about_module_scss__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _components_page_tabs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../components/page-tabs */ \"(app-pages-browser)/./app/[locale]/ui/components/page-tabs.tsx\");\n/* harmony import */ var _home_about__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../home/<USER>/ \"(app-pages-browser)/./app/[locale]/ui/home/<USER>");\n/* harmony import */ var _components_certificates__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../components/certificates */ \"(app-pages-browser)/./app/[locale]/ui/components/certificates.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _locales_client__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/locales/client */ \"(app-pages-browser)/./locales/client.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$(), _s3 = $RefreshSig$(), _s4 = $RefreshSig$(), _s5 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction AboutContentLayout() {\n    _s();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_locales_client__WEBPACK_IMPORTED_MODULE_7__.I18nProviderClient, {\n        locale: (0,_locales_client__WEBPACK_IMPORTED_MODULE_7__.useCurrentLocale)(),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AboutContent, {}, void 0, false, {\n            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n            lineNumber: 15,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n        lineNumber: 14,\n        columnNumber: 5\n    }, this);\n}\n_s(AboutContentLayout, \"9zQ3KOL0Rwq6cPrON/AdiF1Ksas=\", false, function() {\n    return [\n        _locales_client__WEBPACK_IMPORTED_MODULE_7__.useCurrentLocale\n    ];\n});\n_c = AboutContentLayout;\nfunction AboutContent() {\n    _s1();\n    const t = (0,_locales_client__WEBPACK_IMPORTED_MODULE_7__.useI18n)();\n    const [tabs, setTabs] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)([\n        {\n            id: \"0\",\n            text: t(\"aboutCylan\")\n        },\n        {\n            id: \"1\",\n            text: t(\"cylanPrides\")\n        },\n        {\n            id: \"2\",\n            text: t(\"contactUs\")\n        }\n    ]);\n    const [currentTab, setCurrentTab] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(tabs[0].id);\n    const [trigger, setTrigger] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(currentTab);\n    const contentRef = (0,react__WEBPACK_IMPORTED_MODULE_5__.useRef)(null);\n    const handleTabChange = (tab)=>{\n        setTrigger(tab);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default().about),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"\".concat((_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default().about__image), \" hide-on-small\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                    src: \"/about-banner.webp\",\n                    width: 1920,\n                    height: 900,\n                    alt: \"\",\n                    style: {\n                        width: \"100%\",\n                        height: \"100%\",\n                        objectFit: \"fill\",\n                        objectPosition: \"center\"\n                    },\n                    unoptimized: true\n                }, void 0, false, {\n                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                    lineNumber: 48,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                lineNumber: 47,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h6\", {\n                id: \"about-cylan\"\n            }, void 0, false, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                lineNumber: 62,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"hide-on-small\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_page_tabs__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    title: t(\"aboutUs\"),\n                    iconSrc: \"/about-icon.svg\",\n                    currentTab: currentTab,\n                    tabs: tabs,\n                    showBanner: false,\n                    background: \"transparent\",\n                    onTabChange: (tab)=>{\n                        handleTabChange(tab);\n                    }\n                }, void 0, false, {\n                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                    lineNumber: 64,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                lineNumber: 63,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"hide-on-medium hide-on-large\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_page_tabs__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    title: t(\"aboutUs\"),\n                    iconSrc: \"/about-icon.svg\",\n                    currentTab: currentTab,\n                    tabs: tabs,\n                    bannerMobileSrc: \"/about-banner-mobile.jpg\",\n                    background: \"rgb(214,218,211)\",\n                    onTabChange: (tab)=>{\n                        handleTabChange(tab);\n                    }\n                }, void 0, false, {\n                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                    lineNumber: 77,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                lineNumber: 76,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_5__.Suspense, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Content, {\n                    contentRef: contentRef,\n                    tabs: tabs,\n                    setCurrentTab: setCurrentTab,\n                    trigger: trigger\n                }, void 0, false, {\n                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                    lineNumber: 90,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                lineNumber: 89,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n        lineNumber: 46,\n        columnNumber: 5\n    }, this);\n}\n_s1(AboutContent, \"IfBYfwtzUcCp2dyH1DY6EqbD0y4=\", false, function() {\n    return [\n        _locales_client__WEBPACK_IMPORTED_MODULE_7__.useI18n\n    ];\n});\n_c1 = AboutContent;\nfunction Content(param) {\n    let { tabs, setCurrentTab = ()=>{}, contentRef, trigger } = param;\n    _s2();\n    const aboutCylanRef = (0,react__WEBPACK_IMPORTED_MODULE_5__.useRef)(null);\n    const pridesRef = (0,react__WEBPACK_IMPORTED_MODULE_5__.useRef)(null);\n    const contactRef = (0,react__WEBPACK_IMPORTED_MODULE_5__.useRef)(null);\n    const isInitial = (0,react__WEBPACK_IMPORTED_MODULE_5__.useRef)(true);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_6__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_6__.usePathname)();\n    const handleTabChange = (0,react__WEBPACK_IMPORTED_MODULE_5__.useCallback)((tab)=>{\n        let jump;\n        if (tab === tabs[0].id) jump = \"about-cylan\";\n        else if (tab === tabs[1].id) jump = \"prides\";\n        else jump = \"contacts\";\n        setCurrentTab(tab);\n        router.replace(\"\".concat(pathname, \"/#\").concat(jump));\n    }, [\n        tabs,\n        setCurrentTab,\n        router,\n        pathname\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_5__.useEffect)(()=>{\n        const handleScroll = ()=>{\n            var _aboutCylanRef_current;\n            const scrollHeight = window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop;\n            const top = (_aboutCylanRef_current = aboutCylanRef.current) === null || _aboutCylanRef_current === void 0 ? void 0 : _aboutCylanRef_current.getBoundingClientRect().top;\n            if (top && scrollHeight < top) setCurrentTab(tabs[0].id);\n        };\n        window.addEventListener(\"scroll\", handleScroll);\n        return ()=>{\n            window.removeEventListener(\"scroll\", handleScroll);\n        };\n    }, [\n        handleTabChange,\n        tabs,\n        setCurrentTab\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_5__.useEffect)(()=>{\n        if (isInitial.current) {\n            isInitial.current = false;\n        } else {\n            handleTabChange(trigger);\n        }\n    }, [\n        trigger,\n        handleTabChange\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_locales_client__WEBPACK_IMPORTED_MODULE_7__.I18nProviderClient, {\n        locale: (0,_locales_client__WEBPACK_IMPORTED_MODULE_7__.useCurrentLocale)(),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: contentRef,\n                className: (_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default().about__content),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        ref: aboutCylanRef,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Main, {}, void 0, false, {\n                            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                            lineNumber: 160,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                        lineNumber: 159,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Career, {}, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                        lineNumber: 163,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        ref: pridesRef,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Prides, {}, void 0, false, {\n                            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                            lineNumber: 165,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                        lineNumber: 164,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h6\", {\n                        id: \"contacts\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                        lineNumber: 167,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        ref: contactRef,\n                        className: \"\".concat((_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default().about__content__contacts), \" hide-on-small\"),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_home_about__WEBPACK_IMPORTED_MODULE_3__.Contacts, {\n                            isAboutPage: true\n                        }, void 0, false, {\n                            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                            lineNumber: 173,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                        lineNumber: 169,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                lineNumber: 158,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"\".concat((_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default().about__content__contacts), \" hide-on-medium hide-on-large \"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_home_about__WEBPACK_IMPORTED_MODULE_3__.Contacts, {\n                    isAboutPage: true\n                }, void 0, false, {\n                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                    lineNumber: 179,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                lineNumber: 176,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n        lineNumber: 157,\n        columnNumber: 5\n    }, this);\n}\n_s2(Content, \"fobuBSBxj+j+UAIR5uA8Zn5wrJ4=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_6__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_6__.usePathname,\n        _locales_client__WEBPACK_IMPORTED_MODULE_7__.useCurrentLocale\n    ];\n});\n_c2 = Content;\nfunction Main() {\n    _s3();\n    const t = (0,_locales_client__WEBPACK_IMPORTED_MODULE_7__.useI18n)();\n    // 公司概览 - 4个小块，每个有标题和内容\n    const overviewItems = [\n        {\n            label: t(\"companySetTime\"),\n            value: t(\"companySetTimeText\")\n        },\n        {\n            label: t(\"companyMainLocation\"),\n            value: t(\"companyMainLocationText\")\n        },\n        {\n            label: t(\"companyCoreTech\"),\n            value: t(\"companyCoreTechText\")\n        },\n        {\n            label: t(\"companyService\"),\n            value: t(\"companyServiceText\")\n        }\n    ];\n    // 核心产品 - 两个产品分组\n    const coreProductGroup1 = [\n        t(\"companyCoreProductText1\"),\n        t(\"companyCoreProductText2\"),\n        t(\"companyCoreProductText3\"),\n        t(\"companyCoreProductText4\"),\n        t(\"companyCoreProductText5\")\n    ];\n    const coreProductGroup2 = [\n        t(\"companyCoreProductText6\"),\n        t(\"companyCoreProductText7\"),\n        t(\"companyCoreProductText8\"),\n        t(\"companyCoreProductText9\"),\n        t(\"companyCoreProductText10\")\n    ];\n    // 全球化成果 - 分为左右两组\n    const globalLeftList = [\n        t(\"companyGlobalText1\"),\n        t(\"companyGlobalText2\"),\n        t(\"companyGlobalText3\")\n    ];\n    const globalRightList = [\n        t(\"companyGlobalText4\"),\n        t(\"companyGlobalText5\")\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default().about__content__main),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default().about__content__main__header),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        children: t(\"aboutCylanTitle\")\n                    }, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                        lineNumber: 229,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: (_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default().about__content__main__header__subtitle),\n                        children: \"全球智能看护解决方案领导者\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                        lineNumber: 230,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                lineNumber: 228,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default().about__content__main__layout),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default().about__content__main__left),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                                className: \"\".concat((_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default().about__content__main__panel), \" \").concat((_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default()[\"about__content__main__panel--overview\"])),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                        className: (_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default().about__content__main__panel__title),\n                                        children: t(\"companyReview\")\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                        lineNumber: 239,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default().about__content__main__panel__overview),\n                                        children: overviewItems.map((item, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: (_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default().about__content__main__panel__overview__item),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: (_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default().about__content__main__panel__overview__item__label),\n                                                        children: item.label\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                                        lineNumber: 243,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: (_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default().about__content__main__panel__overview__item__value),\n                                                        children: item.value\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                                        lineNumber: 244,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, i, true, {\n                                                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                                lineNumber: 242,\n                                                columnNumber: 17\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                        lineNumber: 240,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                lineNumber: 238,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                                className: \"\".concat((_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default().about__content__main__panel), \" \").concat((_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default()[\"about__content__main__panel--global\"])),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                        className: (_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default().about__content__main__panel__title),\n                                        children: t(\"companyGlobal\")\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                        lineNumber: 252,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default().about__content__main__panel__global),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: (_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default().about__content__main__panel__global__left),\n                                                children: globalLeftList.map((text, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: (_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default().about__content__main__panel__global__item),\n                                                        children: text\n                                                    }, i, false, {\n                                                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                                        lineNumber: 256,\n                                                        columnNumber: 19\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                                lineNumber: 254,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: (_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default().about__content__main__panel__global__right),\n                                                children: globalRightList.map((text, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: (_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default().about__content__main__panel__global__item),\n                                                        children: text\n                                                    }, i, false, {\n                                                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                                        lineNumber: 261,\n                                                        columnNumber: 19\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                                lineNumber: 259,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                        lineNumber: 253,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                lineNumber: 251,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                        lineNumber: 236,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"\".concat((_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default().about__content__main__panel), \" \").concat((_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default()[\"about__content__main__panel--coreproduct\"])),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                className: (_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default().about__content__main__panel__title),\n                                children: t(\"companyCoreProduct\")\n                            }, void 0, false, {\n                                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                lineNumber: 270,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default().about__content__main__panel__products),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default().about__content__main__panel__products__group),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h6\", {\n                                                children: coreProductGroup1[0]\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                                lineNumber: 273,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                children: coreProductGroup1.slice(1).map((text, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: text\n                                                    }, i, false, {\n                                                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                                        lineNumber: 276,\n                                                        columnNumber: 19\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                                lineNumber: 274,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                        lineNumber: 272,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default().about__content__main__panel__products__group),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h6\", {\n                                                children: coreProductGroup2[0]\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                                lineNumber: 281,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                children: coreProductGroup2.slice(1).map((text, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: text\n                                                    }, i, false, {\n                                                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                                        lineNumber: 284,\n                                                        columnNumber: 19\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                                lineNumber: 282,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                        lineNumber: 280,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                lineNumber: 271,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                        lineNumber: 269,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                lineNumber: 234,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n        lineNumber: 226,\n        columnNumber: 5\n    }, this);\n}\n_s3(Main, \"ZaixJaidTjOGprsR5fELIXmueMU=\", false, function() {\n    return [\n        _locales_client__WEBPACK_IMPORTED_MODULE_7__.useI18n\n    ];\n});\n_c3 = Main;\nfunction Career() {\n    _s4();\n    const t = (0,_locales_client__WEBPACK_IMPORTED_MODULE_7__.useI18n)();\n    let LineType;\n    (function(LineType) {\n        LineType[LineType[\"first\"] = 0] = \"first\";\n        LineType[LineType[\"normal\"] = 1] = \"normal\";\n        LineType[LineType[\"last\"] = 2] = \"last\";\n    })(LineType || (LineType = {}));\n    const items = [\n        {\n            title: \"参加2024年香港春季电子展\",\n            time: \"2024年4月\",\n            text: \"2024年香港春季电子产品展及香港国际资讯科技博览会推出二代触摸屏视频通话智能摄像机参展\",\n            imageSrc: \"/hotspot-image-1.png\"\n        },\n        {\n            title: \"参加2024年香港春季电子展\",\n            time: \"2024年4月\",\n            text: \"2024年香港春季电子产品展及香港国际资讯科技博览会推出二代触摸屏视频通话智能摄像机参展\",\n            imageSrc: \"/hotspot-image-1.png\"\n        },\n        {\n            title: \"参加2024年香港春季电子展\",\n            time: \"2024年4月\",\n            text: \"2024年香港春季电子产品展及香港国际资讯科技博览会推出二代触摸屏视频通话智能摄像机参展\",\n            imageSrc: \"/hotspot-image-1.png\"\n        },\n        {\n            title: \"参加2024年香港春季电子展\",\n            time: \"2024年4月\",\n            text: \"2024年香港春季电子产品展及香港国际资讯科技博览会推出二代触摸屏视频通话智能摄像机参展\",\n            imageSrc: \"/hotspot-image-1.png\"\n        },\n        {\n            title: \"参加2024年香港春季电子展\",\n            time: \"2024年4月\",\n            text: \"2024年香港春季电子产品展及香港国际资讯科技博览会推出二代触摸屏视频通话智能摄像机参展\",\n            imageSrc: \"/hotspot-image-1.png\"\n        }\n    ];\n    const TimeLineItem = (param)=>{\n        let { linetype = 0, isReverse = false, title, time, text, imageSrc } = param;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"\".concat((_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default().about__content__career__timeline__item), \" \").concat(isReverse ? (_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default()[\"about__content__career__timeline__item--reverse\"]) : \"\"),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"hide-on-small\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                        src: imageSrc,\n                                        width: 240,\n                                        height: 160,\n                                        alt: \"\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                        lineNumber: 363,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                    lineNumber: 362,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h6\", {\n                                    children: title\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                    lineNumber: 365,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                            lineNumber: 361,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                        lineNumber: 360,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {}, void 0, false, {\n                                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                lineNumber: 369,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {}, void 0, false, {\n                                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                    lineNumber: 371,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                lineNumber: 370,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {}, void 0, false, {\n                                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                lineNumber: 373,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                        lineNumber: 368,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"hide-on-medium hide-on-large\",\n                                children: time\n                            }, void 0, false, {\n                                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                lineNumber: 376,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"hide-on-medium hide-on-large\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                    src: imageSrc,\n                                    width: 285,\n                                    height: 190,\n                                    alt: \"\",\n                                    style: {\n                                        width: \"100%\",\n                                        height: \"100%\"\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                    lineNumber: 378,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                lineNumber: 377,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"hide-on-small\",\n                                children: time\n                            }, void 0, false, {\n                                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                lineNumber: 386,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h6\", {\n                                className: \"hide-on-medium hide-on-large\",\n                                children: title\n                            }, void 0, false, {\n                                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                lineNumber: 387,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: text\n                            }, void 0, false, {\n                                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                lineNumber: 388,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                        lineNumber: 375,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                lineNumber: 354,\n                columnNumber: 9\n            }, this)\n        }, void 0, false);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default().about__content__career),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                children: t(\"career\")\n            }, void 0, false, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                lineNumber: 397,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                children: t(\"careerDescription\")\n            }, void 0, false, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                lineNumber: 398,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default().about__content__career__companytime),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_home_about__WEBPACK_IMPORTED_MODULE_3__.CompanyTime, {\n                    isAboutPage: true\n                }, void 0, false, {\n                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                    lineNumber: 400,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                lineNumber: 399,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h6\", {\n                id: \"prides\"\n            }, void 0, false, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                lineNumber: 402,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n        lineNumber: 396,\n        columnNumber: 5\n    }, this);\n}\n_s4(Career, \"ZaixJaidTjOGprsR5fELIXmueMU=\", false, function() {\n    return [\n        _locales_client__WEBPACK_IMPORTED_MODULE_7__.useI18n\n    ];\n});\n_c4 = Career;\nfunction Prides() {\n    _s5();\n    const t = (0,_locales_client__WEBPACK_IMPORTED_MODULE_7__.useI18n)();\n    let ImageSize;\n    (function(ImageSize) {\n        ImageSize[ImageSize[\"small\"] = 0] = \"small\";\n        ImageSize[ImageSize[\"normal\"] = 1] = \"normal\";\n        ImageSize[ImageSize[\"large\"] = 2] = \"large\";\n    })(ImageSize || (ImageSize = {}));\n    const [showCoverList, setShowCoverList] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)([\n        {\n            text: t(\"pride1\"),\n            show: false\n        },\n        {\n            text: t(\"pride2\"),\n            show: false\n        },\n        {\n            text: t(\"pride3\"),\n            show: false\n        },\n        {\n            text: t(\"pride4\"),\n            show: false\n        },\n        {\n            text: t(\"pride5\"),\n            show: false\n        },\n        {\n            text: t(\"pride6\"),\n            show: false\n        },\n        {\n            text: t(\"pride7\"),\n            show: false\n        },\n        {\n            text: t(\"pride8\"),\n            show: false\n        },\n        {\n            text: t(\"pride9\"),\n            show: false\n        }\n    ]);\n    const ImageItem = (param)=>{\n        let { src, size = 1, index } = param;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            onClick: ()=>{\n                setShowCoverList(showCoverList.map((item, idx)=>{\n                    if (idx === index) item.show = !item.show;\n                    return item;\n                }));\n            },\n            className: \"\".concat((_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default().about__content__prides__list__item), \" \").concat(size === 2 ? (_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default()[\"about__content__prides__list__item--large\"]) : size === 1 ? (_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default()[\"about__content__prides__list__item--normal\"]) : (_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default()[\"about__content__prides__list__item--small\"])),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                    src: src,\n                    width: size === 2 ? 478 : 231,\n                    height: size === 0 ? 154 : 326,\n                    alt: \"\",\n                    style: {\n                        height: \"100%\",\n                        width: \"100%\",\n                        objectFit: \"fill\"\n                    },\n                    unoptimized: true\n                }, void 0, false, {\n                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                    lineNumber: 497,\n                    columnNumber: 9\n                }, this),\n                showCoverList[index].show && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default().about__content__prides__list__item__cover),\n                    children: showCoverList[index].text\n                }, void 0, false, {\n                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                    lineNumber: 506,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n            lineNumber: 481,\n            columnNumber: 7\n        }, this);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"\".concat((_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default().about__content__prides)),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    children: t(\"cylanPrides\")\n                }, void 0, false, {\n                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                    lineNumber: 517,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"hide-on-small\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default().about__content__prides__list),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ImageItem, {\n                                    src: \"/pride-image-1.jpg\",\n                                    index: 0\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                    lineNumber: 520,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ImageItem, {\n                                    src: \"/pride-image-2.jpg\",\n                                    size: 2,\n                                    index: 1\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                    lineNumber: 521,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ImageItem, {\n                                    src: \"/pride-image-3.jpg\",\n                                    index: 2\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                    lineNumber: 526,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ImageItem, {\n                                    src: \"/pride-image-4.jpg\",\n                                    index: 3\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                    lineNumber: 527,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                            lineNumber: 519,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default().about__content__prides__list),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ImageItem, {\n                                    src: \"/pride-image-5.jpg\",\n                                    size: 0,\n                                    index: 4\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                    lineNumber: 530,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ImageItem, {\n                                    src: \"/pride-image-6.jpg\",\n                                    size: 0,\n                                    index: 5\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                    lineNumber: 535,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ImageItem, {\n                                    src: \"/pride-image-7.jpg\",\n                                    size: 0,\n                                    index: 6\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                    lineNumber: 540,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ImageItem, {\n                                    src: \"/pride-image-8.jpg\",\n                                    size: 0,\n                                    index: 7\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                    lineNumber: 545,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ImageItem, {\n                                    src: \"/pride-image-9.jpg\",\n                                    size: 0,\n                                    index: 8\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                    lineNumber: 550,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                            lineNumber: 529,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                    lineNumber: 518,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_certificates__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                    lineNumber: 558,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n            lineNumber: 516,\n            columnNumber: 7\n        }, this)\n    }, void 0, false);\n}\n_s5(Prides, \"/VTK/rcuaULTrNtxGRekcBaFSek=\", false, function() {\n    return [\n        _locales_client__WEBPACK_IMPORTED_MODULE_7__.useI18n\n    ];\n});\n_c5 = Prides;\nvar _c, _c1, _c2, _c3, _c4, _c5;\n$RefreshReg$(_c, \"AboutContentLayout\");\n$RefreshReg$(_c1, \"AboutContent\");\n$RefreshReg$(_c2, \"Content\");\n$RefreshReg$(_c3, \"Main\");\n$RefreshReg$(_c4, \"Career\");\n$RefreshReg$(_c5, \"Prides\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/[locale]/ui/about/about-content.tsx\n"));

/***/ })

});