"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/about/page",{

/***/ "(app-pages-browser)/./app/[locale]/ui/about/about-content.tsx":
/*!*************************************************!*\
  !*** ./app/[locale]/ui/about/about-content.tsx ***!
  \*************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ AboutContentLayout; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _about_module_scss__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./about.module.scss */ \"(app-pages-browser)/./app/[locale]/ui/about/about.module.scss\");\n/* harmony import */ var _about_module_scss__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(_about_module_scss__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _components_page_tabs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../components/page-tabs */ \"(app-pages-browser)/./app/[locale]/ui/components/page-tabs.tsx\");\n/* harmony import */ var _home_about__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../home/<USER>/ \"(app-pages-browser)/./app/[locale]/ui/home/<USER>");\n/* harmony import */ var _components_certificates__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../components/certificates */ \"(app-pages-browser)/./app/[locale]/ui/components/certificates.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _locales_client__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/locales/client */ \"(app-pages-browser)/./locales/client.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$(), _s3 = $RefreshSig$(), _s4 = $RefreshSig$(), _s5 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction AboutContentLayout() {\n    _s();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_locales_client__WEBPACK_IMPORTED_MODULE_7__.I18nProviderClient, {\n        locale: (0,_locales_client__WEBPACK_IMPORTED_MODULE_7__.useCurrentLocale)(),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AboutContent, {}, void 0, false, {\n            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n            lineNumber: 15,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n        lineNumber: 14,\n        columnNumber: 5\n    }, this);\n}\n_s(AboutContentLayout, \"9zQ3KOL0Rwq6cPrON/AdiF1Ksas=\", false, function() {\n    return [\n        _locales_client__WEBPACK_IMPORTED_MODULE_7__.useCurrentLocale\n    ];\n});\n_c = AboutContentLayout;\nfunction AboutContent() {\n    _s1();\n    const t = (0,_locales_client__WEBPACK_IMPORTED_MODULE_7__.useI18n)();\n    const [tabs, setTabs] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)([\n        {\n            id: \"0\",\n            text: t(\"aboutCylan\")\n        },\n        {\n            id: \"1\",\n            text: t(\"cylanPrides\")\n        },\n        {\n            id: \"2\",\n            text: t(\"contactUs\")\n        }\n    ]);\n    const [currentTab, setCurrentTab] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(tabs[0].id);\n    const [trigger, setTrigger] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(currentTab);\n    const contentRef = (0,react__WEBPACK_IMPORTED_MODULE_5__.useRef)(null);\n    const handleTabChange = (tab)=>{\n        setTrigger(tab);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default().about),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"\".concat((_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default().about__image), \" hide-on-small\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                    src: \"/about-banner.webp\",\n                    width: 1920,\n                    height: 900,\n                    alt: \"\",\n                    style: {\n                        width: \"100%\",\n                        height: \"100%\",\n                        objectFit: \"fill\",\n                        objectPosition: \"center\"\n                    },\n                    unoptimized: true\n                }, void 0, false, {\n                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                    lineNumber: 48,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                lineNumber: 47,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h6\", {\n                id: \"about-cylan\"\n            }, void 0, false, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                lineNumber: 62,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"hide-on-small\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_page_tabs__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    title: t(\"aboutUs\"),\n                    iconSrc: \"/about-icon.svg\",\n                    currentTab: currentTab,\n                    tabs: tabs,\n                    showBanner: false,\n                    background: \"transparent\",\n                    onTabChange: (tab)=>{\n                        handleTabChange(tab);\n                    }\n                }, void 0, false, {\n                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                    lineNumber: 64,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                lineNumber: 63,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"hide-on-medium hide-on-large\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_page_tabs__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    title: t(\"aboutUs\"),\n                    iconSrc: \"/about-icon.svg\",\n                    currentTab: currentTab,\n                    tabs: tabs,\n                    bannerMobileSrc: \"/about-banner-mobile.jpg\",\n                    background: \"rgb(214,218,211)\",\n                    onTabChange: (tab)=>{\n                        handleTabChange(tab);\n                    }\n                }, void 0, false, {\n                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                    lineNumber: 77,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                lineNumber: 76,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_5__.Suspense, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Content, {\n                    contentRef: contentRef,\n                    tabs: tabs,\n                    setCurrentTab: setCurrentTab,\n                    trigger: trigger\n                }, void 0, false, {\n                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                    lineNumber: 90,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                lineNumber: 89,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n        lineNumber: 46,\n        columnNumber: 5\n    }, this);\n}\n_s1(AboutContent, \"IfBYfwtzUcCp2dyH1DY6EqbD0y4=\", false, function() {\n    return [\n        _locales_client__WEBPACK_IMPORTED_MODULE_7__.useI18n\n    ];\n});\n_c1 = AboutContent;\nfunction Content(param) {\n    let { tabs, setCurrentTab = ()=>{}, contentRef, trigger } = param;\n    _s2();\n    const aboutCylanRef = (0,react__WEBPACK_IMPORTED_MODULE_5__.useRef)(null);\n    const pridesRef = (0,react__WEBPACK_IMPORTED_MODULE_5__.useRef)(null);\n    const contactRef = (0,react__WEBPACK_IMPORTED_MODULE_5__.useRef)(null);\n    const isInitial = (0,react__WEBPACK_IMPORTED_MODULE_5__.useRef)(true);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_6__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_6__.usePathname)();\n    const handleTabChange = (0,react__WEBPACK_IMPORTED_MODULE_5__.useCallback)((tab)=>{\n        let jump;\n        if (tab === tabs[0].id) jump = \"about-cylan\";\n        else if (tab === tabs[1].id) jump = \"prides\";\n        else jump = \"contacts\";\n        setCurrentTab(tab);\n        router.replace(\"\".concat(pathname, \"/#\").concat(jump));\n    }, [\n        tabs,\n        setCurrentTab,\n        router,\n        pathname\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_5__.useEffect)(()=>{\n        const handleScroll = ()=>{\n            var _aboutCylanRef_current;\n            const scrollHeight = window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop;\n            const top = (_aboutCylanRef_current = aboutCylanRef.current) === null || _aboutCylanRef_current === void 0 ? void 0 : _aboutCylanRef_current.getBoundingClientRect().top;\n            if (top && scrollHeight < top) setCurrentTab(tabs[0].id);\n        };\n        window.addEventListener(\"scroll\", handleScroll);\n        return ()=>{\n            window.removeEventListener(\"scroll\", handleScroll);\n        };\n    }, [\n        handleTabChange,\n        tabs,\n        setCurrentTab\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_5__.useEffect)(()=>{\n        if (isInitial.current) {\n            isInitial.current = false;\n        } else {\n            handleTabChange(trigger);\n        }\n    }, [\n        trigger,\n        handleTabChange\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_locales_client__WEBPACK_IMPORTED_MODULE_7__.I18nProviderClient, {\n        locale: (0,_locales_client__WEBPACK_IMPORTED_MODULE_7__.useCurrentLocale)(),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: contentRef,\n                className: (_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default().about__content),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        ref: aboutCylanRef,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Main, {}, void 0, false, {\n                            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                            lineNumber: 160,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                        lineNumber: 159,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Career, {}, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                        lineNumber: 163,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        ref: pridesRef,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Prides, {}, void 0, false, {\n                            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                            lineNumber: 165,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                        lineNumber: 164,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h6\", {\n                        id: \"contacts\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                        lineNumber: 167,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        ref: contactRef,\n                        className: \"\".concat((_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default().about__content__contacts), \" hide-on-small\"),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_home_about__WEBPACK_IMPORTED_MODULE_3__.Contacts, {\n                            isAboutPage: true\n                        }, void 0, false, {\n                            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                            lineNumber: 173,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                        lineNumber: 169,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                lineNumber: 158,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"\".concat((_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default().about__content__contacts), \" hide-on-medium hide-on-large \"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_home_about__WEBPACK_IMPORTED_MODULE_3__.Contacts, {\n                    isAboutPage: true\n                }, void 0, false, {\n                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                    lineNumber: 179,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                lineNumber: 176,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n        lineNumber: 157,\n        columnNumber: 5\n    }, this);\n}\n_s2(Content, \"fobuBSBxj+j+UAIR5uA8Zn5wrJ4=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_6__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_6__.usePathname,\n        _locales_client__WEBPACK_IMPORTED_MODULE_7__.useCurrentLocale\n    ];\n});\n_c2 = Content;\nfunction Main() {\n    _s3();\n    const t = (0,_locales_client__WEBPACK_IMPORTED_MODULE_7__.useI18n)();\n    const overviewList = [\n        t(\"companySetTime\"),\n        t(\"companyMainLocation\"),\n        t(\"companyCoreTech\"),\n        t(\"companyService\")\n    ];\n    const coreProductList = [\n        t(\"companyCoreProductText1\"),\n        t(\"companyCoreProductText2\"),\n        t(\"companyCoreProductText3\"),\n        t(\"companyCoreProductText4\"),\n        t(\"companyCoreProductText5\"),\n        t(\"companyCoreProductText6\"),\n        t(\"companyCoreProductText7\"),\n        t(\"companyCoreProductText8\"),\n        t(\"companyCoreProductText9\"),\n        t(\"companyCoreProductText10\")\n    ];\n    const globalList = [\n        t(\"companyGlobalText1\"),\n        t(\"companyGlobalText2\"),\n        t(\"companyGlobalText3\"),\n        t(\"companyGlobalText4\"),\n        t(\"companyGlobalText5\")\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default().about__content__main),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default().about__content__main__header),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        children: t(\"aboutCylanTitle\")\n                    }, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                        lineNumber: 220,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: (_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default().about__content__main__header__dash),\n                        children: \"—\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                        lineNumber: 221,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: (_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default().about__content__main__header__subtitle),\n                        children: \"全球智能看护解决方案领导者\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                        lineNumber: 222,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                lineNumber: 219,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default().about__content__main__grid),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: (_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default().about__content__main__panel),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                className: (_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default().about__content__main__panel__title),\n                                children: t(\"companyReview\")\n                            }, void 0, false, {\n                                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                lineNumber: 228,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                className: (_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default().about__content__main__panel__list),\n                                children: overviewList.map((text, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: text\n                                    }, i, false, {\n                                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                        lineNumber: 231,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                lineNumber: 229,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                        lineNumber: 227,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"\".concat((_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default().about__content__main__panel), \" \").concat((_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default()[\"about__content__main__panel--emphasis\"])),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                className: (_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default().about__content__main__panel__title),\n                                children: t(\"companyCoreProduct\")\n                            }, void 0, false, {\n                                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                lineNumber: 237,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                className: \"\".concat((_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default().about__content__main__panel__list), \" \").concat((_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default()[\"about__content__main__panel__list--twoCols\"])),\n                                children: coreProductList.map((text, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: text\n                                    }, i, false, {\n                                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                        lineNumber: 240,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                lineNumber: 238,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                        lineNumber: 236,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"\".concat((_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default().about__content__main__panel), \" \").concat((_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default()[\"about__content__main__panel--span2\"])),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                className: (_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default().about__content__main__panel__title),\n                                children: t(\"companyGlobal\")\n                            }, void 0, false, {\n                                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                lineNumber: 246,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                className: (_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default().about__content__main__panel__list),\n                                children: globalList.map((text, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: text\n                                    }, i, false, {\n                                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                        lineNumber: 249,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                lineNumber: 247,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                        lineNumber: 245,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                lineNumber: 226,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n        lineNumber: 217,\n        columnNumber: 5\n    }, this);\n}\n_s3(Main, \"ZaixJaidTjOGprsR5fELIXmueMU=\", false, function() {\n    return [\n        _locales_client__WEBPACK_IMPORTED_MODULE_7__.useI18n\n    ];\n});\n_c3 = Main;\nfunction Career() {\n    _s4();\n    const t = (0,_locales_client__WEBPACK_IMPORTED_MODULE_7__.useI18n)();\n    let LineType;\n    (function(LineType) {\n        LineType[LineType[\"first\"] = 0] = \"first\";\n        LineType[LineType[\"normal\"] = 1] = \"normal\";\n        LineType[LineType[\"last\"] = 2] = \"last\";\n    })(LineType || (LineType = {}));\n    const items = [\n        {\n            title: \"参加2024年香港春季电子展\",\n            time: \"2024年4月\",\n            text: \"2024年香港春季电子产品展及香港国际资讯科技博览会推出二代触摸屏视频通话智能摄像机参展\",\n            imageSrc: \"/hotspot-image-1.png\"\n        },\n        {\n            title: \"参加2024年香港春季电子展\",\n            time: \"2024年4月\",\n            text: \"2024年香港春季电子产品展及香港国际资讯科技博览会推出二代触摸屏视频通话智能摄像机参展\",\n            imageSrc: \"/hotspot-image-1.png\"\n        },\n        {\n            title: \"参加2024年香港春季电子展\",\n            time: \"2024年4月\",\n            text: \"2024年香港春季电子产品展及香港国际资讯科技博览会推出二代触摸屏视频通话智能摄像机参展\",\n            imageSrc: \"/hotspot-image-1.png\"\n        },\n        {\n            title: \"参加2024年香港春季电子展\",\n            time: \"2024年4月\",\n            text: \"2024年香港春季电子产品展及香港国际资讯科技博览会推出二代触摸屏视频通话智能摄像机参展\",\n            imageSrc: \"/hotspot-image-1.png\"\n        },\n        {\n            title: \"参加2024年香港春季电子展\",\n            time: \"2024年4月\",\n            text: \"2024年香港春季电子产品展及香港国际资讯科技博览会推出二代触摸屏视频通话智能摄像机参展\",\n            imageSrc: \"/hotspot-image-1.png\"\n        }\n    ];\n    const TimeLineItem = (param)=>{\n        let { linetype = 0, isReverse = false, title, time, text, imageSrc } = param;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"\".concat((_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default().about__content__career__timeline__item), \" \").concat(isReverse ? (_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default()[\"about__content__career__timeline__item--reverse\"]) : \"\"),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"hide-on-small\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                        src: imageSrc,\n                                        width: 240,\n                                        height: 160,\n                                        alt: \"\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                        lineNumber: 326,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                    lineNumber: 325,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h6\", {\n                                    children: title\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                    lineNumber: 328,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                            lineNumber: 324,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                        lineNumber: 323,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {}, void 0, false, {\n                                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                lineNumber: 332,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {}, void 0, false, {\n                                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                    lineNumber: 334,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                lineNumber: 333,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {}, void 0, false, {\n                                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                lineNumber: 336,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                        lineNumber: 331,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"hide-on-medium hide-on-large\",\n                                children: time\n                            }, void 0, false, {\n                                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                lineNumber: 339,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"hide-on-medium hide-on-large\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                    src: imageSrc,\n                                    width: 285,\n                                    height: 190,\n                                    alt: \"\",\n                                    style: {\n                                        width: \"100%\",\n                                        height: \"100%\"\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                    lineNumber: 341,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                lineNumber: 340,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"hide-on-small\",\n                                children: time\n                            }, void 0, false, {\n                                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                lineNumber: 349,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h6\", {\n                                className: \"hide-on-medium hide-on-large\",\n                                children: title\n                            }, void 0, false, {\n                                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                lineNumber: 350,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: text\n                            }, void 0, false, {\n                                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                lineNumber: 351,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                        lineNumber: 338,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                lineNumber: 317,\n                columnNumber: 9\n            }, this)\n        }, void 0, false);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default().about__content__career),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                children: t(\"career\")\n            }, void 0, false, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                lineNumber: 360,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                children: t(\"careerDescription\")\n            }, void 0, false, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                lineNumber: 361,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default().about__content__career__companytime),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_home_about__WEBPACK_IMPORTED_MODULE_3__.CompanyTime, {\n                    isAboutPage: true\n                }, void 0, false, {\n                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                    lineNumber: 363,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                lineNumber: 362,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h6\", {\n                id: \"prides\"\n            }, void 0, false, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                lineNumber: 365,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n        lineNumber: 359,\n        columnNumber: 5\n    }, this);\n}\n_s4(Career, \"ZaixJaidTjOGprsR5fELIXmueMU=\", false, function() {\n    return [\n        _locales_client__WEBPACK_IMPORTED_MODULE_7__.useI18n\n    ];\n});\n_c4 = Career;\nfunction Prides() {\n    _s5();\n    const t = (0,_locales_client__WEBPACK_IMPORTED_MODULE_7__.useI18n)();\n    let ImageSize;\n    (function(ImageSize) {\n        ImageSize[ImageSize[\"small\"] = 0] = \"small\";\n        ImageSize[ImageSize[\"normal\"] = 1] = \"normal\";\n        ImageSize[ImageSize[\"large\"] = 2] = \"large\";\n    })(ImageSize || (ImageSize = {}));\n    const [showCoverList, setShowCoverList] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)([\n        {\n            text: t(\"pride1\"),\n            show: false\n        },\n        {\n            text: t(\"pride2\"),\n            show: false\n        },\n        {\n            text: t(\"pride3\"),\n            show: false\n        },\n        {\n            text: t(\"pride4\"),\n            show: false\n        },\n        {\n            text: t(\"pride5\"),\n            show: false\n        },\n        {\n            text: t(\"pride6\"),\n            show: false\n        },\n        {\n            text: t(\"pride7\"),\n            show: false\n        },\n        {\n            text: t(\"pride8\"),\n            show: false\n        },\n        {\n            text: t(\"pride9\"),\n            show: false\n        }\n    ]);\n    const ImageItem = (param)=>{\n        let { src, size = 1, index } = param;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            onClick: ()=>{\n                setShowCoverList(showCoverList.map((item, idx)=>{\n                    if (idx === index) item.show = !item.show;\n                    return item;\n                }));\n            },\n            className: \"\".concat((_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default().about__content__prides__list__item), \" \").concat(size === 2 ? (_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default()[\"about__content__prides__list__item--large\"]) : size === 1 ? (_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default()[\"about__content__prides__list__item--normal\"]) : (_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default()[\"about__content__prides__list__item--small\"])),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                    src: src,\n                    width: size === 2 ? 478 : 231,\n                    height: size === 0 ? 154 : 326,\n                    alt: \"\",\n                    style: {\n                        height: \"100%\",\n                        width: \"100%\",\n                        objectFit: \"fill\"\n                    },\n                    unoptimized: true\n                }, void 0, false, {\n                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                    lineNumber: 460,\n                    columnNumber: 9\n                }, this),\n                showCoverList[index].show && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default().about__content__prides__list__item__cover),\n                    children: showCoverList[index].text\n                }, void 0, false, {\n                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                    lineNumber: 469,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n            lineNumber: 444,\n            columnNumber: 7\n        }, this);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"\".concat((_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default().about__content__prides)),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    children: t(\"cylanPrides\")\n                }, void 0, false, {\n                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                    lineNumber: 480,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"hide-on-small\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default().about__content__prides__list),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ImageItem, {\n                                    src: \"/pride-image-1.jpg\",\n                                    index: 0\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                    lineNumber: 483,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ImageItem, {\n                                    src: \"/pride-image-2.jpg\",\n                                    size: 2,\n                                    index: 1\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                    lineNumber: 484,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ImageItem, {\n                                    src: \"/pride-image-3.jpg\",\n                                    index: 2\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                    lineNumber: 489,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ImageItem, {\n                                    src: \"/pride-image-4.jpg\",\n                                    index: 3\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                    lineNumber: 490,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                            lineNumber: 482,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default().about__content__prides__list),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ImageItem, {\n                                    src: \"/pride-image-5.jpg\",\n                                    size: 0,\n                                    index: 4\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                    lineNumber: 493,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ImageItem, {\n                                    src: \"/pride-image-6.jpg\",\n                                    size: 0,\n                                    index: 5\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                    lineNumber: 498,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ImageItem, {\n                                    src: \"/pride-image-7.jpg\",\n                                    size: 0,\n                                    index: 6\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                    lineNumber: 503,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ImageItem, {\n                                    src: \"/pride-image-8.jpg\",\n                                    size: 0,\n                                    index: 7\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                    lineNumber: 508,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ImageItem, {\n                                    src: \"/pride-image-9.jpg\",\n                                    size: 0,\n                                    index: 8\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                    lineNumber: 513,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                            lineNumber: 492,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                    lineNumber: 481,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_certificates__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                    lineNumber: 521,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n            lineNumber: 479,\n            columnNumber: 7\n        }, this)\n    }, void 0, false);\n}\n_s5(Prides, \"/VTK/rcuaULTrNtxGRekcBaFSek=\", false, function() {\n    return [\n        _locales_client__WEBPACK_IMPORTED_MODULE_7__.useI18n\n    ];\n});\n_c5 = Prides;\nvar _c, _c1, _c2, _c3, _c4, _c5;\n$RefreshReg$(_c, \"AboutContentLayout\");\n$RefreshReg$(_c1, \"AboutContent\");\n$RefreshReg$(_c2, \"Content\");\n$RefreshReg$(_c3, \"Main\");\n$RefreshReg$(_c4, \"Career\");\n$RefreshReg$(_c5, \"Prides\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/[locale]/ui/about/about-content.tsx\n"));

/***/ })

});