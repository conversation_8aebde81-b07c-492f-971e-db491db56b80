.about {
  position: relative;

  &__image {
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 1920px;
    height: 900px;
  }

  &__content {
    width: var(--width-content);
    margin: auto;
    position: relative;
    z-index: 1;
    display: flex;
    flex-direction: column;
    gap: 30px;

    >div {
      border-radius: 24px;
      background-color: #FFFFFFE6;
      padding: 30px 20px;

      h1 {
        text-align: center;
        margin-bottom: 16px;
      }

      p {}
    }

    &__main {
      // 装饰性背景容器，拟合设计的大卡片
      border: 1px solid rgba(0, 0, 0, 0.04);

      &__header {
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 6px;
        margin-bottom: 25px;

        h2 {
          margin: 0;
          font-weight: 400;
        }

        &__dash {
          color: #7aaef2;
        }

        &__subtitle {
          // 渐变色文字：从左到右
          background: linear-gradient(90deg, #0a72e8 0%, #7aaef2 50%, #0a72e8 100%);
          background-clip: text;
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          font-weight: 600;
          font-size: 24px;
          white-space: nowrap;
        }
      }

      &__layout {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 20px;
        align-items: start;
      }

      &__left {
        display: flex;
        flex-direction: column;
        gap: 20px;
      }

      &__panel {
        background: #fff;
        border: 1px solid rgba(0, 0, 0, 0.06);
        border-radius: 10px;
        padding: 20px 36px;
        background: linear-gradient(171deg, #d7e4fdcc 6.07%, #ffffffcc 38.48%);
        box-shadow: 0 2px 4px 0 #89898908;

        &__title {
          margin: 0 0 18px;
          color: #1E46B0FF;
          font-weight: 400;

          &--title2 {
            color: #0F8BB5FF;
          }

          &--title3 {
            color: #5A489EFF;
          }
        }

        &__list {
          margin: 0;
          padding-left: 0;
          list-style: none;
          display: grid;
          row-gap: 8px;

          li {
            line-height: 1.6;
            color: #333;
          }

          &--twoCols {
            grid-template-columns: 1fr 1fr;
            column-gap: 18px;
          }
        }

        &__line {
          width: 100%;
          height: 1px;
          background-color: #1E46B01A;
          margin-bottom: 18px;
        }

        &--span2 {
          grid-column: 1 / -1;
          background: linear-gradient(178deg, #e7e3f8cc 0.14%, #ffffffcc 32.79%);
          box-shadow: 0 2px 4px 0 #89898908;
        }

        &--emphasis {
          background: linear-gradient(178deg, #d3eef5cc 3.39%, #ffffffcc 26.64%);
          box-shadow: 0 2px 4px 0 #89898908;
        }

        &--right {
          // 右侧面板占满全高度
          grid-row: 1 / -1;
        }

        // 公司概览样式
        &__overview {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 16px;
          &__item {
            &__label {
              color: #666;
              font-size: 14px;
              margin-bottom: 4px;
            }
            &__value {
              color: #333;
              font-weight: 500;
              font-size: 15px;
            }
          }
        }

        // 全球化成果样式
        &__global {
          display: flex;
          gap: 20px;
          &__left, &__right {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 8px;
          }
          &__item {
            color: #333;
            line-height: 1.6;
            font-size: 14px;
          }
        }

        // 核心产品样式
        &__products {
          display: flex;
          flex-direction: column;
          gap: 24px;
          &__group {
            h6 {
              margin: 0 0 12px;
              color: #0a72e8;
              font-size: 16px;
              font-weight: 600;
            }
            ul {
              margin: 0;
              padding-left: 0;
              list-style: none;
              display: flex;
              flex-direction: column;
              gap: 6px;
              li {
                color: #333;
                line-height: 1.6;
                font-size: 14px;
                position: relative;
                padding-left: 16px;
                &:before {
                  content: '✓';
                  position: absolute;
                  left: 0;
                  color: #30b566;
                  font-weight: 700;
                }
              }
            }
          }
        }
      }

      // 旧结构兼容（可移除）
      &__view {
        display: none;
      }

      &__quotes {
        margin-top: 34px;
        display: flex;
        justify-content: space-around;

        &__item {
          width: 394px;
          height: 123px;
          display: flex;
          padding: 20px 30px;
          align-items: center;
          gap: 20px;

          >div {
            display: flex;
            flex-direction: column;
            gap: 10px;
            text-align: left;

            h5 {
              margin: 0;
            }

            span {
              color: var(--text-description);
              font-size: var(--font-medium);
            }
          }
        }
      }
    }

    &__career {
      &__companytime {
        margin-top: 30px;
      }

      &__timeline {
        margin-top: 30px;

        &__item {
          display: flex;
          justify-content: center;
          gap: 10px;

          >div:first-of-type {
            display: flex;
            justify-content: flex-end;
            width: 400px;

            >div {
              text-align: center;

              >div {
                border-radius: 6px;
                overflow: hidden;
              }

              h6 {
                margin-top: 10px;
              }
            }
          }

          >div:nth-of-type(2) {
            display: flex;
            flex-direction: column;
            align-items: center;

            >div:first-of-type {
              flex: 1;
              border-right: 1px solid var(--gray-4);
            }

            >div:nth-of-type(2) {
              display: flex;
              justify-content: center;
              align-items: center;
              width: 16px;
              height: 16px;

              >div {
                width: 6px;
                height: 6px;
                border-radius: 50%;
                background-color: var(--color-theme);
              }
            }

            >div:last-of-type {
              flex: 1;
              border-right: 1px solid var(--gray-4);
            }
          }

          >div:nth-of-type(3) {
            width: 400px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            gap: 10px;

            h4 {
              margin: 0;
            }

            p {
              color: var(--text-description);
            }
          }

          &--reverse {
            flex-direction: row-reverse;

            >div:first-of-type {
              justify-content: flex-start;
            }

            p,
            h4 {
              text-align: right;
            }
          }

          &:first-of-type {
            >div:nth-of-type(2) {
              >div:first-of-type {
                opacity: 0;
              }
            }
          }

          &:last-of-type {
            >div:nth-of-type(2) {
              >div:last-of-type {
                opacity: 0;
              }
            }
          }
        }
      }
    }

    &__prides {
      margin-top: 30px;

      &__list {
        display: flex;
        column-gap: 15px;
        row-gap: 16px;
        flex-wrap: nowrap;

        &__item {
          position: relative;

          &--normal {
            max-width: 231px;
            aspect-ratio: 231/326;
          }

          &--large {
            max-width: 478px;
            aspect-ratio: 478/326;
          }

          &--small {
            max-width: 231px;
            aspect-ratio: 231/154;
          }

          &__cover {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 1;
            background-color: rgba($color: #0075eb, $alpha: 0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            color: #fff;
            font-size: var(--font-large);
            border-radius: 6px;
            padding: 0 16px;
            text-align: center;
          }
        }

        &:last-of-type {
          margin-top: 16px;
        }
      }
    }

    &__contacts {
      background-color: transparent !important;
      padding: 0 !important;
      margin-top: 30px;
    }
  }
}

@media (min-width: 451px) and (max-width: 1280px) {
  .about {
    &__image {
      width: 100vw;
      height: 50vh;
    }

    &__content {
      padding: 0 20px;

      >div {
        padding: 20px 16px;
      }

      &__main {
        &__layout {
          grid-template-columns: 1fr;
          gap: 16px;
        }

        &__panel {
          &--right {
            grid-row: auto;
          }
          &__overview {
            grid-template-columns: 1fr;
            gap: 12px;
          }
          &__global {
            flex-direction: column;
            gap: 16px;
          }
        }
      }
    }
  }
}

@media (max-width: 450px) {
  .about {
    &__content {
      padding: 16px 16px 0;

      >div {
        padding: 20px 16px;
      }

      &__main {
        &__layout {
          grid-template-columns: 1fr;
          gap: 12px;
        }

        &__panel {
          &--right {
            grid-row: auto;
          }
          &__overview {
            grid-template-columns: 1fr;
            gap: 8px;
          }
          &__global {
            flex-direction: column;
            gap: 12px;
          }
        }

        &__quotes {
          flex-direction: column;
          justify-content: flex-start;
          gap: 20px;

          &__item {
            width: 100%;
            height: auto;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;

            h5 {
              text-align: center;
            }
          }
        }
      }

      &__career {
        &__timeline {
          margin-top: 20px;

          &__item {
            >div:last-of-type {
              >div {
                width: 100%;
                aspect-ratio: 285/190;
              }
            }

            h6 {
              margin: 0;
              text-align: left;
            }

            p {
              margin-bottom: 20px;
            }

            &--reverse {
              flex-direction: row;

              >div:first-of-type {
                justify-content: flex-end;
              }

              p,
              h4 {
                text-align: left;
              }
            }

            &:last-of-type {
              p {
                margin-bottom: 0;
              }
            }
          }
        }
      }

      &__prides {
        background-color: transparent !important;
        padding: 0 !important;
        margin-top: 0 !important;
      }

      &__contacts {
        margin-top: 0;
      }
    }
  }
}