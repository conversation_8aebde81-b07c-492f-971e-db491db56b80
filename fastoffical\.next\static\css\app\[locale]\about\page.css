/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[11].oneOf[13].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[11].oneOf[13].use[3]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[11].oneOf[13].use[4]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[11].oneOf[13].use[5]!./app/[locale]/ui/components/components.scss ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
@charset "UTF-8";
.flex-box-with-4items {
  display: flex;
  gap: 25.5px;
  flex-wrap: wrap;
}
.flex-box-with-4items__card {
  border-radius: 6px;
  background-color: #fff;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  width: 300px;
  position: relative;
}
.flex-box-with-4items__card__image {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
  width: 100%;
  border-radius: 6px;
  overflow: hidden;
  position: relative;
}
.flex-box-with-4items__card__image__play {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  z-index: 1;
}
.flex-box-with-4items__card__info {
  padding: 20px 16px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 6px;
  width: 100%;
}
.flex-box-with-4items__card__info div {
  font-size: var(--font-large);
}
.flex-box-with-4items__card__info span {
  color: var(--text-mark);
}
.flex-box-with-4items__card__info--product {
  text-align: center;
  padding: 10px 16px 20px;
}
.flex-box-with-4items__card__info--product span {
  color: var(--text-description);
}
.flex-box-with-4items__card__info--pride {
  gap: 0;
}
.flex-box-with-4items__card--video:hover, .flex-box-with-4items__card--link:hover {
  box-shadow: 0px 6px 20px 0px rgba(0, 0, 0, 0.1);
}
.flex-box-with-4items__card--video:hover .flex-box-with-4items__card__info > div, .flex-box-with-4items__card--link:hover .flex-box-with-4items__card__info > div {
  color: var(--color-theme);
}
.flex-box-with-4items--detail .flex-box-with-4items__card {
  width: 290px;
}

.flex-box-with-2items {
  display: flex;
  gap: 30px;
  flex-wrap: wrap;
}
.flex-box-with-2items__item {
  width: 625px;
  border-radius: 4px;
  display: flex;
  justify-content: space-between;
  background-color: #fff;
}
.flex-box-with-2items__item:hover {
  cursor: pointer;
}
.flex-box-with-2items__item > div:first-of-type {
  border-radius: 4px;
  overflow: hidden;
  width: 240px;
  height: 160px;
}
.flex-box-with-2items__item > div:last-of-type {
  display: flex;
  flex-direction: column;
  flex: 1 1;
  padding: 20px;
}
.flex-box-with-2items__item > div:last-of-type h6 {
  font-size: var(--font-large);
  font-weight: var(--font-bold);
  margin: 0;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}
.flex-box-with-2items__item > div:last-of-type p {
  color: var(--text-description);
  margin-top: 6px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}
.flex-box-with-2items__item > div:last-of-type span {
  color: var(--text-mark);
  margin-top: 8px;
}
.flex-box-with-2items--product-detail {
  gap: 0;
  column-gap: 20px;
  row-gap: 30px;
}
.flex-box-with-2items--product-detail .flex-box-with-2items__item {
  width: 600px;
}

.dropdown-window {
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translate(-50%, 100%);
  z-index: 5;
}
.dropdown-window__above {
  display: flex;
  justify-content: center;
}
.dropdown-window__list {
  background-color: #fff;
  border-radius: 10px;
  overflow: hidden;
  padding: 10px 1.5px;
}
.dropdown-window__list__item {
  min-width: 122px;
  text-align: center;
  height: 40px;
  line-height: 40px;
  color: var(--text-description);
}
.dropdown-window__list__item:hover {
  background-color: #f6f6f6;
  color: var(--color-theme);
  cursor: pointer;
}
.dropdown-window__list__item__link {
  width: 100%;
  height: 100%;
  text-align: center;
  line-height: 40px;
}
.dropdown-window__placeholder {
  position: absolute;
  width: 100%;
  height: 24px;
  top: -24px;
  left: 0;
}
.dropdown-window__mask {
  position: fixed;
  width: 100vw;
  height: 200vh;
  top: 0;
  left: 0;
  z-index: 4;
}

.bread-crumbs {
  display: flex;
  gap: 5.5px;
  align-items: center;
}
.bread-crumbs__item {
  color: var(--text-description);
  font-size: var(--font-small);
}
.bread-crumbs__item--current {
  composes: bread-crumbs__item;
  color: var(--text-mark);
}

.banner {
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  height: 100%;
  width: calc(100vw - 10px);
}
.banner__image {
  width: 100%;
  height: 100%;
  object-fit: cover; /* 保持图像原始尺寸 */
  object-position: center; /* 将图像的中心点显示在容器的中心 */
}

.pagination {
  display: flex;
  gap: 10px;
  justify-content: center;
}
.pagination__page, .pagination__pagejumper {
  padding: 0 14px;
  height: 32px;
  color: var(--text-description);
  border-radius: 4px;
  background-color: #fff;
}
.pagination__page--active, .pagination__pagejumper--active {
  color: #fff;
  background-color: var(--color-theme);
}
.pagination__page:disabled, .pagination__pagejumper:disabled {
  opacity: 0.6;
}

.page-tabs {
  height: 220px;
  position: relative;
  background-color: rgb(179, 220, 252);
}
.page-tabs__content {
  width: var(--width-content);
  margin: auto;
  height: 100%;
  position: relative;
}
.page-tabs__content__title {
  display: flex;
  position: absolute;
  top: 31px;
  left: 20px;
  gap: 8px;
}
.page-tabs__content__title h1 {
  color: #fff;
}
.page-tabs__content__items {
  display: flex;
  gap: 14px;
  position: absolute;
  top: 50%;
  left: 0;
  width: 100%;
  justify-content: center;
  transform: translateY(-50%);
}
.page-tabs__content__items__item {
  height: 42px;
  padding: 0 30px;
  border-radius: 4px;
  font-size: var(--font-medium);
  color: var(--text-description);
  line-height: 42px;
  background-color: #fff;
}
.page-tabs__content__items__item--active {
  background-color: var(--color-theme);
  color: #fff;
}
.page-tabs__search {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -59%);
  width: 680px;
  height: 44px;
  display: flex;
  border-radius: 6px;
  overflow: hidden;
  background-color: rgba(255, 255, 255, 0.8);
}
.page-tabs__search input {
  border: none;
  flex: 1 1;
  padding: 0 20px;
  color: var(--text-dark);
}
.page-tabs__search input::placeholder {
  color: var(--text-mark);
}
.page-tabs__search button {
  padding: 0 20px;
  height: 44px;
  line-height: 44px;
  border-radius: 6px;
  color: #fff;
  background-color: var(--color-theme);
  font-size: var(--font-medium);
  display: flex;
  align-items: center;
  gap: 6px;
}

.nav-list {
  padding: 20px;
  background-color: #fff;
  width: 100vw;
}
.nav-list__item {
  display: flex;
  align-items: center;
  border-bottom: 1px solid var(--gray-4);
  height: 50px;
  line-height: 50px;
}
.nav-list__item:hover {
  cursor: pointer;
}
.nav-list__item:last-of-type {
  border-bottom: none;
}
.nav-list__item__icon {
  display: flex;
  align-items: center;
  justify-content: center;
}
.nav-list__item__title {
  font-weight: var(--font-bolder);
  margin-left: 10px;
}
.nav-list__item__button {
  width: 32px;
  height: 32px;
}
.nav-list__item--sub .nav-list__item__title {
  font-weight: normal;
}
.nav-list--footer {
  background-color: #282c30;
}
.nav-list--footer .nav-list__item {
  border-bottom: none;
  box-shadow: 0px 1px 0px 0px #3a3e42;
}
.nav-list--footer .nav-list__item__title {
  color: var(--gray-4);
}
.nav-list--footer .nav-list__item--sub .nav-list__item__title {
  color: var(--gray-2);
  padding-left: 20px;
}
.nav-list--footer .nav-list__item__icon {
  display: none;
}

.back-top {
  position: fixed;
  bottom: 267px;
  right: 20px;
  z-index: 5;
  padding: 10px;
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: #fff;
  border-radius: 6px;
}
.back-top span {
  font-size: var(--font-small);
  color: var(--text-description);
}
.back-top--hide {
  display: none;
}

.show-more {
  width: 100%;
  height: 49px;
  text-align: center;
  line-height: 49px;
  color: var(--text-description);
  background-color: #fff;
  margin-top: 15px;
  border-radius: 6px;
}

.cylan-certificates {
  width: 100%;
  white-space: nowrap;
  overflow: hidden;
  overflow-x: scroll;
  margin-top: 20px;
  padding: 0 16px;
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
  overflow: -moz-scrollbars-none; /* Firefox */
}
.cylan-certificates ::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Opera */
}
.cylan-certificates > div {
  display: inline-block;
  margin-right: 10px;
}
.cylan-certificates > div > div :first-of-type {
  box-shadow: 0px 6px 20px 0px rgba(0, 0, 0, 0.1);
  border-radius: 6px;
  overflow: hidden;
}
.cylan-certificates > div > div:last-of-type {
  margin-top: 15px;
  text-align: center;
  font-size: var(--font-medium);
  color: var(--text-description);
  white-space: wrap;
  display: -webkit-box;
  -webkit-line-clamp: 2; /* 最多显示 3 行 */
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  height: 40px;
}

@media (min-width: 451px) and (max-width: 1280px) {
  .flex-box-with-4items {
    gap: 20px;
  }
  .flex-box-with-4items--product {
    justify-content: space-between;
    gap: 0;
    row-gap: 23px;
    column-gap: 0;
  }
  .flex-box-with-4items__card {
    width: calc(25% - 15px);
  }
  .flex-box-with-4items__card__info div {
    font-size: var(--font-normal);
  }
  .flex-box-with-4items__card__info--pride div {
    font-size: var(--font-medium);
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
  }
  .flex-box-with-4items__card--product {
    width: calc(50% - 10px);
  }
  .flex-box-with-4items--detail .flex-box-with-4items__card {
    width: calc(25% - 15px);
  }
  .flex-box-with-2items {
    flex-direction: column;
    flex-wrap: nowrap;
    gap: 20px;
  }
  .flex-box-with-2items__item {
    width: 100%;
    height: 160px;
  }
  .flex-box-with-2items__item > div:first-of-type {
    width: 240px;
    height: 100%;
  }
  .flex-box-with-2items__item > div:last-of-type {
    flex: 1 1;
    height: 100%;
    padding: 20px;
    display: flex;
    flex-direction: column;
  }
  .flex-box-with-2items--product-detail .flex-box-with-2items__item {
    width: 100%;
  }
  .banner {
    width: calc(100vw - 10px);
  }
  .page-tabs {
    height: 176px;
  }
  .page-tabs__search {
    top: auto;
    bottom: 46px;
    transform: translate(-50%);
  }
}
@media (max-width: 450px) {
  .flex-box-with-4items {
    gap: 15px;
  }
  .flex-box-with-4items__card {
    width: 100%;
  }
  .flex-box-with-4items__card__info--product {
    padding: 0 20px 10px;
  }
  .flex-box-with-4items--detail .flex-box-with-4items__card {
    width: 100%;
  }
  .flex-box-with-2items {
    flex-direction: column;
    gap: 15px;
  }
  .flex-box-with-2items__item {
    width: 100%;
    height: 93px;
  }
  .flex-box-with-2items__item > div:first-of-type {
    width: 140px;
    height: 100%;
  }
  .flex-box-with-2items__item > div:last-of-type {
    flex: 1 1;
    height: 100%;
    padding: 7px 8px;
    flex-direction: column;
    gap: 0;
    justify-content: space-between;
  }
  .flex-box-with-2items--product-detail .flex-box-with-2items__item {
    width: 100%;
  }
  .back-top {
    bottom: 50px;
  }
  .banner {
    width: 100vw;
  }
  .page-tabs {
    height: 120px;
  }
  .page-tabs__search {
    top: auto;
    bottom: 46px;
    transform: translate(-50%);
  }
  .page-tabs__content__title {
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
  }
  .page-tabs__tabs-small {
    background-color: #fff;
    display: flex;
    padding: 0 16px;
    gap: 10px;
    width: 100vw;
    overflow-x: scroll;
    -ms-overflow-style: none; /* IE and Edge */
    scrollbar-width: none; /* Firefox */
    overflow: -moz-scrollbars-none; /* Firefox */
  }
  .page-tabs__tabs-small ::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Opera */
  }
  .page-tabs__tabs-small__tab {
    height: 48px;
    text-align: center;
    line-height: 48px;
    padding: 0 10px;
    flex-shrink: 0;
    color: var(--text-description);
    font-size: var(--font-medium);
  }
  .page-tabs__tabs-small__tab--active {
    color: var(--color-theme);
    position: relative;
  }
  .page-tabs__tabs-small__tab--active::after {
    content: " ";
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 26px;
    height: 3px;
    border-radius: 2px;
    background-color: var(--color-theme);
  }
}
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[11].oneOf[9].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[11].oneOf[9].use[3]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[11].oneOf[9].use[4]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[11].oneOf[9].use[5]!./app/[locale]/ui/home/<USER>
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
@charset "UTF-8";
.home_nav--scrolled__f5oaX {
  box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.1);
}

.home_nav__gr65i {
  z-index: 10;
  width: 100vw;
  background-color: #fff;
  height: 66px;
  position: fixed;
  top: 0;
  left: 0;
}
.home_nav__placeholder__R_bDj {
  height: 66px;
}
.home_nav__content__gXoig {
  display: flex;
  align-items: center;
  height: 66px;
  line-height: 66px;
  width: var(--width-content);
  margin: auto;
}
.home_nav__list__dmRBz {
  display: flex;
  gap: 34px;
  margin: auto;
}
.home_nav__list__item__Ti9E4 {
  height: 66px;
  line-height: 66px;
  display: flex;
  align-items: center;
  gap: 5.5px;
  font-size: var(--font-medium);
}
.home_nav__list__item__Ti9E4:hover {
  cursor: pointer;
}
.home_nav__list__item--link__dx88I:hover {
  color: var(--color-theme);
}
.home_nav__list__item--active__oPRJX {
  color: var(--color-theme);
  border-bottom: 2px solid var(--color-theme);
}
.home_nav__right__4GRXj {
  display: flex;
  gap: 10px;
  align-items: center;
}
.home_nav__right__language__YzU4O {
  width: 108px;
  display: flex;
  justify-content: center;
  gap: 8px;
  font-size: var(--font-medium);
}
.home_nav__right__language__text__yUNmB {
  color: var(--text-mark);
  font-size: var(--font-medium);
}
.home_nav__right__language__text__yUNmB:hover {
  cursor: pointer;
}
.home_nav__right__language__text--active__e4h1y {
  color: var(--text-dark);
}
.home_nav__right__language__text--active__e4h1y:hover {
  cursor: default;
}
.home_nav__right__search__QAvd_ {
  width: 44px;
  height: 44px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: auto;
}
.home_nav__right__menu__tMG4s {
  width: 49px;
  height: 50px;
}
.home_nav__drop__RNd3y {
  position: fixed;
  top: 66px;
  left: 0;
  z-index: 9;
}
.home_nav__mask__YVj5E {
  position: fixed;
  z-index: 8;
  left: 0;
  top: 0;
  width: 100vw;
  height: 1000vh;
  background-color: rgba(0, 0, 0, 0.3);
}

.home_banner-slider__UBj9I {
  position: relative;
  height: 500px;
  background-color: #c4c4c4;
  overflow: hidden;
}
.home_banner-slider__swiper__9Bl8q {
  width: 100%;
  height: 100%;
  max-width: 2560px;
  margin: auto;
}
.home_banner-slider__slide__2U7Uu {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgb(13, 114, 153);
}
.home_banner-slider__button__GKjGy {
  position: absolute;
  width: 44px;
  height: 44px;
  top: 50%;
  transform: translateY(-50%);
  z-index: 10;
  cursor: pointer;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}
.home_banner-slider__button__GKjGy:hover {
  background: rgba(0, 0, 0, 0.5);
  transform: translateY(-50%) scale(1.1);
}
.home_banner-slider__button__GKjGy.home_swiper-button-disabled__siaDk {
  opacity: 0.3;
  cursor: not-allowed;
}
.home_banner-slider__button__GKjGy.home_swiper-button-disabled__siaDk:hover {
  background: rgba(0, 0, 0, 0.3);
  transform: translateY(-50%) scale(1);
}
.home_banner-slider__button-prev__VeikB {
  left: 100px;
}
.home_banner-slider__button-next__UC5d2 {
  right: 100px;
}
.home_banner-slider__pagination__J58et {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 18px;
  z-index: 10;
}
.home_banner-slider__bullet__c3a9X {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.5);
  cursor: pointer;
  transition: all 0.3s ease;
  opacity: 1;
}
.home_banner-slider__bullet__c3a9X:hover {
  background-color: rgba(255, 255, 255, 0.8);
  transform: scale(1.2);
}
.home_banner-slider__bullet--active__5BpSZ {
  background-color: #fff;
  transform: scale(1.2);
}
@media (max-width: 768px) {
  .home_banner-slider__UBj9I {
    height: 300px;
  }
  .home_banner-slider__switcher__SoaxS {
    left: 20px;
    width: 36px;
    height: 36px;
  }
  .home_banner-slider__switcher--right___84yN {
    right: 20px;
  }
  .home_banner-slider__indicator__0OOU4 {
    bottom: 15px;
    gap: 12px;
  }
  .home_banner-slider__indicator__item__f8vBh {
    width: 10px;
    height: 10px;
  }
}
@media (max-width: 480px) {
  .home_banner-slider__UBj9I {
    height: 250px;
  }
  .home_banner-slider__switcher__SoaxS {
    left: 10px;
    width: 32px;
    height: 32px;
  }
  .home_banner-slider__switcher--right___84yN {
    right: 10px;
  }
}

.home_hot-spot__HmXBc {
  width: var(--width-content);
  margin: auto;
  margin-top: 34px;
}
.home_hot-spot__captain__P7sAg {
  display: flex;
  height: 31px;
  align-items: center;
  line-height: 31px;
  margin-bottom: 16px;
  position: relative;
}
.home_hot-spot__captain__P7sAg h2 {
  margin: 0;
}
.home_hot-spot__captain__more__hoe30 {
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  height: 31px;
  line-height: 31px;
  align-items: center;
  gap: 4px;
  font-size: var(--font-small);
  color: var(--text-description);
}
.home_hot-spot__news__mFPbX {
  display: flex;
  justify-content: space-between;
}
.home_hot-spot__news__left__bYNbF, .home_hot-spot__news__right__IYxxG {
  width: 625px;
  height: 350px;
}
.home_hot-spot__news__left__bYNbF {
  border-radius: 6px;
  background-color: #fff;
  overflow: hidden;
}
.home_hot-spot__news__right__IYxxG {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.home_hot-spot__news__item__i6svw {
  height: 160px;
  border-radius: 6px;
  background-color: #fff;
  display: flex;
  overflow: hidden;
}
.home_hot-spot__news__item__info__GSDkz {
  height: 100%;
}
.home_hot-spot__news__item__image__0Dj0A {
  width: 625px;
  height: 240px;
}
.home_hot-spot__news__item__image--right__scey9 {
  width: 240px;
  height: 160px;
}
.home_hot-spot__news__item__info__GSDkz {
  padding: 16.5px 20px;
  display: flex;
  flex-direction: column;
  gap: 6px;
}
.home_hot-spot__news__item__info__GSDkz div {
  color: var(--text-description);
  overflow: hidden;
  text-overflow: ellipsis;
}
.home_hot-spot__news__item__info__GSDkz span {
  display: block;
  font-size: var(--font-small);
}
.home_hot-spot__news__item--left__W7YL9 {
  width: 100%;
  height: 100%;
  display: block;
}
.home_hot-spot__news__item--left__W7YL9 .home_hot-spot__news__item__info__GSDkz div {
  white-space: nowrap;
}

.home_about__vPbFi {
  margin-top: 30px;
  position: relative;
}
.home_about__cover__SPvuD {
  position: absolute;
  top: 0;
  left: 0;
  height: 221px;
  width: 100%;
  background: linear-gradient(0deg, #f5f7fa 0%, #0078e7);
}
.home_about__content__EA9EW {
  position: relative;
  z-index: 1;
  width: var(--width-content);
  margin: auto;
  padding-top: 30px;
}
.home_about__content__EA9EW h3 {
  color: #fff;
}
.home_about__content__time__HcHq6 {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 97px;
  margin-top: 12px;
}
.home_about__content__time__item__n4W8C {
  color: #fff;
  text-align: center;
}
.home_about__content__time__item__n4W8C div:first-of-type {
  height: 42px;
  line-height: 42px;
  display: flex;
  align-items: center;
  gap: 4px;
}
.home_about__content__time__item__n4W8C div:first-of-type span:first-of-type {
  font-size: 35px;
  font-weight: 500;
}
.home_about__content__time__item__n4W8C div:first-of-type span:last-of-type {
  font-size: var(--font-medium);
}
.home_about__content__time--page__Azkeq .home_about__content__time__item__n4W8C {
  color: var(--text-dark);
}
.home_about__content__time--page__Azkeq .home_about__content__time__item__n4W8C div:last-of-type {
  color: var(--text-description);
}
.home_about__content__prides__zHCpT {
  margin-top: 34px;
}

.home_contacts__TRH4N {
  display: flex;
  justify-content: space-between;
  margin-top: 52px;
}
.home_contacts--page__0BV0w {
  margin-top: 0;
}
.home_contacts__info__pIGy0 {
  width: 625px;
  height: 249px;
  border-radius: 6px;
  background-color: #fff;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  gap: 14px;
  padding: 20px;
}
.home_contacts__info__items__qUSi9 {
  margin: auto 0;
  display: flex;
  flex-direction: column;
  gap: 14px;
  justify-content: center;
}
.home_contacts__info__title__3_UHT {
  font-size: var(--font-large);
}
.home_contacts__info__item__eDIm0 {
  display: flex;
  gap: 12px;
  color: var(--text-description);
}
.home_contacts__address___ZQdr {
  width: 625px;
  height: 249px;
  border-radius: 6px;
  overflow: hidden;
}

.home_footer__qefFZ {
  margin-top: 38px;
  background-color: #282c30;
}
.home_footer__qefFZ > div {
  width: var(--width-content);
  margin: auto;
}
.home_footer__logo__jG71u {
  margin-right: 48px;
}
.home_footer__links__q5uiZ {
  padding: 20px;
  display: flex;
  justify-content: center;
  gap: 51px;
}
.home_footer__links__item__gB0TO {
  display: flex;
  flex-direction: column;
  gap: 10px;
  width: 140px;
  align-items: flex-start;
}
.home_footer__links__item__gB0TO div {
  color: var(--gray-4);
  font-weight: var(--font-bolder);
}
.home_footer__links__item__gB0TO span {
  color: var(--gray-1);
  font-weight: 400;
}
.home_footer__links__follow__jv8nP {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 10px;
}
.home_footer__links__follow__jv8nP > div {
  color: var(--gray-1);
}
.home_footer__links__follow__weixin__yeCNp {
  position: relative;
}
.home_footer__links__follow__weixin__yeCNp > div {
  position: absolute;
  z-index: 3;
  left: 80%;
  bottom: 80%;
  background-color: #fff;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-bottom: 6px;
}
.home_footer__links__follow__weixin__yeCNp > div span {
  text-align: center;
  color: var(--text-description);
  padding-bottom: 4px;
  font-size: var(--font-small);
}
.home_footer__links__follow__weixin__yeCNp > div a {
  color: var(--color-theme);
  font-size: var(--font-small);
}
.home_footer__copyright__M6lua {
  text-align: center;
  font-size: var(--font-small);
  color: var(--gray);
  padding: 10px 0;
}
.home_footer__copyright__link__PBT0B:hover {
  color: #fff;
}

@media (min-width: 451px) and (max-width: 1280px) {
  .home_nav__content__gXoig {
    justify-content: space-between;
    padding: 0 15px;
  }
  .home_hot-spot__HmXBc {
    padding: 0 20px;
  }
  .home_hot-spot__captain__P7sAg h2 {
    text-align: left;
    margin: 0;
  }
  .home_hot-spot__news__left__bYNbF, .home_hot-spot__news__right__IYxxG {
    width: calc(50% - 10px);
  }
  .home_hot-spot__news__item__image__0Dj0A {
    width: 100%;
    height: 71%;
  }
  .home_hot-spot__news__item__image--right__scey9 {
    width: 50%;
    height: 100%;
  }
  .home_hot-spot__news__item__info__GSDkz {
    width: 50%;
    height: 100%;
  }
  .home_hot-spot__news__item__info__GSDkz h4 {
    font-size: var(--font-medium);
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .home_hot-spot__news__item__info__GSDkz div {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: normal;
  }
  .home_hot-spot__news__item--left__W7YL9 .home_hot-spot__news__item__info__GSDkz {
    height: 29%;
    width: 100%;
  }
  .home_hot-spot__news__item--left__W7YL9 .home_hot-spot__news__item__info__GSDkz h4 {
    display: block;
    font-size: 20px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    margin: 0;
    text-align: center;
    padding: 0 10px;
  }
  .home_hot-spot__news__item--left__W7YL9 .home_hot-spot__news__item__info__GSDkz div {
    white-space: normal;
  }
  .home_hot-spot__news__item--left__W7YL9 .home_hot-spot__news__item__info__GSDkz span {
    display: none;
  }
  .home_about__content__EA9EW {
    padding: 30px 20px;
  }
  .home_contacts__info__pIGy0, .home_contacts__address___ZQdr {
    width: calc(50% - 10px);
  }
  .home_footer__links__q5uiZ {
    gap: 0;
    width: 120px;
  }
  .home_banner-slider__UBj9I {
    height: 400px;
  }
}
@media (max-width: 450px) {
  .home_hot-spot__HmXBc {
    padding: 0 16px;
  }
  .home_footer__qefFZ {
    margin-top: 35px;
  }
  .home_footer__logo__jG71u {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px 0 15px 0;
  }
  .home_footer__links__follow__jv8nP {
    padding-left: 30px;
  }
  .home_about__vPbFi h3 {
    padding-left: 16px;
  }
  .home_about__content__time__HcHq6 {
    gap: 14px;
    justify-content: center;
  }
  .home_about__content__time__item__n4W8C {
    max-width: 140px;
  }
  .home_about__content__time__item__n4W8C div:first-of-type {
    justify-content: center;
  }
  .home_hot-spot__news__mFPbX {
    flex-wrap: wrap;
    gap: 15px;
  }
  .home_hot-spot__news__left__bYNbF, .home_hot-spot__news__right__IYxxG {
    width: 100%;
    height: auto;
  }
  .home_hot-spot__news__right__IYxxG {
    gap: 15px;
    justify-content: flex-start;
  }
  .home_hot-spot__news__item__i6svw {
    height: 93px;
  }
  .home_hot-spot__news__item__info__GSDkz {
    padding: 7px 8px;
    justify-content: center;
    gap: 14px;
  }
  .home_hot-spot__news__item__info__GSDkz h4 {
    font-size: var(--font-normal);
    display: -webkit-box;
    -webkit-line-clamp: 2; /* 最多显示 3 行 */
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .home_hot-spot__news__item__info__GSDkz div {
    display: none;
  }
  .home_hot-spot__news__item__image__0Dj0A {
    width: 100%;
    height: 132px;
  }
  .home_hot-spot__news__item__image--right__scey9 {
    width: 60%;
  }
  .home_hot-spot__news__item--left__W7YL9 {
    height: auto;
    display: flex;
    flex-direction: column;
  }
  .home_hot-spot__news__item--left__W7YL9 .home_hot-spot__news__item__info__GSDkz {
    height: auto;
    padding: 20px;
    display: flex;
    gap: 6px;
    justify-content: flex-start;
  }
  .home_hot-spot__news__item--left__W7YL9 .home_hot-spot__news__item__info__GSDkz h4 {
    font-size: 20px;
  }
  .home_hot-spot__news__item--left__W7YL9 .home_hot-spot__news__item__info__GSDkz div {
    display: block;
  }
  .home_nav__gr65i {
    height: 54px;
  }
  .home_nav__placeholder__R_bDj {
    height: 54px;
  }
  .home_nav__content__gXoig {
    justify-content: space-between;
    padding: 2px 15px;
    height: 54px;
  }
  .home_nav__list__item__Ti9E4 {
    height: 54px;
    line-height: 54px;
  }
  .home_nav__drop__RNd3y {
    top: 54px;
  }
  .home_contacts__TRH4N {
    margin-top: 20px;
    flex-direction: column;
    justify-content: flex-start;
    gap: 20px;
    padding: 16px;
  }
  .home_contacts__info__pIGy0, .home_contacts__address___ZQdr {
    width: 100%;
  }
  .home_contacts__address___ZQdr {
    height: 200px;
  }
  .home_banner-slider__UBj9I {
    height: 44vw;
  }
  .home_banner-slider__indicator__0OOU4 {
    bottom: 10px;
  }
  .home_banner-slider__indicator__item__f8vBh {
    width: 4px;
    height: 4px;
  }
  .home_banner-slider__indicator__item--active__Pkcak {
    background-color: transparent;
    border: 1px solid #fff;
  }
  .home_banner-slider__switcher__SoaxS {
    display: none;
  }
}
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[11].oneOf[9].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[11].oneOf[9].use[3]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[11].oneOf[9].use[4]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[11].oneOf[9].use[5]!./app/[locale]/ui/about/about.module.scss ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
@charset "UTF-8";
.about_about__wvePq {
  position: relative;
}
.about_about__image__KxBNO {
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 1920px;
  height: 900px;
}
.about_about__content__LS1_A {
  width: var(--width-content);
  margin: auto;
  position: relative;
  z-index: 1;
  display: flex;
  flex-direction: column;
  gap: 30px;
}
.about_about__content__LS1_A > div {
  border-radius: 24px;
  background-color: rgba(255, 255, 255, 0.9019607843);
  padding: 30px 20px;
}
.about_about__content__LS1_A > div h1 {
  text-align: center;
  margin-bottom: 16px;
}
.about_about__content__main__HF1T_ {
  border: 1px solid rgba(0, 0, 0, 0.04);
}
.about_about__content__main__header__l3IFd {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 6px;
  margin-bottom: 25px;
}
.about_about__content__main__header__l3IFd h2 {
  margin: 0;
  font-weight: 400;
}
.about_about__content__main__header__dash__46adj {
  color: #7aaef2;
}
.about_about__content__main__header__subtitle__Q8v_4 {
  background: linear-gradient(90deg, #0a72e8 0%, #7aaef2 50%, #0a72e8 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  font-weight: 600;
  font-size: 24px;
  white-space: nowrap;
}
.about_about__content__main__layout__jXAIP {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-gap: 20px;
  gap: 20px;
  align-items: start;
}
.about_about__content__main__left__cx2XR {
  display: flex;
  flex-direction: column;
  gap: 20px;
}
.about_about__content__main__panel__FXe3v {
  background: #fff;
  border: 1px solid rgba(0, 0, 0, 0.06);
  border-radius: 10px;
  padding: 20px 36px;
  background: linear-gradient(171deg, rgba(215, 228, 253, 0.8) 6.07%, rgba(255, 255, 255, 0.8) 38.48%);
  box-shadow: 0 2px 4px 0 rgba(137, 137, 137, 0.031372549);
}
.about_about__content__main__panel__title__oNH7i {
  margin: 0 0 18px;
  color: #1e46b0;
  font-weight: 400;
}
.about_about__content__main__panel__title--title2__oNe15 {
  color: #0f8bb5;
}
.about_about__content__main__panel__title--title3__BoQ01 {
  color: #5a489e;
}
.about_about__content__main__panel__list__UwCfz {
  margin: 0;
  padding-left: 0;
  list-style: none;
  display: grid;
  grid-row-gap: 8px;
  row-gap: 8px;
}
.about_about__content__main__panel__list__UwCfz li {
  line-height: 1.6;
  color: #333;
}
.about_about__content__main__panel__list--twoCols__tV3fi {
  grid-template-columns: 1fr 1fr;
  column-gap: 18px;
}
.about_about__content__main__panel__line__LTorV {
  width: 100%;
  height: 1px;
  background-color: rgba(30, 70, 176, 0.1019607843);
  margin-bottom: 18px;
}
.about_about__content__main__panel--span2__Q1lRT {
  grid-column: 1/-1;
  background: linear-gradient(178deg, rgba(231, 227, 248, 0.8) 0.14%, rgba(255, 255, 255, 0.8) 32.79%);
  box-shadow: 0 2px 4px 0 rgba(137, 137, 137, 0.031372549);
}
.about_about__content__main__panel--emphasis__XSsj6 {
  background: linear-gradient(178deg, rgba(211, 238, 245, 0.8) 3.39%, rgba(255, 255, 255, 0.8) 26.64%);
  box-shadow: 0 2px 4px 0 rgba(137, 137, 137, 0.031372549);
}
.about_about__content__main__panel--right__AyFVO {
  grid-row: 1/-1;
}
.about_about__content__main__panel__overview__2uLz_ {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-gap: 16px;
  gap: 16px;
}
.about_about__content__main__panel__overview__item__label__8lgXf {
  color: #666;
  font-size: 14px;
  margin-bottom: 4px;
}
.about_about__content__main__panel__overview__item__value__cFb4n {
  color: #333;
  font-weight: 500;
  font-size: 15px;
}
.about_about__content__main__panel__global__dsiVb {
  display: flex;
  gap: 20px;
}
.about_about__content__main__panel__global__left__CItrW, .about_about__content__main__panel__global__right__gew37 {
  flex: 1 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
}
.about_about__content__main__panel__global__item__ZUSKg {
  color: #333;
  line-height: 1.6;
  font-size: 14px;
}
.about_about__content__main__panel__products__pfc9E {
  display: flex;
  flex-direction: column;
  gap: 24px;
}
.about_about__content__main__panel__products__group__30KKJ h6 {
  margin: 0 0 12px;
  color: #0a72e8;
  font-size: 16px;
  font-weight: 600;
}
.about_about__content__main__panel__products__group__30KKJ ul {
  margin: 0;
  padding-left: 0;
  list-style: none;
  display: flex;
  flex-direction: column;
  gap: 6px;
}
.about_about__content__main__panel__products__group__30KKJ ul li {
  color: #333;
  line-height: 1.6;
  font-size: 14px;
  position: relative;
  padding-left: 16px;
}
.about_about__content__main__panel__products__group__30KKJ ul li:before {
  content: "✓";
  position: absolute;
  left: 0;
  color: #30b566;
  font-weight: 700;
}
.about_about__content__main__view__SDZLN {
  display: none;
}
.about_about__content__main__quotes__gj59l {
  margin-top: 34px;
  display: flex;
  justify-content: space-around;
}
.about_about__content__main__quotes__item__fN1Rr {
  width: 394px;
  height: 123px;
  display: flex;
  padding: 20px 30px;
  align-items: center;
  gap: 20px;
}
.about_about__content__main__quotes__item__fN1Rr > div {
  display: flex;
  flex-direction: column;
  gap: 10px;
  text-align: left;
}
.about_about__content__main__quotes__item__fN1Rr > div h5 {
  margin: 0;
}
.about_about__content__main__quotes__item__fN1Rr > div span {
  color: var(--text-description);
  font-size: var(--font-medium);
}
.about_about__content__career__companytime__HUZ14 {
  margin-top: 30px;
}
.about_about__content__career__timeline__hCaJK {
  margin-top: 30px;
}
.about_about__content__career__timeline__item__r5rr0 {
  display: flex;
  justify-content: center;
  gap: 10px;
}
.about_about__content__career__timeline__item__r5rr0 > div:first-of-type {
  display: flex;
  justify-content: flex-end;
  width: 400px;
}
.about_about__content__career__timeline__item__r5rr0 > div:first-of-type > div {
  text-align: center;
}
.about_about__content__career__timeline__item__r5rr0 > div:first-of-type > div > div {
  border-radius: 6px;
  overflow: hidden;
}
.about_about__content__career__timeline__item__r5rr0 > div:first-of-type > div h6 {
  margin-top: 10px;
}
.about_about__content__career__timeline__item__r5rr0 > div:nth-of-type(2) {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.about_about__content__career__timeline__item__r5rr0 > div:nth-of-type(2) > div:first-of-type {
  flex: 1 1;
  border-right: 1px solid var(--gray-4);
}
.about_about__content__career__timeline__item__r5rr0 > div:nth-of-type(2) > div:nth-of-type(2) {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 16px;
  height: 16px;
}
.about_about__content__career__timeline__item__r5rr0 > div:nth-of-type(2) > div:nth-of-type(2) > div {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: var(--color-theme);
}
.about_about__content__career__timeline__item__r5rr0 > div:nth-of-type(2) > div:last-of-type {
  flex: 1 1;
  border-right: 1px solid var(--gray-4);
}
.about_about__content__career__timeline__item__r5rr0 > div:nth-of-type(3) {
  width: 400px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 10px;
}
.about_about__content__career__timeline__item__r5rr0 > div:nth-of-type(3) h4 {
  margin: 0;
}
.about_about__content__career__timeline__item__r5rr0 > div:nth-of-type(3) p {
  color: var(--text-description);
}
.about_about__content__career__timeline__item--reverse__s8Qhz {
  flex-direction: row-reverse;
}
.about_about__content__career__timeline__item--reverse__s8Qhz > div:first-of-type {
  justify-content: flex-start;
}
.about_about__content__career__timeline__item--reverse__s8Qhz p,
.about_about__content__career__timeline__item--reverse__s8Qhz h4 {
  text-align: right;
}
.about_about__content__career__timeline__item__r5rr0:first-of-type > div:nth-of-type(2) > div:first-of-type {
  opacity: 0;
}
.about_about__content__career__timeline__item__r5rr0:last-of-type > div:nth-of-type(2) > div:last-of-type {
  opacity: 0;
}
.about_about__content__prides__0DhgW {
  margin-top: 30px;
}
.about_about__content__prides__list__LSiiG {
  display: flex;
  column-gap: 15px;
  row-gap: 16px;
  flex-wrap: nowrap;
}
.about_about__content__prides__list__item__BPyny {
  position: relative;
}
.about_about__content__prides__list__item--normal__tpjHv {
  max-width: 231px;
  aspect-ratio: 231/326;
}
.about_about__content__prides__list__item--large__VfdUa {
  max-width: 478px;
  aspect-ratio: 478/326;
}
.about_about__content__prides__list__item--small__fRQpL {
  max-width: 231px;
  aspect-ratio: 231/154;
}
.about_about__content__prides__list__item__cover__GN83j {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  background-color: rgba(0, 117, 235, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: var(--font-large);
  border-radius: 6px;
  padding: 0 16px;
  text-align: center;
}
.about_about__content__prides__list__LSiiG:last-of-type {
  margin-top: 16px;
}
.about_about__content__contacts__S9y1Y {
  background-color: transparent !important;
  padding: 0 !important;
  margin-top: 30px;
}

@media (min-width: 451px) and (max-width: 1280px) {
  .about_about__image__KxBNO {
    width: 100vw;
    height: 50vh;
  }
  .about_about__content__LS1_A {
    padding: 0 20px;
  }
  .about_about__content__LS1_A > div {
    padding: 20px 16px;
  }
  .about_about__content__main__layout__jXAIP {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  .about_about__content__main__panel--right__AyFVO {
    grid-row: auto;
  }
  .about_about__content__main__panel__overview__2uLz_ {
    grid-template-columns: 1fr;
    gap: 12px;
  }
  .about_about__content__main__panel__global__dsiVb {
    flex-direction: column;
    gap: 16px;
  }
}
@media (max-width: 450px) {
  .about_about__content__LS1_A {
    padding: 16px 16px 0;
  }
  .about_about__content__LS1_A > div {
    padding: 20px 16px;
  }
  .about_about__content__main__layout__jXAIP {
    grid-template-columns: 1fr;
    gap: 12px;
  }
  .about_about__content__main__panel--right__AyFVO {
    grid-row: auto;
  }
  .about_about__content__main__panel__overview__2uLz_ {
    grid-template-columns: 1fr;
    gap: 8px;
  }
  .about_about__content__main__panel__global__dsiVb {
    flex-direction: column;
    gap: 12px;
  }
  .about_about__content__main__quotes__gj59l {
    flex-direction: column;
    justify-content: flex-start;
    gap: 20px;
  }
  .about_about__content__main__quotes__item__fN1Rr {
    width: 100%;
    height: auto;
    padding: 0;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .about_about__content__main__quotes__item__fN1Rr h5 {
    text-align: center;
  }
  .about_about__content__career__timeline__hCaJK {
    margin-top: 20px;
  }
  .about_about__content__career__timeline__item__r5rr0 > div:last-of-type > div {
    width: 100%;
    aspect-ratio: 285/190;
  }
  .about_about__content__career__timeline__item__r5rr0 h6 {
    margin: 0;
    text-align: left;
  }
  .about_about__content__career__timeline__item__r5rr0 p {
    margin-bottom: 20px;
  }
  .about_about__content__career__timeline__item--reverse__s8Qhz {
    flex-direction: row;
  }
  .about_about__content__career__timeline__item--reverse__s8Qhz > div:first-of-type {
    justify-content: flex-end;
  }
  .about_about__content__career__timeline__item--reverse__s8Qhz p,
  .about_about__content__career__timeline__item--reverse__s8Qhz h4 {
    text-align: left;
  }
  .about_about__content__career__timeline__item__r5rr0:last-of-type p {
    margin-bottom: 0;
  }
  .about_about__content__prides__0DhgW {
    background-color: transparent !important;
    padding: 0 !important;
    margin-top: 0 !important;
  }
  .about_about__content__contacts__S9y1Y {
    margin-top: 0;
  }
}
