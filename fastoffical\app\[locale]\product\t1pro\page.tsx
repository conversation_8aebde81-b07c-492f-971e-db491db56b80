import './t1pro.scss'
import Image from 'next/image'
import Link from 'next/link'
import { getI18n, getCurrentLocale } from '@/locales/server'
import type { Metadata, ResolvingMetadata } from 'next'
import { PageProps } from '@/data/type'

export async function generateMetadata(
  { params, searchParams }: PageProps,
  parent: ResolvingMetadata
): Promise<Metadata> {
  const locale = params.locale
  return {
    title: locale === 'zh' ? '桌面式双屏AI翻译机 T1Pro' : 'Desktop Dual-Screen AI Translator T1Pro',
    description:
      '赛蓝科技 引领生活 桌面式双屏AI翻译机 T1Pro 看家王带屏摄像机',
    icons: {
      icon: '/favicon.ico',
    },
  }
}

export default async function Page({ params, searchParams }: PageProps) {
  const t = await getI18n()
  const locale = await getCurrentLocale()

  const Cover = ({ src }: { src: string }) => {
    return (
      <div className="c31__banner__cover">
        <Image
          width={1920}
          height={664}
          alt=""
          src={src}
          style={{ height: '100%', width: '100%' }}
          unoptimized
        ></Image>
      </div>
    )
  }

  return (
    <>
      <div className="c31-outter">
        <div className="c31">
          <div className="c31__banner1 c31__banner">
            <Cover src="/t1pro/<EMAIL>" />
            <div className="c31__banner__main">
              <div className="c31__banner1__product">
                {/* <Image
                  src={'/c31/pic-01-ys.png'}
                  width={445}
                  height={664}
                  alt=""
                  unoptimized
                ></Image> */}
                <div style={{
                  width: 445,
                  height: 66
                }}></div>
              </div>
              <div
                className="c31__banner1__description"
                style={
                  locale === 'en' ? { marginLeft: 20 } : { marginLeft: 100 }
                }
              >
                <div className="c31__banner1__icon">
                  <Image
                    src={'/c31/imcam-icon.png'}
                    width={74}
                    height={74}
                    alt=""
                    unoptimized
                  ></Image>
                </div>
                {locale === 'en' ? (
                  <p>Where Language Barriers Disappear!</p>
                ) : (
                  <p>让语言不再成为沟通障碍</p>
                )}
                <span>
                  {locale === 'zh' ? '悦办桌面式双屏翻译机' : 'Im Mate Desktop Dual-Screen Translator'}
                </span>

                <div className="c31__banner1__qrcode">
                  <Link href={'/support/download_client'}>
                    <button>
                      {locale === 'zh' ? '下载APP' : 'Download App'}
                    </button>
                  </Link>
                  <div className="c31__banner1">
                    <Image
                      src={'/support/download-code-imacam.png'}
                      width={156}
                      height={166}
                      alt=""
                    ></Image>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className="c31__banner2 c31__banner">
            <Cover src={'/t1pro/bg2.jpg'} />
            <div className="c31__banner__main">
              <div className="c31__banner__content">
                <h2>
                  {locale === 'zh'
                    ? '开口即译，联网即用'
                    : 'Speak & Translate, Connect & Go'}
                </h2>
                <p>
                  {locale === 'zh'
                    ? '多种语言实时互译'
                    : 'Real-Time Multilingual Translation'}
                </p>

              </div>

            </div>
          </div>

          <div className="c31__banner3 c31__banner">
            <Cover src="/t1pro/bg3.jpg" />
            <div className="c31__banner__main">
              <div className="c31__banner__content">
                <h2>
                  {locale === 'zh'
                    ? '多种语言实时互译'
                    : 'Real-Time Multilingual Translation'}
                </h2>

              </div>

            </div>
          </div>

          <div className="c31__banner4 c31__banner">
            <Cover src="/t1pro/bg4.jpg" />
            <div className="c31__banner__main">
              <div className="c31__banner__content">
                <h2>
                  {locale === 'zh'
                    ? '多种场景使用'
                    : 'Multi-Scenario Applications'}
                </h2>

              </div>

            </div>
          </div>

          <div className="c31__banner5 c31__banner">
            <Cover src="/t1pro/bg5.jpg" />
            <div className="c31__banner__main">
              <div className="c31__banner__content">
                <h2>
                  {locale === 'zh'
                    ? 'AI大模型知识库服务'
                    : 'AI Knowledge Base Powered by LLM'}
                </h2>
                <p>
                  {locale === 'zh'
                    ? '机器人模式，自动翻译并回复用户的问题'
                    : 'Real-Time Multilingual Translation'}
                </p>

              </div>

            </div>
          </div>
        </div>
      </div>
    </>
  )
}
