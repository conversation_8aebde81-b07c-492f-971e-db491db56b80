/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/[locale]/about/page";
exports.ids = ["app/[locale]/about/page"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F%5Blocale%5D%2Fabout%2Fpage&page=%2F%5Blocale%5D%2Fabout%2Fpage&appPaths=%2F%5Blocale%5D%2Fabout%2Fpage&pagePath=private-next-app-dir%2F%5Blocale%5D%2Fabout%2Fpage.tsx&appDir=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F%5Blocale%5D%2Fabout%2Fpage&page=%2F%5Blocale%5D%2Fabout%2Fpage&appPaths=%2F%5Blocale%5D%2Fabout%2Fpage&pagePath=private-next-app-dir%2F%5Blocale%5D%2Fabout%2Fpage.tsx&appDir=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?9d97\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        '[locale]',\n        {\n        children: [\n        'about',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/[locale]/about/page.tsx */ \"(rsc)/./app/[locale]/about/page.tsx\")), \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\about\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/[locale]/layout.tsx */ \"(rsc)/./app/[locale]/layout.tsx\")), \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/[locale]/not-found.tsx */ \"(rsc)/./app/[locale]/not-found.tsx\")), \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\not-found.tsx\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\")), \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/not-found.tsx */ \"(rsc)/./app/not-found.tsx\")), \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\not-found.tsx\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\about\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/[locale]/about/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/[locale]/about/page\",\n        pathname: \"/[locale]/about\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F%5Blocale%5D%2Fabout%2Fpage&page=%2F%5Blocale%5D%2Fabout%2Fpage&appPaths=%2F%5Blocale%5D%2Fabout%2Fpage&pagePath=private-next-app-dir%2F%5Blocale%5D%2Fabout%2Fpage.tsx&appDir=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Capp%5C%5Blocale%5D%5Cpage.module.scss&modules=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Cimage-component.js&modules=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Clink.js&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Capp%5C%5Blocale%5D%5Cpage.module.scss&modules=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Cimage-component.js&modules=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Clink.js&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/image-component.js */ \"(ssr)/./node_modules/next/dist/client/image-component.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/link.js */ \"(ssr)/./node_modules/next/dist/client/link.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9RCUzQSU1Qy0tLUFQcm9qZWN0cy0tLSU1Q2ltY2FtLW9mZmljaWFsc2l0ZSU1Q2Zhc3RvZmZpY2FsJTVDYXBwJTVDJTVCbG9jYWxlJTVEJTVDcGFnZS5tb2R1bGUuc2NzcyZtb2R1bGVzPUQlM0ElNUMtLS1BUHJvamVjdHMtLS0lNUNpbWNhbS1vZmZpY2lhbHNpdGUlNUNmYXN0b2ZmaWNhbCU1Q25vZGVfbW9kdWxlcyU1Q25leHQlNUNkaXN0JTVDY2xpZW50JTVDaW1hZ2UtY29tcG9uZW50LmpzJm1vZHVsZXM9RCUzQSU1Qy0tLUFQcm9qZWN0cy0tLSU1Q2ltY2FtLW9mZmljaWFsc2l0ZSU1Q2Zhc3RvZmZpY2FsJTVDbm9kZV9tb2R1bGVzJTVDbmV4dCU1Q2Rpc3QlNUNjbGllbnQlNUNsaW5rLmpzJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxzTkFBK0k7QUFDL0kiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9pbWNhbV9vZmZpY2lhbF9zaXRlLz84OGJjIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxcLS0tQVByb2plY3RzLS0tXFxcXGltY2FtLW9mZmljaWFsc2l0ZVxcXFxmYXN0b2ZmaWNhbFxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxpbWFnZS1jb21wb25lbnQuanNcIik7XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXC0tLUFQcm9qZWN0cy0tLVxcXFxpbWNhbS1vZmZpY2lhbHNpdGVcXFxcZmFzdG9mZmljYWxcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcbGluay5qc1wiKSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Capp%5C%5Blocale%5D%5Cpage.module.scss&modules=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Cimage-component.js&modules=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Clink.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Capp%5C%5Blocale%5D%5Cui%5Cabout%5Cabout-content.tsx&server=true!":
/*!******************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Capp%5C%5Blocale%5D%5Cui%5Cabout%5Cabout-content.tsx&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/[locale]/ui/about/about-content.tsx */ \"(ssr)/./app/[locale]/ui/about/about-content.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9RCUzQSU1Qy0tLUFQcm9qZWN0cy0tLSU1Q2ltY2FtLW9mZmljaWFsc2l0ZSU1Q2Zhc3RvZmZpY2FsJTVDYXBwJTVDJTVCbG9jYWxlJTVEJTVDdWklNUNhYm91dCU1Q2Fib3V0LWNvbnRlbnQudHN4JnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSIsInNvdXJjZXMiOlsid2VicGFjazovL2ltY2FtX29mZmljaWFsX3NpdGUvPzk5ZTkiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFwtLS1BUHJvamVjdHMtLS1cXFxcaW1jYW0tb2ZmaWNpYWxzaXRlXFxcXGZhc3RvZmZpY2FsXFxcXGFwcFxcXFxbbG9jYWxlXVxcXFx1aVxcXFxhYm91dFxcXFxhYm91dC1jb250ZW50LnRzeFwiKSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Capp%5C%5Blocale%5D%5Cui%5Cabout%5Cabout-content.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Capp%5C%5Blocale%5D%5Cui%5Ccomponents%5Cback-to-top.tsx&modules=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Capp%5C%5Blocale%5D%5Cui%5Chome%5Cfooter.tsx&modules=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Capp%5C%5Blocale%5D%5Cui%5Chome%5Cnav.tsx&modules=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22app%5C%5C%5Blocale%5D%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Capp%5Cglobals.scss&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Capp%5C%5Blocale%5D%5Cui%5Ccomponents%5Cback-to-top.tsx&modules=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Capp%5C%5Blocale%5D%5Cui%5Chome%5Cfooter.tsx&modules=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Capp%5C%5Blocale%5D%5Cui%5Chome%5Cnav.tsx&modules=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22app%5C%5C%5Blocale%5D%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Capp%5Cglobals.scss&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/[locale]/ui/components/back-to-top.tsx */ \"(ssr)/./app/[locale]/ui/components/back-to-top.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/[locale]/ui/home/<USER>/ \"(ssr)/./app/[locale]/ui/home/<USER>"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/[locale]/ui/home/<USER>/ \"(ssr)/./app/[locale]/ui/home/<USER>"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9RCUzQSU1Qy0tLUFQcm9qZWN0cy0tLSU1Q2ltY2FtLW9mZmljaWFsc2l0ZSU1Q2Zhc3RvZmZpY2FsJTVDYXBwJTVDJTVCbG9jYWxlJTVEJTVDdWklNUNjb21wb25lbnRzJTVDYmFjay10by10b3AudHN4Jm1vZHVsZXM9RCUzQSU1Qy0tLUFQcm9qZWN0cy0tLSU1Q2ltY2FtLW9mZmljaWFsc2l0ZSU1Q2Zhc3RvZmZpY2FsJTVDYXBwJTVDJTVCbG9jYWxlJTVEJTVDdWklNUNob21lJTVDZm9vdGVyLnRzeCZtb2R1bGVzPUQlM0ElNUMtLS1BUHJvamVjdHMtLS0lNUNpbWNhbS1vZmZpY2lhbHNpdGUlNUNmYXN0b2ZmaWNhbCU1Q2FwcCU1QyU1QmxvY2FsZSU1RCU1Q3VpJTVDaG9tZSU1Q25hdi50c3gmbW9kdWxlcz1EJTNBJTVDLS0tQVByb2plY3RzLS0tJTVDaW1jYW0tb2ZmaWNpYWxzaXRlJTVDZmFzdG9mZmljYWwlNUNub2RlX21vZHVsZXMlNUNuZXh0JTVDZm9udCU1Q2dvb2dsZSU1Q3RhcmdldC5jc3MlM0YlN0IlMjJwYXRoJTIyJTNBJTIyYXBwJTVDJTVDJTVCbG9jYWxlJTVEJTVDJTVDbGF5b3V0LnRzeCUyMiUyQyUyMmltcG9ydCUyMiUzQSUyMkludGVyJTIyJTJDJTIyYXJndW1lbnRzJTIyJTNBJTVCJTdCJTIyc3Vic2V0cyUyMiUzQSU1QiUyMmxhdGluJTIyJTVEJTdEJTVEJTJDJTIydmFyaWFibGVOYW1lJTIyJTNBJTIyaW50ZXIlMjIlN0QmbW9kdWxlcz1EJTNBJTVDLS0tQVByb2plY3RzLS0tJTVDaW1jYW0tb2ZmaWNpYWxzaXRlJTVDZmFzdG9mZmljYWwlNUNhcHAlNUNnbG9iYWxzLnNjc3Mmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLG9NQUF5STtBQUN6SSw4S0FBOEg7QUFDOUgiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9pbWNhbV9vZmZpY2lhbF9zaXRlLz9jOTMxIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxcLS0tQVByb2plY3RzLS0tXFxcXGltY2FtLW9mZmljaWFsc2l0ZVxcXFxmYXN0b2ZmaWNhbFxcXFxhcHBcXFxcW2xvY2FsZV1cXFxcdWlcXFxcY29tcG9uZW50c1xcXFxiYWNrLXRvLXRvcC50c3hcIik7XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXC0tLUFQcm9qZWN0cy0tLVxcXFxpbWNhbS1vZmZpY2lhbHNpdGVcXFxcZmFzdG9mZmljYWxcXFxcYXBwXFxcXFtsb2NhbGVdXFxcXHVpXFxcXGhvbWVcXFxcZm9vdGVyLnRzeFwiKTtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxcLS0tQVByb2plY3RzLS0tXFxcXGltY2FtLW9mZmljaWFsc2l0ZVxcXFxmYXN0b2ZmaWNhbFxcXFxhcHBcXFxcW2xvY2FsZV1cXFxcdWlcXFxcaG9tZVxcXFxuYXYudHN4XCIpIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Capp%5C%5Blocale%5D%5Cui%5Ccomponents%5Cback-to-top.tsx&modules=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Capp%5C%5Blocale%5D%5Cui%5Chome%5Cfooter.tsx&modules=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Capp%5C%5Blocale%5D%5Cui%5Chome%5Cnav.tsx&modules=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22app%5C%5C%5Blocale%5D%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Capp%5Cglobals.scss&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Capp%5Cnot-found.tsx&server=true!":
/*!**********************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Capp%5Cnot-found.tsx&server=true! ***!
  \**********************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/not-found.tsx */ \"(ssr)/./app/not-found.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9RCUzQSU1Qy0tLUFQcm9qZWN0cy0tLSU1Q2ltY2FtLW9mZmljaWFsc2l0ZSU1Q2Zhc3RvZmZpY2FsJTVDYXBwJTVDbm90LWZvdW5kLnRzeCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9pbWNhbV9vZmZpY2lhbF9zaXRlLz9mZGMwIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxcLS0tQVByb2plY3RzLS0tXFxcXGltY2FtLW9mZmljaWFsc2l0ZVxcXFxmYXN0b2ZmaWNhbFxcXFxhcHBcXFxcbm90LWZvdW5kLnRzeFwiKSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Capp%5Cnot-found.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./app/[locale]/ui/about/about-content.tsx":
/*!*************************************************!*\
  !*** ./app/[locale]/ui/about/about-content.tsx ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AboutContentLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _about_module_scss__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./about.module.scss */ \"(ssr)/./app/[locale]/ui/about/about.module.scss\");\n/* harmony import */ var _about_module_scss__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(_about_module_scss__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _components_page_tabs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../components/page-tabs */ \"(ssr)/./app/[locale]/ui/components/page-tabs.tsx\");\n/* harmony import */ var _home_about__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../home/<USER>/ \"(ssr)/./app/[locale]/ui/home/<USER>");\n/* harmony import */ var _components_certificates__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../components/certificates */ \"(ssr)/./app/[locale]/ui/components/certificates.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _locales_client__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/locales/client */ \"(ssr)/./locales/client.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\n\nfunction AboutContentLayout() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_locales_client__WEBPACK_IMPORTED_MODULE_7__.I18nProviderClient, {\n        locale: (0,_locales_client__WEBPACK_IMPORTED_MODULE_7__.useCurrentLocale)(),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AboutContent, {}, void 0, false, {\n            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n            lineNumber: 15,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n        lineNumber: 14,\n        columnNumber: 5\n    }, this);\n}\nfunction AboutContent() {\n    const t = (0,_locales_client__WEBPACK_IMPORTED_MODULE_7__.useI18n)();\n    const [tabs, setTabs] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)([\n        {\n            id: \"0\",\n            text: t(\"aboutCylan\")\n        },\n        {\n            id: \"1\",\n            text: t(\"cylanPrides\")\n        },\n        {\n            id: \"2\",\n            text: t(\"contactUs\")\n        }\n    ]);\n    const [currentTab, setCurrentTab] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(tabs[0].id);\n    const [trigger, setTrigger] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(currentTab);\n    const contentRef = (0,react__WEBPACK_IMPORTED_MODULE_5__.useRef)(null);\n    const handleTabChange = (tab)=>{\n        setTrigger(tab);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default().about),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `${(_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default().about__image)} hide-on-small`,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                    src: \"/about-banner.webp\",\n                    width: 1920,\n                    height: 900,\n                    alt: \"\",\n                    style: {\n                        width: \"100%\",\n                        height: \"100%\",\n                        objectFit: \"fill\",\n                        objectPosition: \"center\"\n                    },\n                    unoptimized: true\n                }, void 0, false, {\n                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                    lineNumber: 48,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                lineNumber: 47,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h6\", {\n                id: \"about-cylan\"\n            }, void 0, false, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                lineNumber: 62,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"hide-on-small\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_page_tabs__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    title: t(\"aboutUs\"),\n                    iconSrc: \"/about-icon.svg\",\n                    currentTab: currentTab,\n                    tabs: tabs,\n                    showBanner: false,\n                    background: \"transparent\",\n                    onTabChange: (tab)=>{\n                        handleTabChange(tab);\n                    }\n                }, void 0, false, {\n                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                    lineNumber: 64,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                lineNumber: 63,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"hide-on-medium hide-on-large\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_page_tabs__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    title: t(\"aboutUs\"),\n                    iconSrc: \"/about-icon.svg\",\n                    currentTab: currentTab,\n                    tabs: tabs,\n                    bannerMobileSrc: \"/about-banner-mobile.jpg\",\n                    background: \"rgb(214,218,211)\",\n                    onTabChange: (tab)=>{\n                        handleTabChange(tab);\n                    }\n                }, void 0, false, {\n                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                    lineNumber: 77,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                lineNumber: 76,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_5__.Suspense, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Content, {\n                    contentRef: contentRef,\n                    tabs: tabs,\n                    setCurrentTab: setCurrentTab,\n                    trigger: trigger\n                }, void 0, false, {\n                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                    lineNumber: 90,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                lineNumber: 89,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n        lineNumber: 46,\n        columnNumber: 5\n    }, this);\n}\nfunction Content({ tabs, setCurrentTab = ()=>{}, contentRef, trigger }) {\n    const aboutCylanRef = (0,react__WEBPACK_IMPORTED_MODULE_5__.useRef)(null);\n    const pridesRef = (0,react__WEBPACK_IMPORTED_MODULE_5__.useRef)(null);\n    const contactRef = (0,react__WEBPACK_IMPORTED_MODULE_5__.useRef)(null);\n    const isInitial = (0,react__WEBPACK_IMPORTED_MODULE_5__.useRef)(true);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_6__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_6__.usePathname)();\n    const handleTabChange = (0,react__WEBPACK_IMPORTED_MODULE_5__.useCallback)((tab)=>{\n        let jump;\n        if (tab === tabs[0].id) jump = \"about-cylan\";\n        else if (tab === tabs[1].id) jump = \"prides\";\n        else jump = \"contacts\";\n        setCurrentTab(tab);\n        router.replace(`${pathname}/#${jump}`);\n    }, [\n        tabs,\n        setCurrentTab,\n        router,\n        pathname\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_5__.useEffect)(()=>{\n        const handleScroll = ()=>{\n            const scrollHeight = window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop;\n            const top = aboutCylanRef.current?.getBoundingClientRect().top;\n            if (top && scrollHeight < top) setCurrentTab(tabs[0].id);\n        };\n        window.addEventListener(\"scroll\", handleScroll);\n        return ()=>{\n            window.removeEventListener(\"scroll\", handleScroll);\n        };\n    }, [\n        handleTabChange,\n        tabs,\n        setCurrentTab\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_5__.useEffect)(()=>{\n        if (isInitial.current) {\n            isInitial.current = false;\n        } else {\n            handleTabChange(trigger);\n        }\n    }, [\n        trigger,\n        handleTabChange\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_locales_client__WEBPACK_IMPORTED_MODULE_7__.I18nProviderClient, {\n        locale: (0,_locales_client__WEBPACK_IMPORTED_MODULE_7__.useCurrentLocale)(),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: contentRef,\n                className: (_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default().about__content),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        ref: aboutCylanRef,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Main, {}, void 0, false, {\n                            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                            lineNumber: 160,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                        lineNumber: 159,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Career, {}, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                        lineNumber: 163,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        ref: pridesRef,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Prides, {}, void 0, false, {\n                            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                            lineNumber: 165,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                        lineNumber: 164,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h6\", {\n                        id: \"contacts\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                        lineNumber: 167,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        ref: contactRef,\n                        className: `${(_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default().about__content__contacts)} hide-on-small`,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_home_about__WEBPACK_IMPORTED_MODULE_3__.Contacts, {\n                            isAboutPage: true\n                        }, void 0, false, {\n                            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                            lineNumber: 173,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                        lineNumber: 169,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                lineNumber: 158,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `${(_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default().about__content__contacts)} hide-on-medium hide-on-large `,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_home_about__WEBPACK_IMPORTED_MODULE_3__.Contacts, {\n                    isAboutPage: true\n                }, void 0, false, {\n                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                    lineNumber: 179,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                lineNumber: 176,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n        lineNumber: 157,\n        columnNumber: 5\n    }, this);\n}\nfunction Main() {\n    const t = (0,_locales_client__WEBPACK_IMPORTED_MODULE_7__.useI18n)();\n    const overviewList = [\n        t(\"companySetTime\"),\n        t(\"companyMainLocation\"),\n        t(\"companyCoreTech\"),\n        t(\"companyService\")\n    ];\n    const coreProductList = [\n        t(\"companyCoreProductText1\"),\n        t(\"companyCoreProductText2\"),\n        t(\"companyCoreProductText3\"),\n        t(\"companyCoreProductText4\"),\n        t(\"companyCoreProductText5\"),\n        t(\"companyCoreProductText6\"),\n        t(\"companyCoreProductText7\"),\n        t(\"companyCoreProductText8\"),\n        t(\"companyCoreProductText9\"),\n        t(\"companyCoreProductText10\")\n    ];\n    const globalList = [\n        t(\"companyGlobalText1\"),\n        t(\"companyGlobalText2\"),\n        t(\"companyGlobalText3\"),\n        t(\"companyGlobalText4\"),\n        t(\"companyGlobalText5\")\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default().about__content__main),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default().about__content__main__header),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        children: t(\"aboutCylanTitle\")\n                    }, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                        lineNumber: 220,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: (_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default().about__content__main__header__dash)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                        lineNumber: 221,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: (_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default().about__content__main__header__subtitle),\n                        children: \"全球智能看护解决方案领导者\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                        lineNumber: 222,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                lineNumber: 219,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default().about__content__main__grid),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: (_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default().about__content__main__panel),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                className: `${(_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default().about__content__main__panel__title)} ${(_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default()[\"about__content__main__panel__title--title1\"])}`,\n                                children: t(\"companyReview\")\n                            }, void 0, false, {\n                                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                lineNumber: 228,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default().about__content__main__panel__line)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                lineNumber: 230,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                className: (_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default().about__content__main__panel__list),\n                                children: overviewList.map((text, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: text\n                                    }, i, false, {\n                                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                        lineNumber: 234,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                lineNumber: 232,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                        lineNumber: 227,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: `${(_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default().about__content__main__panel)} ${(_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default()[\"about__content__main__panel--emphasis\"])}`,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                className: `${(_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default().about__content__main__panel__title)} ${(_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default()[\"about__content__main__panel__title--title2\"])}`,\n                                children: t(\"companyCoreProduct\")\n                            }, void 0, false, {\n                                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                lineNumber: 240,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default().about__content__main__panel__line)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                lineNumber: 242,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                className: `${(_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default().about__content__main__panel__list)} ${(_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default()[\"about__content__main__panel__list--twoCols\"])}`,\n                                children: coreProductList.map((text, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: text\n                                    }, i, false, {\n                                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                        lineNumber: 246,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                lineNumber: 244,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                        lineNumber: 239,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: `${(_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default().about__content__main__panel)} ${(_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default()[\"about__content__main__panel--span2\"])}`,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                className: `${(_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default().about__content__main__panel__title)} ${(_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default()[\"about__content__main__panel__title--title3\"])}`,\n                                children: t(\"companyGlobal\")\n                            }, void 0, false, {\n                                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                lineNumber: 252,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default().about__content__main__panel__line)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                lineNumber: 254,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                className: (_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default().about__content__main__panel__list),\n                                children: globalList.map((text, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: text\n                                    }, i, false, {\n                                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                        lineNumber: 258,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                lineNumber: 256,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                        lineNumber: 251,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                lineNumber: 226,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n        lineNumber: 217,\n        columnNumber: 5\n    }, this);\n}\nfunction Career() {\n    const t = (0,_locales_client__WEBPACK_IMPORTED_MODULE_7__.useI18n)();\n    let LineType;\n    (function(LineType) {\n        LineType[LineType[\"first\"] = 0] = \"first\";\n        LineType[LineType[\"normal\"] = 1] = \"normal\";\n        LineType[LineType[\"last\"] = 2] = \"last\";\n    })(LineType || (LineType = {}));\n    const items = [\n        {\n            title: \"参加2024年香港春季电子展\",\n            time: \"2024年4月\",\n            text: \"2024年香港春季电子产品展及香港国际资讯科技博览会推出二代触摸屏视频通话智能摄像机参展\",\n            imageSrc: \"/hotspot-image-1.png\"\n        },\n        {\n            title: \"参加2024年香港春季电子展\",\n            time: \"2024年4月\",\n            text: \"2024年香港春季电子产品展及香港国际资讯科技博览会推出二代触摸屏视频通话智能摄像机参展\",\n            imageSrc: \"/hotspot-image-1.png\"\n        },\n        {\n            title: \"参加2024年香港春季电子展\",\n            time: \"2024年4月\",\n            text: \"2024年香港春季电子产品展及香港国际资讯科技博览会推出二代触摸屏视频通话智能摄像机参展\",\n            imageSrc: \"/hotspot-image-1.png\"\n        },\n        {\n            title: \"参加2024年香港春季电子展\",\n            time: \"2024年4月\",\n            text: \"2024年香港春季电子产品展及香港国际资讯科技博览会推出二代触摸屏视频通话智能摄像机参展\",\n            imageSrc: \"/hotspot-image-1.png\"\n        },\n        {\n            title: \"参加2024年香港春季电子展\",\n            time: \"2024年4月\",\n            text: \"2024年香港春季电子产品展及香港国际资讯科技博览会推出二代触摸屏视频通话智能摄像机参展\",\n            imageSrc: \"/hotspot-image-1.png\"\n        }\n    ];\n    const TimeLineItem = ({ linetype = 0, isReverse = false, title, time, text, imageSrc })=>{\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `${(_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default().about__content__career__timeline__item)} ${isReverse ? (_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default()[\"about__content__career__timeline__item--reverse\"]) : \"\"}`,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"hide-on-small\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                        src: imageSrc,\n                                        width: 240,\n                                        height: 160,\n                                        alt: \"\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                        lineNumber: 335,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                    lineNumber: 334,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h6\", {\n                                    children: title\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                    lineNumber: 337,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                            lineNumber: 333,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                        lineNumber: 332,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {}, void 0, false, {\n                                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                lineNumber: 341,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {}, void 0, false, {\n                                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                    lineNumber: 343,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                lineNumber: 342,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {}, void 0, false, {\n                                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                lineNumber: 345,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                        lineNumber: 340,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"hide-on-medium hide-on-large\",\n                                children: time\n                            }, void 0, false, {\n                                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                lineNumber: 348,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"hide-on-medium hide-on-large\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                    src: imageSrc,\n                                    width: 285,\n                                    height: 190,\n                                    alt: \"\",\n                                    style: {\n                                        width: \"100%\",\n                                        height: \"100%\"\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                    lineNumber: 350,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                lineNumber: 349,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"hide-on-small\",\n                                children: time\n                            }, void 0, false, {\n                                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                lineNumber: 358,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h6\", {\n                                className: \"hide-on-medium hide-on-large\",\n                                children: title\n                            }, void 0, false, {\n                                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                lineNumber: 359,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: text\n                            }, void 0, false, {\n                                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                lineNumber: 360,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                        lineNumber: 347,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                lineNumber: 326,\n                columnNumber: 9\n            }, this)\n        }, void 0, false);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default().about__content__career),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                children: t(\"career\")\n            }, void 0, false, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                lineNumber: 369,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                children: t(\"careerDescription\")\n            }, void 0, false, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                lineNumber: 370,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default().about__content__career__companytime),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_home_about__WEBPACK_IMPORTED_MODULE_3__.CompanyTime, {\n                    isAboutPage: true\n                }, void 0, false, {\n                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                    lineNumber: 372,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                lineNumber: 371,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h6\", {\n                id: \"prides\"\n            }, void 0, false, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                lineNumber: 374,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n        lineNumber: 368,\n        columnNumber: 5\n    }, this);\n}\nfunction Prides() {\n    const t = (0,_locales_client__WEBPACK_IMPORTED_MODULE_7__.useI18n)();\n    let ImageSize;\n    (function(ImageSize) {\n        ImageSize[ImageSize[\"small\"] = 0] = \"small\";\n        ImageSize[ImageSize[\"normal\"] = 1] = \"normal\";\n        ImageSize[ImageSize[\"large\"] = 2] = \"large\";\n    })(ImageSize || (ImageSize = {}));\n    const [showCoverList, setShowCoverList] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)([\n        {\n            text: t(\"pride1\"),\n            show: false\n        },\n        {\n            text: t(\"pride2\"),\n            show: false\n        },\n        {\n            text: t(\"pride3\"),\n            show: false\n        },\n        {\n            text: t(\"pride4\"),\n            show: false\n        },\n        {\n            text: t(\"pride5\"),\n            show: false\n        },\n        {\n            text: t(\"pride6\"),\n            show: false\n        },\n        {\n            text: t(\"pride7\"),\n            show: false\n        },\n        {\n            text: t(\"pride8\"),\n            show: false\n        },\n        {\n            text: t(\"pride9\"),\n            show: false\n        }\n    ]);\n    const ImageItem = ({ src, size = 1, index })=>{\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            onClick: ()=>{\n                setShowCoverList(showCoverList.map((item, idx)=>{\n                    if (idx === index) item.show = !item.show;\n                    return item;\n                }));\n            },\n            className: `${(_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default().about__content__prides__list__item)} ${size === 2 ? (_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default()[\"about__content__prides__list__item--large\"]) : size === 1 ? (_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default()[\"about__content__prides__list__item--normal\"]) : (_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default()[\"about__content__prides__list__item--small\"])}`,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                    src: src,\n                    width: size === 2 ? 478 : 231,\n                    height: size === 0 ? 154 : 326,\n                    alt: \"\",\n                    style: {\n                        height: \"100%\",\n                        width: \"100%\",\n                        objectFit: \"fill\"\n                    },\n                    unoptimized: true\n                }, void 0, false, {\n                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                    lineNumber: 469,\n                    columnNumber: 9\n                }, this),\n                showCoverList[index].show && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default().about__content__prides__list__item__cover),\n                    children: showCoverList[index].text\n                }, void 0, false, {\n                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                    lineNumber: 478,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n            lineNumber: 453,\n            columnNumber: 7\n        }, this);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: `${(_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default().about__content__prides)}`,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    children: t(\"cylanPrides\")\n                }, void 0, false, {\n                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                    lineNumber: 489,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"hide-on-small\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default().about__content__prides__list),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ImageItem, {\n                                    src: \"/pride-image-1.jpg\",\n                                    index: 0\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                    lineNumber: 492,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ImageItem, {\n                                    src: \"/pride-image-2.jpg\",\n                                    size: 2,\n                                    index: 1\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                    lineNumber: 493,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ImageItem, {\n                                    src: \"/pride-image-3.jpg\",\n                                    index: 2\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                    lineNumber: 498,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ImageItem, {\n                                    src: \"/pride-image-4.jpg\",\n                                    index: 3\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                    lineNumber: 499,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                            lineNumber: 491,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default().about__content__prides__list),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ImageItem, {\n                                    src: \"/pride-image-5.jpg\",\n                                    size: 0,\n                                    index: 4\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                    lineNumber: 502,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ImageItem, {\n                                    src: \"/pride-image-6.jpg\",\n                                    size: 0,\n                                    index: 5\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                    lineNumber: 507,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ImageItem, {\n                                    src: \"/pride-image-7.jpg\",\n                                    size: 0,\n                                    index: 6\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                    lineNumber: 512,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ImageItem, {\n                                    src: \"/pride-image-8.jpg\",\n                                    size: 0,\n                                    index: 7\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                    lineNumber: 517,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ImageItem, {\n                                    src: \"/pride-image-9.jpg\",\n                                    size: 0,\n                                    index: 8\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                    lineNumber: 522,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                            lineNumber: 501,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                    lineNumber: 490,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_certificates__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                    lineNumber: 530,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n            lineNumber: 488,\n            columnNumber: 7\n        }, this)\n    }, void 0, false);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/[locale]/ui/about/about-content.tsx\n");

/***/ }),

/***/ "(ssr)/./app/[locale]/ui/components/back-to-top.tsx":
/*!****************************************************!*\
  !*** ./app/[locale]/ui/components/back-to-top.tsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ BackToTopLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_scss__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./components.scss */ \"(ssr)/./app/[locale]/ui/components/components.scss\");\n/* harmony import */ var _locales_client__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/locales/client */ \"(ssr)/./locales/client.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction BackToTopLayout() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_locales_client__WEBPACK_IMPORTED_MODULE_4__.I18nProviderClient, {\n        locale: (0,_locales_client__WEBPACK_IMPORTED_MODULE_4__.useCurrentLocale)(),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(BackToTop, {}, void 0, false, {\n            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\back-to-top.tsx\",\n            lineNumber: 10,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\back-to-top.tsx\",\n        lineNumber: 9,\n        columnNumber: 5\n    }, this);\n}\nfunction BackToTop() {\n    const t = (0,_locales_client__WEBPACK_IMPORTED_MODULE_4__.useI18n)();\n    const locale = (0,_locales_client__WEBPACK_IMPORTED_MODULE_4__.useCurrentLocale)();\n    const [showBackTop, setShowBackTop] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    let scrollTimer = null;\n    let isScrolling = false;\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        const handleScroll = ()=>{\n            const scrollHeight = window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop;\n            setShowBackTop(scrollHeight > window.innerHeight);\n        };\n        window.addEventListener(\"scroll\", handleScroll);\n        return ()=>{\n            window.removeEventListener(\"scroll\", handleScroll);\n        };\n    }, []);\n    const scrollToTop = ()=>{\n        if (isScrolling) return; // 如果正在滚动,则不执行操作\n        isScrolling = true // 设置滚动标记为 true\n        ;\n        if (scrollTimer) {\n            clearTimeout(scrollTimer);\n        }\n        scrollTimer = setTimeout(()=>{\n            window.scrollTo({\n                top: 0,\n                behavior: \"smooth\"\n            });\n            isScrolling = false // 滚动结束,将标记设置为 false\n            ;\n        }, 100);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        className: `back-top ${showBackTop ? \"\" : \"back-top--hide\"}`,\n        onClick: scrollToTop,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                src: \"/back-top.svg\",\n                width: 26,\n                height: 26,\n                alt: \"Top\"\n            }, void 0, false, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\back-to-top.tsx\",\n                lineNumber: 56,\n                columnNumber: 7\n            }, this),\n            locale === \"zh\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"hide-on-small\",\n                children: t(\"backToTop\")\n            }, void 0, false, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\back-to-top.tsx\",\n                lineNumber: 58,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\back-to-top.tsx\",\n        lineNumber: 52,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/[locale]/ui/components/back-to-top.tsx\n");

/***/ }),

/***/ "(ssr)/./app/[locale]/ui/components/banner.tsx":
/*!***********************************************!*\
  !*** ./app/[locale]/ui/components/banner.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Banner)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _components_scss__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./components.scss */ \"(ssr)/./app/[locale]/ui/components/components.scss\");\n\n\n\nfunction Banner({ src = \"/search-banner.webp\", mobileSrc = \"/search-banner.webp\" }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"banner hide-on-small\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                    src: src,\n                    width: 1920,\n                    height: 220,\n                    alt: \"\",\n                    className: \"banner__image\",\n                    unoptimized: true\n                }, void 0, false, {\n                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\banner.tsx\",\n                    lineNumber: 11,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\banner.tsx\",\n                lineNumber: 10,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"banner hide-on-medium hide-on-large\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                    src: mobileSrc,\n                    width: 1920,\n                    height: 220,\n                    alt: \"\",\n                    className: \"banner__image\",\n                    unoptimized: true\n                }, void 0, false, {\n                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\banner.tsx\",\n                    lineNumber: 21,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\banner.tsx\",\n                lineNumber: 20,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9hcHAvW2xvY2FsZV0vdWkvY29tcG9uZW50cy9iYW5uZXIudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUE4QjtBQUNKO0FBRVgsU0FBU0MsT0FBTyxFQUM3QkMsTUFBTSxxQkFBcUIsRUFDM0JDLFlBQVkscUJBQXFCLEVBQ2xDO0lBQ0MscUJBQ0U7OzBCQUNFLDhEQUFDQztnQkFBSUMsV0FBVzswQkFDZCw0RUFBQ0wsa0RBQUtBO29CQUNKRSxLQUFLQTtvQkFDTEksT0FBTztvQkFDUEMsUUFBUTtvQkFDUkMsS0FBSTtvQkFDSkgsV0FBVztvQkFDWEksV0FBVzs7Ozs7Ozs7Ozs7MEJBR2YsOERBQUNMO2dCQUFJQyxXQUFXOzBCQUNkLDRFQUFDTCxrREFBS0E7b0JBQ0pFLEtBQUtDO29CQUNMRyxPQUFPO29CQUNQQyxRQUFRO29CQUNSQyxLQUFJO29CQUNKSCxXQUFXO29CQUNYSSxXQUFXOzs7Ozs7Ozs7Ozs7O0FBS3JCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vaW1jYW1fb2ZmaWNpYWxfc2l0ZS8uL2FwcC9bbG9jYWxlXS91aS9jb21wb25lbnRzL2Jhbm5lci50c3g/MGQyNyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgSW1hZ2UgZnJvbSAnbmV4dC9pbWFnZSdcclxuaW1wb3J0ICcuL2NvbXBvbmVudHMuc2NzcydcclxuXHJcbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIEJhbm5lcih7XHJcbiAgc3JjID0gJy9zZWFyY2gtYmFubmVyLndlYnAnLFxyXG4gIG1vYmlsZVNyYyA9ICcvc2VhcmNoLWJhbm5lci53ZWJwJyxcclxufSkge1xyXG4gIHJldHVybiAoXHJcbiAgICA8PlxyXG4gICAgICA8ZGl2IGNsYXNzTmFtZT17J2Jhbm5lciBoaWRlLW9uLXNtYWxsJ30+XHJcbiAgICAgICAgPEltYWdlXHJcbiAgICAgICAgICBzcmM9e3NyY31cclxuICAgICAgICAgIHdpZHRoPXsxOTIwfVxyXG4gICAgICAgICAgaGVpZ2h0PXsyMjB9XHJcbiAgICAgICAgICBhbHQ9XCJcIlxyXG4gICAgICAgICAgY2xhc3NOYW1lPXsnYmFubmVyX19pbWFnZSd9XHJcbiAgICAgICAgICB1bm9wdGltaXplZFxyXG4gICAgICAgID48L0ltYWdlPlxyXG4gICAgICA8L2Rpdj5cclxuICAgICAgPGRpdiBjbGFzc05hbWU9eydiYW5uZXIgaGlkZS1vbi1tZWRpdW0gaGlkZS1vbi1sYXJnZSd9PlxyXG4gICAgICAgIDxJbWFnZVxyXG4gICAgICAgICAgc3JjPXttb2JpbGVTcmN9XHJcbiAgICAgICAgICB3aWR0aD17MTkyMH1cclxuICAgICAgICAgIGhlaWdodD17MjIwfVxyXG4gICAgICAgICAgYWx0PVwiXCJcclxuICAgICAgICAgIGNsYXNzTmFtZT17J2Jhbm5lcl9faW1hZ2UnfVxyXG4gICAgICAgICAgdW5vcHRpbWl6ZWRcclxuICAgICAgICA+PC9JbWFnZT5cclxuICAgICAgPC9kaXY+XHJcbiAgICA8Lz5cclxuICApXHJcbn1cclxuIl0sIm5hbWVzIjpbIkltYWdlIiwiQmFubmVyIiwic3JjIiwibW9iaWxlU3JjIiwiZGl2IiwiY2xhc3NOYW1lIiwid2lkdGgiLCJoZWlnaHQiLCJhbHQiLCJ1bm9wdGltaXplZCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./app/[locale]/ui/components/banner.tsx\n");

/***/ }),

/***/ "(ssr)/./app/[locale]/ui/components/certificates.tsx":
/*!*****************************************************!*\
  !*** ./app/[locale]/ui/components/certificates.tsx ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CylanCertificatesLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _locales_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/locales/client */ \"(ssr)/./locales/client.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction CylanCertificatesLayout() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_locales_client__WEBPACK_IMPORTED_MODULE_2__.I18nProviderClient, {\n        locale: (0,_locales_client__WEBPACK_IMPORTED_MODULE_2__.useCurrentLocale)(),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CylanCertificates, {}, void 0, false, {\n            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\certificates.tsx\",\n            lineNumber: 8,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\certificates.tsx\",\n        lineNumber: 7,\n        columnNumber: 5\n    }, this);\n}\nfunction CylanCertificates() {\n    const t = (0,_locales_client__WEBPACK_IMPORTED_MODULE_2__.useI18n)();\n    const certificates = [\n        {\n            imageSrc: \"/pride-image-1.jpg\",\n            title: t(\"pride1\"),\n            imageWidth: 120\n        },\n        {\n            imageSrc: \"/pride-image-2.jpg\",\n            title: t(\"pride2\"),\n            imageWidth: 242\n        },\n        {\n            imageSrc: \"/pride-image-3.jpg\",\n            title: t(\"pride3\"),\n            imageWidth: 120\n        },\n        {\n            imageSrc: \"/pride-image-4.jpg\",\n            title: t(\"pride4\"),\n            imageWidth: 120\n        },\n        {\n            imageSrc: \"/pride-image-5.jpg\",\n            title: t(\"pride5\"),\n            imageWidth: 240\n        },\n        {\n            imageSrc: \"/pride-image-6.jpg\",\n            title: t(\"pride6\"),\n            imageWidth: 240\n        },\n        {\n            imageSrc: \"/pride-image-7.jpg\",\n            title: t(\"pride7\"),\n            imageWidth: 240\n        },\n        {\n            imageSrc: \"/pride-image-8.jpg\",\n            title: t(\"pride8\"),\n            imageWidth: 240\n        },\n        {\n            imageSrc: \"/pride-image-9.jpg\",\n            title: t(\"pride9\"),\n            imageWidth: 240\n        }\n    ];\n    const CertificateItem = ({ imageSrc, title, imageWidth })=>{\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                width: imageWidth\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                        src: imageSrc,\n                        height: 160,\n                        width: imageWidth,\n                        alt: \"\",\n                        unoptimized: true\n                    }, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\certificates.tsx\",\n                        lineNumber: 76,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\certificates.tsx\",\n                    lineNumber: 75,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: title\n                }, void 0, false, {\n                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\certificates.tsx\",\n                    lineNumber: 84,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\certificates.tsx\",\n            lineNumber: 74,\n            columnNumber: 7\n        }, this);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `cylan-certificates hide-on-medium hide-on-large`,\n        children: certificates.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CertificateItem, {\n                ...item\n            }, index, false, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\certificates.tsx\",\n                lineNumber: 92,\n                columnNumber: 9\n            }, this))\n    }, void 0, false, {\n        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\certificates.tsx\",\n        lineNumber: 90,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9hcHAvW2xvY2FsZV0vdWkvY29tcG9uZW50cy9jZXJ0aWZpY2F0ZXMudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUM4QjtBQUNrRDtBQUVqRSxTQUFTSTtJQUN0QixxQkFDRSw4REFBQ0QsK0RBQWtCQTtRQUFDRSxRQUFRSCxpRUFBZ0JBO2tCQUMxQyw0RUFBQ0k7Ozs7Ozs7Ozs7QUFHUDtBQUVBLFNBQVNBO0lBQ1AsTUFBTUMsSUFBSU4sd0RBQU9BO0lBRWpCLE1BQU1PLGVBQWU7UUFDbkI7WUFDRUMsVUFBVTtZQUNWQyxPQUFPSCxFQUFFO1lBQ1RJLFlBQVk7UUFDZDtRQUNBO1lBQ0VGLFVBQVU7WUFDVkMsT0FBT0gsRUFBRTtZQUNUSSxZQUFZO1FBQ2Q7UUFDQTtZQUNFRixVQUFVO1lBQ1ZDLE9BQU9ILEVBQUU7WUFDVEksWUFBWTtRQUNkO1FBQ0E7WUFDRUYsVUFBVTtZQUNWQyxPQUFPSCxFQUFFO1lBQ1RJLFlBQVk7UUFDZDtRQUNBO1lBQ0VGLFVBQVU7WUFDVkMsT0FBT0gsRUFBRTtZQUNUSSxZQUFZO1FBQ2Q7UUFDQTtZQUNFRixVQUFVO1lBQ1ZDLE9BQU9ILEVBQUU7WUFDVEksWUFBWTtRQUNkO1FBQ0E7WUFDRUYsVUFBVTtZQUNWQyxPQUFPSCxFQUFFO1lBQ1RJLFlBQVk7UUFDZDtRQUNBO1lBQ0VGLFVBQVU7WUFDVkMsT0FBT0gsRUFBRTtZQUNUSSxZQUFZO1FBQ2Q7UUFDQTtZQUNFRixVQUFVO1lBQ1ZDLE9BQU9ILEVBQUU7WUFDVEksWUFBWTtRQUNkO0tBQ0Q7SUFFRCxNQUFNQyxrQkFBa0IsQ0FBQyxFQUN2QkgsUUFBUSxFQUNSQyxLQUFLLEVBQ0xDLFVBQVUsRUFLWDtRQUNDLHFCQUNFLDhEQUFDRTtZQUFJQyxPQUFPO2dCQUFFQyxPQUFPSjtZQUFXOzs4QkFDOUIsOERBQUNFOzhCQUNDLDRFQUFDYixrREFBS0E7d0JBQ0pnQixLQUFLUDt3QkFDTFEsUUFBUTt3QkFDUkYsT0FBT0o7d0JBQ1BPLEtBQUk7d0JBQ0pDLFdBQVc7Ozs7Ozs7Ozs7OzhCQUdmLDhEQUFDTjs4QkFBS0g7Ozs7Ozs7Ozs7OztJQUdaO0lBRUEscUJBQ0UsOERBQUNHO1FBQUlPLFdBQVcsQ0FBQywrQ0FBK0MsQ0FBQztrQkFDOURaLGFBQWFhLEdBQUcsQ0FBQyxDQUFDQyxNQUFNQyxzQkFDdkIsOERBQUNYO2dCQUE2QixHQUFHVSxJQUFJO2VBQWZDOzs7Ozs7Ozs7O0FBSTlCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vaW1jYW1fb2ZmaWNpYWxfc2l0ZS8uL2FwcC9bbG9jYWxlXS91aS9jb21wb25lbnRzL2NlcnRpZmljYXRlcy50c3g/NjU4NSJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCdcclxuaW1wb3J0IEltYWdlIGZyb20gJ25leHQvaW1hZ2UnXHJcbmltcG9ydCB7IHVzZUkxOG4sIHVzZUN1cnJlbnRMb2NhbGUsIEkxOG5Qcm92aWRlckNsaWVudCB9IGZyb20gJ0AvbG9jYWxlcy9jbGllbnQnXHJcblxyXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBDeWxhbkNlcnRpZmljYXRlc0xheW91dCgpIHtcclxuICByZXR1cm4gKFxyXG4gICAgPEkxOG5Qcm92aWRlckNsaWVudCBsb2NhbGU9e3VzZUN1cnJlbnRMb2NhbGUoKX0+XHJcbiAgICAgIDxDeWxhbkNlcnRpZmljYXRlcyAvPlxyXG4gICAgPC9JMThuUHJvdmlkZXJDbGllbnQ+XHJcbiAgKVxyXG59XHJcblxyXG5mdW5jdGlvbiBDeWxhbkNlcnRpZmljYXRlcygpIHtcclxuICBjb25zdCB0ID0gdXNlSTE4bigpXHJcblxyXG4gIGNvbnN0IGNlcnRpZmljYXRlcyA9IFtcclxuICAgIHtcclxuICAgICAgaW1hZ2VTcmM6ICcvcHJpZGUtaW1hZ2UtMS5qcGcnLFxyXG4gICAgICB0aXRsZTogdCgncHJpZGUxJyksXHJcbiAgICAgIGltYWdlV2lkdGg6IDEyMCxcclxuICAgIH0sXHJcbiAgICB7XHJcbiAgICAgIGltYWdlU3JjOiAnL3ByaWRlLWltYWdlLTIuanBnJyxcclxuICAgICAgdGl0bGU6IHQoJ3ByaWRlMicpLFxyXG4gICAgICBpbWFnZVdpZHRoOiAyNDIsXHJcbiAgICB9LFxyXG4gICAge1xyXG4gICAgICBpbWFnZVNyYzogJy9wcmlkZS1pbWFnZS0zLmpwZycsXHJcbiAgICAgIHRpdGxlOiB0KCdwcmlkZTMnKSxcclxuICAgICAgaW1hZ2VXaWR0aDogMTIwLFxyXG4gICAgfSxcclxuICAgIHtcclxuICAgICAgaW1hZ2VTcmM6ICcvcHJpZGUtaW1hZ2UtNC5qcGcnLFxyXG4gICAgICB0aXRsZTogdCgncHJpZGU0JyksXHJcbiAgICAgIGltYWdlV2lkdGg6IDEyMCxcclxuICAgIH0sXHJcbiAgICB7XHJcbiAgICAgIGltYWdlU3JjOiAnL3ByaWRlLWltYWdlLTUuanBnJyxcclxuICAgICAgdGl0bGU6IHQoJ3ByaWRlNScpLFxyXG4gICAgICBpbWFnZVdpZHRoOiAyNDAsXHJcbiAgICB9LFxyXG4gICAge1xyXG4gICAgICBpbWFnZVNyYzogJy9wcmlkZS1pbWFnZS02LmpwZycsXHJcbiAgICAgIHRpdGxlOiB0KCdwcmlkZTYnKSxcclxuICAgICAgaW1hZ2VXaWR0aDogMjQwLFxyXG4gICAgfSxcclxuICAgIHtcclxuICAgICAgaW1hZ2VTcmM6ICcvcHJpZGUtaW1hZ2UtNy5qcGcnLFxyXG4gICAgICB0aXRsZTogdCgncHJpZGU3JyksXHJcbiAgICAgIGltYWdlV2lkdGg6IDI0MCxcclxuICAgIH0sXHJcbiAgICB7XHJcbiAgICAgIGltYWdlU3JjOiAnL3ByaWRlLWltYWdlLTguanBnJyxcclxuICAgICAgdGl0bGU6IHQoJ3ByaWRlOCcpLFxyXG4gICAgICBpbWFnZVdpZHRoOiAyNDAsXHJcbiAgICB9LFxyXG4gICAge1xyXG4gICAgICBpbWFnZVNyYzogJy9wcmlkZS1pbWFnZS05LmpwZycsXHJcbiAgICAgIHRpdGxlOiB0KCdwcmlkZTknKSxcclxuICAgICAgaW1hZ2VXaWR0aDogMjQwLFxyXG4gICAgfSxcclxuICBdXHJcblxyXG4gIGNvbnN0IENlcnRpZmljYXRlSXRlbSA9ICh7XHJcbiAgICBpbWFnZVNyYyxcclxuICAgIHRpdGxlLFxyXG4gICAgaW1hZ2VXaWR0aCxcclxuICB9OiB7XHJcbiAgICBpbWFnZVNyYzogc3RyaW5nXHJcbiAgICB0aXRsZTogc3RyaW5nXHJcbiAgICBpbWFnZVdpZHRoOiBudW1iZXJcclxuICB9KSA9PiB7XHJcbiAgICByZXR1cm4gKFxyXG4gICAgICA8ZGl2IHN0eWxlPXt7IHdpZHRoOiBpbWFnZVdpZHRoIH19PlxyXG4gICAgICAgIDxkaXY+XHJcbiAgICAgICAgICA8SW1hZ2VcclxuICAgICAgICAgICAgc3JjPXtpbWFnZVNyY31cclxuICAgICAgICAgICAgaGVpZ2h0PXsxNjB9XHJcbiAgICAgICAgICAgIHdpZHRoPXtpbWFnZVdpZHRofVxyXG4gICAgICAgICAgICBhbHQ9XCJcIlxyXG4gICAgICAgICAgICB1bm9wdGltaXplZFxyXG4gICAgICAgICAgPjwvSW1hZ2U+XHJcbiAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgPGRpdj57dGl0bGV9PC9kaXY+XHJcbiAgICAgIDwvZGl2PlxyXG4gICAgKVxyXG4gIH1cclxuXHJcbiAgcmV0dXJuIChcclxuICAgIDxkaXYgY2xhc3NOYW1lPXtgY3lsYW4tY2VydGlmaWNhdGVzIGhpZGUtb24tbWVkaXVtIGhpZGUtb24tbGFyZ2VgfT5cclxuICAgICAge2NlcnRpZmljYXRlcy5tYXAoKGl0ZW0sIGluZGV4KSA9PiAoXHJcbiAgICAgICAgPENlcnRpZmljYXRlSXRlbSBrZXk9e2luZGV4fSB7Li4uaXRlbX0gLz5cclxuICAgICAgKSl9XHJcbiAgICA8L2Rpdj5cclxuICApXHJcbn1cclxuIl0sIm5hbWVzIjpbIkltYWdlIiwidXNlSTE4biIsInVzZUN1cnJlbnRMb2NhbGUiLCJJMThuUHJvdmlkZXJDbGllbnQiLCJDeWxhbkNlcnRpZmljYXRlc0xheW91dCIsImxvY2FsZSIsIkN5bGFuQ2VydGlmaWNhdGVzIiwidCIsImNlcnRpZmljYXRlcyIsImltYWdlU3JjIiwidGl0bGUiLCJpbWFnZVdpZHRoIiwiQ2VydGlmaWNhdGVJdGVtIiwiZGl2Iiwic3R5bGUiLCJ3aWR0aCIsInNyYyIsImhlaWdodCIsImFsdCIsInVub3B0aW1pemVkIiwiY2xhc3NOYW1lIiwibWFwIiwiaXRlbSIsImluZGV4Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./app/[locale]/ui/components/certificates.tsx\n");

/***/ }),

/***/ "(ssr)/./app/[locale]/ui/components/dropdown-window.tsx":
/*!********************************************************!*\
  !*** ./app/[locale]/ui/components/dropdown-window.tsx ***!
  \********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DropdownWindow)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n\n\n\nfunction DropdownWindow({ children, list, show = false, onClick = ()=>{}, onClickMask = ()=>{} }) {\n    const handleClick = (id)=>{\n        onClick(id);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            position: \"relative\"\n        },\n        children: [\n            children,\n            show && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"dropdown-window\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"dropdown-window__above\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                    src: \"/dropdown-window-above.svg\",\n                                    width: 15,\n                                    height: 8,\n                                    alt: \"drop\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\dropdown-window.tsx\",\n                                    lineNumber: 34,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\dropdown-window.tsx\",\n                                lineNumber: 33,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"dropdown-window__list\",\n                                children: list.map((item, index)=>{\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: item.link ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            href: item.link,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"dropdown-window__list__item\",\n                                                onClick: ()=>{\n                                                    handleClick(item.link);\n                                                },\n                                                children: item.text\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\dropdown-window.tsx\",\n                                                lineNumber: 47,\n                                                columnNumber: 25\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\dropdown-window.tsx\",\n                                            lineNumber: 46,\n                                            columnNumber: 23\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"dropdown-window__list__item\",\n                                            onClick: ()=>{\n                                                handleClick(item?.id);\n                                            },\n                                            children: item.text\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\dropdown-window.tsx\",\n                                            lineNumber: 57,\n                                            columnNumber: 23\n                                        }, this)\n                                    }, index, false, {\n                                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\dropdown-window.tsx\",\n                                        lineNumber: 44,\n                                        columnNumber: 19\n                                    }, this);\n                                })\n                            }, void 0, false, {\n                                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\dropdown-window.tsx\",\n                                lineNumber: 41,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"dropdown-window__placeholder\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\dropdown-window.tsx\",\n                                lineNumber: 70,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\dropdown-window.tsx\",\n                        lineNumber: 32,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"dropdown-window__mask hide-on-medium hide-on-large\",\n                        onClick: ()=>{\n                            onClickMask();\n                        }\n                    }, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\dropdown-window.tsx\",\n                        lineNumber: 72,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\dropdown-window.tsx\",\n        lineNumber: 28,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/[locale]/ui/components/dropdown-window.tsx\n");

/***/ }),

/***/ "(ssr)/./app/[locale]/ui/components/flex-4items-box.tsx":
/*!********************************************************!*\
  !*** ./app/[locale]/ui/components/flex-4items-box.tsx ***!
  \********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Flex4ItemsBox),\n/* harmony export */   itemsMode: () => (/* binding */ itemsMode)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_scss__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./components.scss */ \"(ssr)/./app/[locale]/ui/components/components.scss\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n\n\n\n\n\nvar itemsMode;\n(function(itemsMode) {\n    itemsMode[\"normal\"] = \"\";\n    itemsMode[\"product\"] = \"product\";\n    itemsMode[\"pride\"] = \"pride\";\n})(itemsMode || (itemsMode = {}));\nfunction Flex4ItemsBox({ infos, imageSize, imageBox, mode = \"\", gap = 0, isDetail = false }) {\n    const renderCard = ({ imageSrc, title, tip = \"\", link = \"\", videoSrc = \"\" })=>{\n        const isLinkCard = !!link;\n        const isVideoCard = !!videoSrc;\n        const cardClassName = `flex-box-with-4items__card ${mode === \"product\" ? \"flex-box-with-4items__card--product\" : \"\"} ${isLinkCard ? \"flex-box-with-4items__card--link\" : \"\"} ${isVideoCard ? \"flex-box-with-4items__card--video\" : \"\"}`;\n        const infoClassName = `${mode === \"\" ? \"\" : `flex-box-with-4items__card__info--${mode}`} flex-box-with-4items__card__info`;\n        const cardContent = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-box-with-4items__card__image\",\n                    style: {\n                        aspectRatio: imageBox.width / imageBox.height\n                    },\n                    children: isVideoCard ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"video\", {\n                        width: imageSize.width,\n                        height: imageSize.height,\n                        controls: true,\n                        poster: imageSrc,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"source\", {\n                                src: videoSrc,\n                                type: \"video/mp4\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\flex-4items-box.tsx\",\n                                lineNumber: 76,\n                                columnNumber: 15\n                            }, this),\n                            \"Your browser does not support the video tag.\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\flex-4items-box.tsx\",\n                        lineNumber: 70,\n                        columnNumber: 13\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                        unoptimized: true,\n                        src: imageSrc,\n                        width: imageSize.width,\n                        height: imageSize.height,\n                        alt: \"image\",\n                        style: {\n                            objectFit: \"fill\",\n                            width: \"100%\",\n                            height: \"auto\"\n                        }\n                    }, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\flex-4items-box.tsx\",\n                        lineNumber: 80,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\flex-4items-box.tsx\",\n                    lineNumber: 65,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: infoClassName,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: title\n                        }, void 0, false, {\n                            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\flex-4items-box.tsx\",\n                            lineNumber: 95,\n                            columnNumber: 11\n                        }, this),\n                        mode === \"pride\" ? \"\" : tip ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: tip\n                        }, void 0, false, {\n                            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\flex-4items-box.tsx\",\n                            lineNumber: 96,\n                            columnNumber: 50\n                        }, this) : \"\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\flex-4items-box.tsx\",\n                    lineNumber: 94,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true);\n        return isLinkCard ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n            href: link,\n            className: cardClassName,\n            children: cardContent\n        }, void 0, false, {\n            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\flex-4items-box.tsx\",\n            lineNumber: 102,\n            columnNumber: 7\n        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: cardClassName,\n            children: cardContent\n        }, void 0, false, {\n            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\flex-4items-box.tsx\",\n            lineNumber: 106,\n            columnNumber: 7\n        }, this);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `${`flex-box-with-4items`} ${mode === \"product\" ? \"flex-box-with-4items--product\" : \"\"} ${isDetail ? \"flex-box-with-4items--detail\" : \"\"}`,\n        style: gap ? {\n            gap\n        } : {},\n        children: infos.map((info, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((react__WEBPACK_IMPORTED_MODULE_2___default().Fragment), {\n                children: renderCard(info)\n            }, `${info.link}-${index}`, false, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\flex-4items-box.tsx\",\n                lineNumber: 117,\n                columnNumber: 9\n            }, this))\n    }, void 0, false, {\n        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\flex-4items-box.tsx\",\n        lineNumber: 110,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/[locale]/ui/components/flex-4items-box.tsx\n");

/***/ }),

/***/ "(ssr)/./app/[locale]/ui/components/nav-list.tsx":
/*!*************************************************!*\
  !*** ./app/[locale]/ui/components/nav-list.tsx ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NavListLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _locales_client__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/locales/client */ \"(ssr)/./locales/client.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction NavListLayout({ isFooter = false, onClick = ()=>{} }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_locales_client__WEBPACK_IMPORTED_MODULE_4__.I18nProviderClient, {\n        locale: (0,_locales_client__WEBPACK_IMPORTED_MODULE_4__.useCurrentLocale)(),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NavList, {\n            isFooter: isFooter,\n            onClick: onClick\n        }, void 0, false, {\n            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\nav-list.tsx\",\n            lineNumber: 51,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\nav-list.tsx\",\n        lineNumber: 50,\n        columnNumber: 5\n    }, this);\n}\nfunction NavList({ isFooter = false, onClick }) {\n    const t = (0,_locales_client__WEBPACK_IMPORTED_MODULE_4__.useI18n)();\n    const _items = [\n        {\n            iconSrc: \"/menu-home-icon.svg\",\n            title: t(\"home\"),\n            id: 0,\n            link: \"/\"\n        },\n        {\n            iconSrc: \"/menu-product-icon.svg\",\n            title: t(\"productCenter\"),\n            id: 1,\n            active: false\n        },\n        {\n            subNavs: [\n                {\n                    title: t(\"productCamera\"),\n                    link: \"/product\",\n                    id: 1\n                }\n            ]\n        },\n        // {\n        //   iconSrc: '/menu-videos-icon.svg',\n        //   title: '产品视频',\n        //   link: '/videos',\n        //   id: 2,\n        // },\n        {\n            iconSrc: \"/menu-support-icon.svg\",\n            title: t(\"support\"),\n            link: \"/support\",\n            id: 3\n        },\n        // {\n        //   iconSrc: '/menu-news-icon.svg',\n        //   title: '新闻资讯',\n        //   link: '/news',\n        //   id: 4,\n        // },\n        {\n            iconSrc: \"/menu-about-icon.svg\",\n            title: t(\"aboutCylan\"),\n            link: \"/about\",\n            id: 5\n        }\n    ];\n    const [items, setItems] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(_items);\n    const handleClick = (id, isLink)=>{\n        if (isLink) {\n            onClick && onClick();\n            return;\n        }\n        const newItems = [\n            ...items\n        ];\n        newItems.forEach((item)=>{\n            if (item.id === id) item.active = !item.active;\n        });\n        setItems(newItems);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `nav-list ${isFooter ? \"nav-list--footer\" : \"\"}`,\n        children: items.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NavItem, {\n                navItemPros: item,\n                navItemStatus: items,\n                onClick: handleClick\n            }, index, false, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\nav-list.tsx\",\n                lineNumber: 123,\n                columnNumber: 9\n            }, this))\n    }, void 0, false, {\n        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\nav-list.tsx\",\n        lineNumber: 121,\n        columnNumber: 5\n    }, this);\n}\nfunction NavItem({ navItemPros, navItemStatus, onClick }) {\n    if (navItemPros.subNavs) {\n        const subNavs = navItemPros.subNavs;\n        const id = subNavs[0].id;\n        const mainItem = navItemStatus.find((item)=>item?.id === id);\n        const handleClick = ()=>{\n            const isLink = true;\n            onClick(id, isLink);\n        };\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: mainItem?.active && subNavs.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                    href: item.link,\n                    onClick: handleClick,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"nav-list__item nav-list__item--sub\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"nav-list__item__icon\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\nav-list.tsx\",\n                                lineNumber: 158,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"nav-list__item__title\",\n                                children: item.title\n                            }, void 0, false, {\n                                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\nav-list.tsx\",\n                                lineNumber: 159,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\nav-list.tsx\",\n                        lineNumber: 157,\n                        columnNumber: 15\n                    }, this)\n                }, index, false, {\n                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\nav-list.tsx\",\n                    lineNumber: 156,\n                    columnNumber: 13\n                }, this))\n        }, void 0, false);\n    } else {\n        const { iconSrc, title, link, id, active } = navItemPros;\n        const handleClick = ()=>{\n            const isLink = !!link;\n            onClick(id, isLink);\n        };\n        const Item = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"nav-list__item__icon\",\n                        children: iconSrc && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            src: iconSrc,\n                            width: 24,\n                            height: 24,\n                            alt: \"\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\nav-list.tsx\",\n                            lineNumber: 177,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\nav-list.tsx\",\n                        lineNumber: 175,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"nav-list__item__title\",\n                        children: title\n                    }, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\nav-list.tsx\",\n                        lineNumber: 180,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            flex: 1\n                        }\n                    }, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\nav-list.tsx\",\n                        lineNumber: 181,\n                        columnNumber: 9\n                    }, this),\n                    !link && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"nav-list__item__button\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            src: `/menu-arrow-${active ? \"up\" : \"down\"}.svg`,\n                            width: 32,\n                            height: 32,\n                            alt: \"\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\nav-list.tsx\",\n                            lineNumber: 184,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\nav-list.tsx\",\n                        lineNumber: 183,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true);\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                onClick: handleClick,\n                className: \"nav-list__item\",\n                children: [\n                    !link && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Item, {}, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\nav-list.tsx\",\n                        lineNumber: 198,\n                        columnNumber: 21\n                    }, this),\n                    link && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                        style: {\n                            width: \"100%\",\n                            height: \"100%\"\n                        },\n                        className: \"nav-list__item\",\n                        href: link,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Item, {}, void 0, false, {\n                            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\nav-list.tsx\",\n                            lineNumber: 205,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\nav-list.tsx\",\n                        lineNumber: 200,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\nav-list.tsx\",\n                lineNumber: 197,\n                columnNumber: 9\n            }, this)\n        }, void 0, false);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/[locale]/ui/components/nav-list.tsx\n");

/***/ }),

/***/ "(ssr)/./app/[locale]/ui/components/page-tabs.tsx":
/*!**************************************************!*\
  !*** ./app/[locale]/ui/components/page-tabs.tsx ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ PageTabs)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_scss__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./components.scss */ \"(ssr)/./app/[locale]/ui/components/components.scss\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _banner__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./banner */ \"(ssr)/./app/[locale]/ui/components/banner.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nfunction PageTabs({ title = \"\", iconSrc = \"\", tabs = [], currentTab, bannerSrc = \"/pic_shipinbg@2x (1).webp\", bannerMobileSrc = \"/pic_shipinbg@2x (1).webp\", background = \"rgb(179, 220, 252)\", onTabChange = ()=>{}, showBanner = true, isSearch = false, isLink = false }) {\n    const handleTabChange = (tab)=>{\n        onTabChange(tab);\n    };\n    const Title = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"page-tabs__content\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `page-tabs__content__title`,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        src: iconSrc,\n                        width: 34,\n                        height: 34,\n                        alt: \"\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\page-tabs.tsx\",\n                        lineNumber: 45,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        children: title\n                    }, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\page-tabs.tsx\",\n                        lineNumber: 46,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\page-tabs.tsx\",\n                lineNumber: 44,\n                columnNumber: 7\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\page-tabs.tsx\",\n            lineNumber: 43,\n            columnNumber: 5\n        }, this);\n    const TabItems = ({ isLink = false })=>{\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: `page-tabs__content__items hide-on-small`,\n            children: tabs.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((react__WEBPACK_IMPORTED_MODULE_4___default().Fragment), {\n                    children: isLink ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        href: `${item.id}`,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: `${`page-tabs__content__items__item`} ${currentTab === item.id ? `page-tabs__content__items__item--active` : \"\"}`,\n                            onClick: ()=>handleTabChange(item.id),\n                            children: item.text\n                        }, void 0, false, {\n                            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\page-tabs.tsx\",\n                            lineNumber: 58,\n                            columnNumber: 17\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\page-tabs.tsx\",\n                        lineNumber: 57,\n                        columnNumber: 15\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: `${`page-tabs__content__items__item`} ${currentTab === item.id ? `page-tabs__content__items__item--active` : \"\"}`,\n                        onClick: ()=>handleTabChange(item.id),\n                        children: item.text\n                    }, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\page-tabs.tsx\",\n                        lineNumber: 70,\n                        columnNumber: 15\n                    }, this)\n                }, index, false, {\n                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\page-tabs.tsx\",\n                    lineNumber: 55,\n                    columnNumber: 11\n                }, this))\n        }, void 0, false, {\n            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\page-tabs.tsx\",\n            lineNumber: 53,\n            columnNumber: 7\n        }, this);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"page-tabs\",\n                style: background ? {\n                    background\n                } : {},\n                children: [\n                    showBanner && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_banner__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        src: bannerSrc,\n                        mobileSrc: bannerMobileSrc\n                    }, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\page-tabs.tsx\",\n                        lineNumber: 90,\n                        columnNumber: 24\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {}, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\page-tabs.tsx\",\n                        lineNumber: 91,\n                        columnNumber: 9\n                    }, this),\n                    !isSearch && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TabItems, {\n                        isLink: isLink\n                    }, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\page-tabs.tsx\",\n                        lineNumber: 93,\n                        columnNumber: 23\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\page-tabs.tsx\",\n                lineNumber: 89,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SmallTabs, {\n                onTabChange: onTabChange,\n                tabs: tabs,\n                currentTab: currentTab,\n                isLink: isLink\n            }, void 0, false, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\page-tabs.tsx\",\n                lineNumber: 95,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\nfunction SearchArea() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `page-tabs__search hide-on-small`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                type: \"text\",\n                placeholder: \"请输入关键字，例如：看家王C31、大屏摄像机\"\n            }, void 0, false, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\page-tabs.tsx\",\n                lineNumber: 108,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        src: \"/search-icon-white.svg\",\n                        width: 20,\n                        height: 20,\n                        alt: \"\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\page-tabs.tsx\",\n                        lineNumber: 113,\n                        columnNumber: 9\n                    }, this),\n                    \"搜索\"\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\page-tabs.tsx\",\n                lineNumber: 112,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\page-tabs.tsx\",\n        lineNumber: 107,\n        columnNumber: 5\n    }, this);\n}\nfunction SmallTabs({ tabs, currentTab, onTabChange, isLink }) {\n    const Tab = ({ id, text })=>{\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: `${currentTab === id ? \"page-tabs__tabs-small__tab--active\" : \"\"} page-tabs__tabs-small__tab`,\n            onClick: ()=>{\n                onTabChange(id);\n            },\n            children: text\n        }, void 0, false, {\n            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\page-tabs.tsx\",\n            lineNumber: 138,\n            columnNumber: 7\n        }, this);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"page-tabs__tabs-small hide-on-medium hide-on-large\",\n        children: tabs.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((react__WEBPACK_IMPORTED_MODULE_4___default().Fragment), {\n                children: isLink ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    href: item.id,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Tab, {\n                        ...item\n                    }, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\page-tabs.tsx\",\n                        lineNumber: 157,\n                        columnNumber: 15\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\page-tabs.tsx\",\n                    lineNumber: 156,\n                    columnNumber: 13\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Tab, {\n                    ...item\n                }, void 0, false, {\n                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\page-tabs.tsx\",\n                    lineNumber: 160,\n                    columnNumber: 13\n                }, this)\n            }, index, false, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\page-tabs.tsx\",\n                lineNumber: 154,\n                columnNumber: 9\n            }, this))\n    }, void 0, false, {\n        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\page-tabs.tsx\",\n        lineNumber: 152,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/[locale]/ui/components/page-tabs.tsx\n");

/***/ }),

/***/ "(ssr)/./app/[locale]/ui/home/<USER>":
/*!****************************************!*\
  !*** ./app/[locale]/ui/home/<USER>
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CompanyTime: () => (/* binding */ CompanyTime),\n/* harmony export */   Contacts: () => (/* binding */ Contacts),\n/* harmony export */   \"default\": () => (/* binding */ ABoutLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _home_module_scss__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./home.module.scss */ \"(ssr)/./app/[locale]/ui/home/<USER>");\n/* harmony import */ var _home_module_scss__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(_home_module_scss__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _components_flex_4items_box__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../components/flex-4items-box */ \"(ssr)/./app/[locale]/ui/components/flex-4items-box.tsx\");\n/* harmony import */ var _components_certificates__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../components/certificates */ \"(ssr)/./app/[locale]/ui/components/certificates.tsx\");\n/* harmony import */ var _locales_client__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/locales/client */ \"(ssr)/./locales/client.ts\");\n/* __next_internal_client_entry_do_not_use__ default,CompanyTime,Contacts auto */ \n\n\n\n\n\n\nfunction ABoutLayout() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_locales_client__WEBPACK_IMPORTED_MODULE_5__.I18nProviderClient, {\n        locale: (0,_locales_client__WEBPACK_IMPORTED_MODULE_5__.useCurrentLocale)(),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(About, {}, void 0, false, {\n            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\about.tsx\",\n            lineNumber: 15,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\about.tsx\",\n        lineNumber: 14,\n        columnNumber: 5\n    }, this);\n}\nfunction About() {\n    const t = (0,_locales_client__WEBPACK_IMPORTED_MODULE_5__.useI18n)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (_home_module_scss__WEBPACK_IMPORTED_MODULE_6___default().about),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_home_module_scss__WEBPACK_IMPORTED_MODULE_6___default().about__cover)\n            }, void 0, false, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\about.tsx\",\n                lineNumber: 25,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_home_module_scss__WEBPACK_IMPORTED_MODULE_6___default().about__content),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        children: t(\"aboutUs\")\n                    }, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\about.tsx\",\n                        lineNumber: 27,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CompanyTime, {}, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\about.tsx\",\n                        lineNumber: 28,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Prides, {}, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\about.tsx\",\n                        lineNumber: 29,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Contacts, {}, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\about.tsx\",\n                        lineNumber: 30,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\about.tsx\",\n                lineNumber: 26,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\about.tsx\",\n        lineNumber: 24,\n        columnNumber: 5\n    }, this);\n}\nfunction CompanyTime({ isAboutPage = false }) {\n    const t = (0,_locales_client__WEBPACK_IMPORTED_MODULE_5__.useI18n)();\n    const locale = (0,_locales_client__WEBPACK_IMPORTED_MODULE_5__.useCurrentLocale)();\n    const TimeItem = ({ num, unit, text })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: (_home_module_scss__WEBPACK_IMPORTED_MODULE_6___default().about__content__time__item),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: num\n                        }, void 0, false, {\n                            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\about.tsx\",\n                            lineNumber: 55,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: unit\n                        }, void 0, false, {\n                            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\about.tsx\",\n                            lineNumber: 56,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\about.tsx\",\n                    lineNumber: 54,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: text\n                }, void 0, false, {\n                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\about.tsx\",\n                    lineNumber: 58,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\about.tsx\",\n            lineNumber: 53,\n            columnNumber: 5\n        }, this);\n    const Line = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                height: 46,\n                width: 0,\n                opacity: 0.1,\n                border: `1px solid ${isAboutPage ? \"var(--gray)\" : \"#fff\"}`\n            }\n        }, void 0, false, {\n            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\about.tsx\",\n            lineNumber: 63,\n            columnNumber: 5\n        }, this);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `${(_home_module_scss__WEBPACK_IMPORTED_MODULE_6___default().about__content__time)} ${isAboutPage ? (_home_module_scss__WEBPACK_IMPORTED_MODULE_6___default()[\"about__content__time--page\"]) : \"\"}`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TimeItem, {\n                num: 2005,\n                unit: t(\"year\"),\n                text: t(\"companiyTime\")\n            }, void 0, false, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\about.tsx\",\n                lineNumber: 79,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Line, {}, void 0, false, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\about.tsx\",\n                lineNumber: 80,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TimeItem, {\n                num: locale === \"zh\" ? 45 : 450,\n                unit: locale === \"zh\" ? \"万+\" : \"K+\",\n                text: t(\"deviceSells\")\n            }, void 0, false, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\about.tsx\",\n                lineNumber: 81,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Line, {}, void 0, false, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\about.tsx\",\n                lineNumber: 86,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TimeItem, {\n                num: locale === \"zh\" ? 90 : 900,\n                unit: locale === \"zh\" ? \"万+\" : \"K+\",\n                text: t(\"activeUsers\")\n            }, void 0, false, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\about.tsx\",\n                lineNumber: 87,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\about.tsx\",\n        lineNumber: 74,\n        columnNumber: 5\n    }, this);\n}\nfunction Prides() {\n    const t = (0,_locales_client__WEBPACK_IMPORTED_MODULE_5__.useI18n)();\n    const PridesInfo = {\n        infos: [\n            {\n                imageSrc: \"/pride-image-6.jpg\",\n                title: t(\"pride6\")\n            },\n            {\n                imageSrc: \"/pride-image-9.jpg\",\n                title: t(\"pride9\")\n            },\n            {\n                imageSrc: \"/pride-image-5.jpg\",\n                title: t(\"pride5\")\n            },\n            {\n                imageSrc: \"/pride-image-2.jpg\",\n                title: t(\"pride2\")\n            }\n        ],\n        imageSize: {\n            width: 300,\n            height: 200\n        },\n        imageBox: {\n            width: 300,\n            height: 200\n        },\n        mode: _components_flex_4items_box__WEBPACK_IMPORTED_MODULE_3__.itemsMode.pride\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (_home_module_scss__WEBPACK_IMPORTED_MODULE_6___default().about__prides),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"hide-on-small\",\n                style: {\n                    marginTop: 30\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_flex_4items_box__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    ...PridesInfo\n                }, void 0, false, {\n                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\about.tsx\",\n                    lineNumber: 143,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\about.tsx\",\n                lineNumber: 142,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_certificates__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\about.tsx\",\n                lineNumber: 146,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\about.tsx\",\n        lineNumber: 141,\n        columnNumber: 5\n    }, this);\n}\nfunction Contacts({ isAboutPage = false }) {\n    const t = (0,_locales_client__WEBPACK_IMPORTED_MODULE_5__.useI18n)();\n    const ContactItem = ({ iconUrl, text })=>{\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: (_home_module_scss__WEBPACK_IMPORTED_MODULE_6___default().contacts__info__item),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    src: iconUrl,\n                    width: 18,\n                    height: 20,\n                    alt: \"icon\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\about.tsx\",\n                    lineNumber: 163,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        display: \"flex\",\n                        flexDirection: \"column\",\n                        gap: 4\n                    },\n                    children: text.split(`/n`).map((t, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: t\n                        }, index, false, {\n                            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\about.tsx\",\n                            lineNumber: 166,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\about.tsx\",\n                    lineNumber: 164,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\about.tsx\",\n            lineNumber: 162,\n            columnNumber: 7\n        }, this);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `${(_home_module_scss__WEBPACK_IMPORTED_MODULE_6___default().contacts)} ${isAboutPage ? (_home_module_scss__WEBPACK_IMPORTED_MODULE_6___default()[\"contacts--page\"]) : \"\"}`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_home_module_scss__WEBPACK_IMPORTED_MODULE_6___default().contacts__info),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_home_module_scss__WEBPACK_IMPORTED_MODULE_6___default().contacts__info__title),\n                        children: t(\"contactUs\")\n                    }, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\about.tsx\",\n                        lineNumber: 180,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_home_module_scss__WEBPACK_IMPORTED_MODULE_6___default().contacts__info__items),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ContactItem, {\n                                iconUrl: \"/contact-position.svg\",\n                                text: t(\"cylanAddress\")\n                            }, void 0, false, {\n                                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\about.tsx\",\n                                lineNumber: 182,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ContactItem, {\n                                iconUrl: \"/contact-email.svg\",\n                                text: \"<EMAIL>\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\about.tsx\",\n                                lineNumber: 186,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ContactItem, {\n                                iconUrl: \"/contact-phone.svg\",\n                                text: \"+86-0755-83073491\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\about.tsx\",\n                                lineNumber: 187,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\about.tsx\",\n                        lineNumber: 181,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\about.tsx\",\n                lineNumber: 179,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_home_module_scss__WEBPACK_IMPORTED_MODULE_6___default().contacts__address),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                    href: \"https://map.baidu.com/poi/%E5%90%88%E6%88%90%E5%8F%B7%E6%B7%B1%E5%9C%B3%E6%B0%91%E4%BF%97%E6%96%87%E5%8C%96%E4%BA%A7%E4%B8%9A%E5%9B%AD/@12682764.738888016,2566016.5718568345,14z?uid=eb63e5cd850d1ef4a3acc4a1&ugc_type=3&ugc_ver=1&device_ratio=1&compat=1&pcevaname=pc4.1&querytype=detailConInfo\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        src: \"/address-image.webp\",\n                        width: 625,\n                        height: 249,\n                        alt: \"address\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\about.tsx\",\n                        lineNumber: 192,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\about.tsx\",\n                    lineNumber: 191,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\about.tsx\",\n                lineNumber: 190,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\about.tsx\",\n        lineNumber: 174,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/[locale]/ui/home/<USER>");

/***/ }),

/***/ "(ssr)/./app/[locale]/ui/home/<USER>":
/*!*****************************************!*\
  !*** ./app/[locale]/ui/home/<USER>
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ FooterLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _home_module_scss__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./home.module.scss */ \"(ssr)/./app/[locale]/ui/home/<USER>");\n/* harmony import */ var _home_module_scss__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(_home_module_scss__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _components_nav_list__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../components/nav-list */ \"(ssr)/./app/[locale]/ui/components/nav-list.tsx\");\n/* harmony import */ var _locales_client__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/locales/client */ \"(ssr)/./locales/client.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\nfunction FooterLayout() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_locales_client__WEBPACK_IMPORTED_MODULE_5__.I18nProviderClient, {\n        locale: (0,_locales_client__WEBPACK_IMPORTED_MODULE_5__.useCurrentLocale)(),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Footer, {}, void 0, false, {\n            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\footer.tsx\",\n            lineNumber: 13,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\footer.tsx\",\n        lineNumber: 12,\n        columnNumber: 5\n    }, this);\n}\nfunction Footer() {\n    const t = (0,_locales_client__WEBPACK_IMPORTED_MODULE_5__.useI18n)();\n    const renderLink = ({ link, text })=>{\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n            href: link,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                children: text\n            }, void 0, false, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\footer.tsx\",\n                lineNumber: 29,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\footer.tsx\",\n            lineNumber: 28,\n            columnNumber: 7\n        }, this);\n    };\n    const FooterItem = ({ title, links })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: (_home_module_scss__WEBPACK_IMPORTED_MODULE_6___default().footer__links__item),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: title\n                }, void 0, false, {\n                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\footer.tsx\",\n                    lineNumber: 42,\n                    columnNumber: 7\n                }, this),\n                links.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((react__WEBPACK_IMPORTED_MODULE_1___default().Fragment), {\n                        children: renderLink({\n                            link: item.link,\n                            text: item.text\n                        })\n                    }, `${item.link}-${index}`, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\footer.tsx\",\n                        lineNumber: 44,\n                        columnNumber: 9\n                    }, this))\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\footer.tsx\",\n            lineNumber: 41,\n            columnNumber: 5\n        }, this);\n    const productLinks = [\n        {\n            link: \"/product?tab=01\",\n            text: t(\"productCamera\")\n        },\n        {\n            link: \"/product?tab=02\",\n            text: t(\"productTranslator\")\n        }\n    ];\n    const supportLinks = [\n        {\n            link: \"/support/download_client\",\n            text: t(\"downloadClient\")\n        },\n        {\n            link: \"/support/help\",\n            text: t(\"help\")\n        }\n    ];\n    const aboutLinks = [\n        {\n            link: \"/about#about-cylan\",\n            text: t(\"aboutCylan\")\n        },\n        {\n            link: \"/about#prides\",\n            text: t(\"cylanPrides\")\n        },\n        {\n            link: \"/about#contacts\",\n            text: t(\"contactUs\")\n        }\n    ];\n    const Follow = ()=>{\n        const [isShowPop, setIsShowpop] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: (_home_module_scss__WEBPACK_IMPORTED_MODULE_6___default().footer__links__follow),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: t(\"followUs\")\n                }, void 0, false, {\n                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\footer.tsx\",\n                    lineNumber: 93,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    onMouseEnter: ()=>{\n                        setIsShowpop(true);\n                    },\n                    onMouseLeave: ()=>{\n                        setIsShowpop(false);\n                    },\n                    className: (_home_module_scss__WEBPACK_IMPORTED_MODULE_6___default().footer__links__follow__weixin),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            src: \"/weixin.svg\",\n                            width: 20,\n                            height: 20,\n                            alt: \"weixin\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\footer.tsx\",\n                            lineNumber: 103,\n                            columnNumber: 11\n                        }, this),\n                        isShowPop && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    src: \"/support/imcam-gongzhonghao.jpg\",\n                                    width: 140,\n                                    height: 140,\n                                    alt: \"\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\footer.tsx\",\n                                    lineNumber: 111,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: t(\"imcamGongzhonghao\")\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\footer.tsx\",\n                                    lineNumber: 117,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    className: \"hide-on-medium hide-on-large\",\n                                    href: \"/support/imcam-gongzhonghao.jpg\",\n                                    download: true,\n                                    children: t(\"downloadQRcode\")\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\footer.tsx\",\n                                    lineNumber: 118,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\footer.tsx\",\n                            lineNumber: 110,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\footer.tsx\",\n                    lineNumber: 94,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\footer.tsx\",\n            lineNumber: 92,\n            columnNumber: 7\n        }, this);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (_home_module_scss__WEBPACK_IMPORTED_MODULE_6___default().footer),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `${(_home_module_scss__WEBPACK_IMPORTED_MODULE_6___default().footer__logo)} hide-on-medium hide-on-large`,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    href: \"/\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        src: \"/cylan_logo-white.png\",\n                        width: 125,\n                        height: 44,\n                        alt: \"logo\",\n                        unoptimized: true\n                    }, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\footer.tsx\",\n                        lineNumber: 136,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\footer.tsx\",\n                    lineNumber: 135,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\footer.tsx\",\n                lineNumber: 134,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `${(_home_module_scss__WEBPACK_IMPORTED_MODULE_6___default().footer__links)} hide-on-small`,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `${(_home_module_scss__WEBPACK_IMPORTED_MODULE_6___default().footer__logo)}`,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            href: \"/\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                src: \"/cylan_logo-white.png\",\n                                width: 125,\n                                height: 44,\n                                alt: \"logo\",\n                                unoptimized: true\n                            }, void 0, false, {\n                                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\footer.tsx\",\n                                lineNumber: 148,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\footer.tsx\",\n                            lineNumber: 147,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\footer.tsx\",\n                        lineNumber: 146,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FooterItem, {\n                        title: t(\"productCenter\"),\n                        links: productLinks\n                    }, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\footer.tsx\",\n                        lineNumber: 157,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FooterItem, {\n                        title: t(\"support\"),\n                        links: supportLinks\n                    }, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\footer.tsx\",\n                        lineNumber: 159,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FooterItem, {\n                        title: t(\"aboutUs\"),\n                        links: aboutLinks\n                    }, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\footer.tsx\",\n                        lineNumber: 161,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Follow, {}, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\footer.tsx\",\n                        lineNumber: 162,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\footer.tsx\",\n                lineNumber: 145,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"hide-on-large hide-on-medium\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_nav_list__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        isFooter: true\n                    }, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\footer.tsx\",\n                        lineNumber: 165,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Follow, {}, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\footer.tsx\",\n                        lineNumber: 166,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\footer.tsx\",\n                lineNumber: 164,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_home_module_scss__WEBPACK_IMPORTED_MODULE_6___default().footer__copyright),\n                children: [\n                    t(\"copyrightText\"),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        className: (_home_module_scss__WEBPACK_IMPORTED_MODULE_6___default().footer__copyright__link),\n                        href: \"https://beian.miit.gov.cn/\",\n                        target: \"_blank\",\n                        children: t(\"copyrightLink\")\n                    }, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\footer.tsx\",\n                        lineNumber: 170,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\footer.tsx\",\n                lineNumber: 168,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\footer.tsx\",\n        lineNumber: 133,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/[locale]/ui/home/<USER>");

/***/ }),

/***/ "(ssr)/./app/[locale]/ui/home/<USER>":
/*!**************************************!*\
  !*** ./app/[locale]/ui/home/<USER>
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NavLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _home_module_scss__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./home.module.scss */ \"(ssr)/./app/[locale]/ui/home/<USER>");\n/* harmony import */ var _home_module_scss__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(_home_module_scss__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _components_dropdown_window__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../components/dropdown-window */ \"(ssr)/./app/[locale]/ui/components/dropdown-window.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_nav_list__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../components/nav-list */ \"(ssr)/./app/[locale]/ui/components/nav-list.tsx\");\n/* harmony import */ var _locales_client__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/locales/client */ \"(ssr)/./locales/client.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\n\nfunction NavLayout() {\n    const selectedLayoutSegment = (0,next_navigation__WEBPACK_IMPORTED_MODULE_5__.useSelectedLayoutSegment)();\n    const isNotFoundPage = selectedLayoutSegment === \"__DEFAULT__\";\n    if (isNotFoundPage) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {}, void 0, false, {\n        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\nav.tsx\",\n        lineNumber: 21,\n        columnNumber: 30\n    }, this);\n    else return(// eslint-disable-next-line react-hooks/rules-of-hooks\n    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_locales_client__WEBPACK_IMPORTED_MODULE_7__.I18nProviderClient, {\n        locale: (0,_locales_client__WEBPACK_IMPORTED_MODULE_7__.useCurrentLocale)(),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Nav, {}, void 0, false, {\n            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\nav.tsx\",\n            lineNumber: 26,\n            columnNumber: 9\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\nav.tsx\",\n        lineNumber: 25,\n        columnNumber: 7\n    }, this));\n}\nfunction Nav() {\n    const t = (0,_locales_client__WEBPACK_IMPORTED_MODULE_7__.useI18n)();\n    const [isScrolled, setIsScrolled] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\n    const [isShowMenu, setShowMenu] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)(()=>{\n        // 监听滚动事件\n        const handleScroll = ()=>{\n            // 获取当前滚动位置\n            const scrollTop = window.scrollY;\n            // 根据滚动位置是否大于0来判断是否添加投影效果\n            setIsScrolled(scrollTop > 0);\n        };\n        window.addEventListener(\"scroll\", handleScroll);\n        return ()=>{\n            window.removeEventListener(\"scroll\", handleScroll);\n        };\n    }, []);\n    const handleNavListClick = ()=>{\n        setShowMenu(false);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            position: \"relative\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_home_module_scss__WEBPACK_IMPORTED_MODULE_8___default().nav__placeholder)\n            }, void 0, false, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\nav.tsx\",\n                lineNumber: 57,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `${(_home_module_scss__WEBPACK_IMPORTED_MODULE_8___default().nav)} ${isScrolled ? (_home_module_scss__WEBPACK_IMPORTED_MODULE_8___default()[\"nav--scrolled\"]) : \"\"} ${isShowMenu ? (_home_module_scss__WEBPACK_IMPORTED_MODULE_8___default()[\"nav--scrolled\"]) : \"\"}`,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_home_module_scss__WEBPACK_IMPORTED_MODULE_8___default().nav__content),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                            href: \"/\",\n                            style: {\n                                height: 44\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                src: \"/cylan_logo.png\",\n                                width: 125,\n                                height: 44,\n                                alt: \"Cylan Logo\",\n                                unoptimized: true\n                            }, void 0, false, {\n                                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\nav.tsx\",\n                                lineNumber: 65,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\nav.tsx\",\n                            lineNumber: 64,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: `${(_home_module_scss__WEBPACK_IMPORTED_MODULE_8___default().nav__list)} hide-on-small hide-on-medium`,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NavListItem, {\n                                    title: t(\"home\"),\n                                    link: \"/\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\nav.tsx\",\n                                    lineNumber: 76,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NavListItem, {\n                                    title: t(\"productCenter\"),\n                                    showArrow: true,\n                                    link: \"/product\",\n                                    links: [\n                                        {\n                                            link: \"/product?tab=01\",\n                                            text: t(\"productCamera\")\n                                        },\n                                        {\n                                            link: \"/product?tab=02\",\n                                            text: t(\"productTranslator\")\n                                        }\n                                    ]\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\nav.tsx\",\n                                    lineNumber: 77,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NavListItem, {\n                                    title: t(\"support\"),\n                                    showArrow: true,\n                                    link: \"/support\",\n                                    links: [\n                                        {\n                                            link: \"/support/download_client\",\n                                            text: t(\"downloadClient\")\n                                        },\n                                        {\n                                            link: \"/support/help\",\n                                            text: t(\"help\")\n                                        }\n                                    ]\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\nav.tsx\",\n                                    lineNumber: 93,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NavListItem, {\n                                    title: t(\"aboutUs\"),\n                                    link: \"/about\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\nav.tsx\",\n                                    lineNumber: 109,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\nav.tsx\",\n                            lineNumber: 75,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (_home_module_scss__WEBPACK_IMPORTED_MODULE_8___default().nav__right),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Language, {}, void 0, false, {\n                                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\nav.tsx\",\n                                    lineNumber: 114,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>{\n                                        setShowMenu(!isShowMenu);\n                                    },\n                                    className: `${(_home_module_scss__WEBPACK_IMPORTED_MODULE_8___default().nav__right__menu)} hide-on-large`,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        src: \"/menu.svg\",\n                                        width: 49,\n                                        height: 50,\n                                        alt: \"menu\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\nav.tsx\",\n                                        lineNumber: 122,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\nav.tsx\",\n                                    lineNumber: 116,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\nav.tsx\",\n                            lineNumber: 113,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\nav.tsx\",\n                    lineNumber: 62,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\nav.tsx\",\n                lineNumber: 58,\n                columnNumber: 7\n            }, this),\n            isShowMenu && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `${(_home_module_scss__WEBPACK_IMPORTED_MODULE_8___default().nav__drop)} hide-on-large`,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_nav_list__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            onClick: ()=>handleNavListClick()\n                        }, void 0, false, {\n                            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\nav.tsx\",\n                            lineNumber: 135,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\nav.tsx\",\n                        lineNumber: 134,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_home_module_scss__WEBPACK_IMPORTED_MODULE_8___default().nav__mask),\n                        onClick: ()=>{\n                            setShowMenu(false);\n                        }\n                    }, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\nav.tsx\",\n                        lineNumber: 137,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\nav.tsx\",\n        lineNumber: 56,\n        columnNumber: 5\n    }, this);\n}\nfunction ArrowDown() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        src: \"/arrow-down.svg\",\n        height: 10,\n        width: 16,\n        alt: \"\"\n    }, void 0, false, {\n        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\nav.tsx\",\n        lineNumber: 150,\n        columnNumber: 10\n    }, this);\n}\nfunction NavListItem({ title, link = \"/\", showArrow = false, links = [] }) {\n    // 鼠标悬停\n    const [isHovered, setIsHovered] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\n    const handleMouseEnter = ()=>{\n        setIsHovered(true);\n    };\n    const handleMouseLeave = ()=>{\n        setIsHovered(false);\n    };\n    let pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_5__.usePathname)();\n    if (pathname.includes(\"/zh/\") || pathname.includes(\"/en/\")) {\n        pathname = pathname.replace(/\\/zh|\\/en/, \"\");\n    } else {\n        pathname = \"/\";\n    }\n    pathname = \"/\" + pathname.split(\"/\")[1];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        onMouseEnter: handleMouseEnter,\n        onMouseLeave: handleMouseLeave,\n        children: showArrow ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dropdown_window__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            onClick: ()=>setIsHovered(false),\n            list: links,\n            show: isHovered,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                href: link,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: `${(_home_module_scss__WEBPACK_IMPORTED_MODULE_8___default().nav__list__item)} ${pathname === link.split(\"?\")[0] ? (_home_module_scss__WEBPACK_IMPORTED_MODULE_8___default()[\"nav__list__item--active\"]) : \"\"}`,\n                    children: [\n                        title,\n                        \" \",\n                        showArrow && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ArrowDown, {}, void 0, false, {\n                            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\nav.tsx\",\n                            lineNumber: 202,\n                            columnNumber: 37\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\nav.tsx\",\n                    lineNumber: 196,\n                    columnNumber: 13\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\nav.tsx\",\n                lineNumber: 195,\n                columnNumber: 11\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\nav.tsx\",\n            lineNumber: 190,\n            columnNumber: 9\n        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n            href: link,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `${(_home_module_scss__WEBPACK_IMPORTED_MODULE_8___default().nav__list__item)} ${(_home_module_scss__WEBPACK_IMPORTED_MODULE_8___default()[\"nav__list__item--link\"])} ${pathname === `/${link.split(\"/\")[1]}` ? (_home_module_scss__WEBPACK_IMPORTED_MODULE_8___default()[\"nav__list__item--active\"]) : \"\"}`,\n                children: [\n                    title,\n                    \" \",\n                    showArrow && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ArrowDown, {}, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\nav.tsx\",\n                        lineNumber: 215,\n                        columnNumber: 35\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\nav.tsx\",\n                lineNumber: 208,\n                columnNumber: 11\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\nav.tsx\",\n            lineNumber: 207,\n            columnNumber: 9\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\nav.tsx\",\n        lineNumber: 188,\n        columnNumber: 5\n    }, this);\n}\nfunction Language() {\n    const changeLocale = (0,_locales_client__WEBPACK_IMPORTED_MODULE_7__.useChangeLocale)();\n    const currentLocal = (0,_locales_client__WEBPACK_IMPORTED_MODULE_7__.useCurrentLocale)();\n    const t = (0,_locales_client__WEBPACK_IMPORTED_MODULE_7__.useI18n)();\n    const LangItem = ({ lang, isActive = false })=>{\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n            onClick: ()=>{\n                if (isActive) return;\n                changeLocale(lang);\n            },\n            className: `${isActive ? (_home_module_scss__WEBPACK_IMPORTED_MODULE_8___default()[\"nav__right__language__text--active\"]) : \"\"} ${(_home_module_scss__WEBPACK_IMPORTED_MODULE_8___default().nav__right__language__text)}`,\n            children: lang === \"zh\" ? t(\"chinese\") : t(\"english\")\n        }, void 0, false, {\n            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\nav.tsx\",\n            lineNumber: 236,\n            columnNumber: 7\n        }, this);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (_home_module_scss__WEBPACK_IMPORTED_MODULE_8___default().nav__right__language),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LangItem, {\n                lang: \"zh\",\n                isActive: currentLocal === \"zh\"\n            }, void 0, false, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\nav.tsx\",\n                lineNumber: 250,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                children: \"/\"\n            }, void 0, false, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\nav.tsx\",\n                lineNumber: 251,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LangItem, {\n                lang: \"en\",\n                isActive: currentLocal === \"en\"\n            }, void 0, false, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\nav.tsx\",\n                lineNumber: 252,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\nav.tsx\",\n        lineNumber: 249,\n        columnNumber: 5\n    }, this);\n}\nfunction Search() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (_home_module_scss__WEBPACK_IMPORTED_MODULE_8___default().nav__right__search),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n            src: \"/search-icon.svg\",\n            width: 20,\n            height: 20,\n            alt: \"Search icon\"\n        }, void 0, false, {\n            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\nav.tsx\",\n            lineNumber: 260,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\nav.tsx\",\n        lineNumber: 259,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/[locale]/ui/home/<USER>");

/***/ }),

/***/ "(ssr)/./app/not-found.tsx":
/*!***************************!*\
  !*** ./app/not-found.tsx ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NotFound)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_error__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/error */ \"(ssr)/./node_modules/next/error.js\");\n/* harmony import */ var next_error__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_error__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n// Render the default Next.js 404 page when a route\n// is requested that doesn't match the middleware and\n// therefore doesn't have a locale associated with it.\nfunction NotFound() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_error__WEBPACK_IMPORTED_MODULE_1___default()), {\n                statusCode: 404\n            }, void 0, false, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\not-found.tsx\",\n                lineNumber: 13,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\not-found.tsx\",\n            lineNumber: 12,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\not-found.tsx\",\n        lineNumber: 11,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9hcHAvbm90LWZvdW5kLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFFOEI7QUFFOUIsbURBQW1EO0FBQ25ELHFEQUFxRDtBQUNyRCxzREFBc0Q7QUFFdkMsU0FBU0M7SUFDdEIscUJBQ0UsOERBQUNDO1FBQUtDLE1BQUs7a0JBQ1QsNEVBQUNDO3NCQUNDLDRFQUFDSixtREFBS0E7Z0JBQUNLLFlBQVk7Ozs7Ozs7Ozs7Ozs7Ozs7QUFJM0IiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9pbWNhbV9vZmZpY2lhbF9zaXRlLy4vYXBwL25vdC1mb3VuZC50c3g/NWM4MCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCdcclxuXHJcbmltcG9ydCBFcnJvciBmcm9tICduZXh0L2Vycm9yJ1xyXG5cclxuLy8gUmVuZGVyIHRoZSBkZWZhdWx0IE5leHQuanMgNDA0IHBhZ2Ugd2hlbiBhIHJvdXRlXHJcbi8vIGlzIHJlcXVlc3RlZCB0aGF0IGRvZXNuJ3QgbWF0Y2ggdGhlIG1pZGRsZXdhcmUgYW5kXHJcbi8vIHRoZXJlZm9yZSBkb2Vzbid0IGhhdmUgYSBsb2NhbGUgYXNzb2NpYXRlZCB3aXRoIGl0LlxyXG5cclxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gTm90Rm91bmQoKSB7XHJcbiAgcmV0dXJuIChcclxuICAgIDxodG1sIGxhbmc9XCJlblwiPlxyXG4gICAgICA8Ym9keT5cclxuICAgICAgICA8RXJyb3Igc3RhdHVzQ29kZT17NDA0fSAvPlxyXG4gICAgICA8L2JvZHk+XHJcbiAgICA8L2h0bWw+XHJcbiAgKVxyXG59XHJcbiJdLCJuYW1lcyI6WyJFcnJvciIsIk5vdEZvdW5kIiwiaHRtbCIsImxhbmciLCJib2R5Iiwic3RhdHVzQ29kZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./app/not-found.tsx\n");

/***/ }),

/***/ "(ssr)/./locales/client.ts":
/*!***************************!*\
  !*** ./locales/client.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   I18nProviderClient: () => (/* binding */ I18nProviderClient),\n/* harmony export */   useChangeLocale: () => (/* binding */ useChangeLocale),\n/* harmony export */   useCurrentLocale: () => (/* binding */ useCurrentLocale),\n/* harmony export */   useI18n: () => (/* binding */ useI18n),\n/* harmony export */   useScopedI18n: () => (/* binding */ useScopedI18n)\n/* harmony export */ });\n/* harmony import */ var next_international_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-international/client */ \"(ssr)/./node_modules/next-international/dist/app/client/index.js\");\n/* harmony import */ var next_international_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_international_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst { useI18n, useScopedI18n, I18nProviderClient, useCurrentLocale, useChangeLocale } = (0,next_international_client__WEBPACK_IMPORTED_MODULE_0__.createI18nClient)({\n    en: ()=>__webpack_require__.e(/*! import() */ \"_ssr_locales_en_ts\").then(__webpack_require__.bind(__webpack_require__, /*! ./en */ \"(ssr)/./locales/en.ts\")),\n    zh: ()=>__webpack_require__.e(/*! import() */ \"_ssr_locales_zh_ts\").then(__webpack_require__.bind(__webpack_require__, /*! ./zh */ \"(ssr)/./locales/zh.ts\"))\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9sb2NhbGVzL2NsaWVudC50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBQTZEO0FBRXRELE1BQU0sRUFDWEMsT0FBTyxFQUNQQyxhQUFhLEVBQ2JDLGtCQUFrQixFQUNsQkMsZ0JBQWdCLEVBQ2hCQyxlQUFlLEVBQ2hCLEdBQUdMLDJFQUFnQkEsQ0FBQztJQUNuQk0sSUFBSSxJQUFNLG9KQUFjO0lBQ3hCQyxJQUFJLElBQU0sb0pBQWM7QUFDMUIsR0FBRyIsInNvdXJjZXMiOlsid2VicGFjazovL2ltY2FtX29mZmljaWFsX3NpdGUvLi9sb2NhbGVzL2NsaWVudC50cz9jZjQ1Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGNyZWF0ZUkxOG5DbGllbnQgfSBmcm9tIFwibmV4dC1pbnRlcm5hdGlvbmFsL2NsaWVudFwiO1xyXG5cclxuZXhwb3J0IGNvbnN0IHtcclxuICB1c2VJMThuLFxyXG4gIHVzZVNjb3BlZEkxOG4sXHJcbiAgSTE4blByb3ZpZGVyQ2xpZW50LFxyXG4gIHVzZUN1cnJlbnRMb2NhbGUsXHJcbiAgdXNlQ2hhbmdlTG9jYWxlLFxyXG59ID0gY3JlYXRlSTE4bkNsaWVudCh7XHJcbiAgZW46ICgpID0+IGltcG9ydChcIi4vZW5cIiksXHJcbiAgemg6ICgpID0+IGltcG9ydChcIi4vemhcIiksXHJcbn0pO1xyXG4iXSwibmFtZXMiOlsiY3JlYXRlSTE4bkNsaWVudCIsInVzZUkxOG4iLCJ1c2VTY29wZWRJMThuIiwiSTE4blByb3ZpZGVyQ2xpZW50IiwidXNlQ3VycmVudExvY2FsZSIsInVzZUNoYW5nZUxvY2FsZSIsImVuIiwiemgiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./locales/client.ts\n");

/***/ }),

/***/ "(ssr)/./app/[locale]/ui/components/components.scss":
/*!****************************************************!*\
  !*** ./app/[locale]/ui/components/components.scss ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"e6ee3d37fbc5\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9hcHAvW2xvY2FsZV0vdWkvY29tcG9uZW50cy9jb21wb25lbnRzLnNjc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9pbWNhbV9vZmZpY2lhbF9zaXRlLy4vYXBwL1tsb2NhbGVdL3VpL2NvbXBvbmVudHMvY29tcG9uZW50cy5zY3NzPzQ4MzAiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCJlNmVlM2QzN2ZiYzVcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./app/[locale]/ui/components/components.scss\n");

/***/ }),

/***/ "(rsc)/./app/globals.scss":
/*!**************************!*\
  !*** ./app/globals.scss ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"dabe1292f855\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5zY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vaW1jYW1fb2ZmaWNpYWxfc2l0ZS8uL2FwcC9nbG9iYWxzLnNjc3M/YWZhZCJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImRhYmUxMjkyZjg1NVwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.scss\n");

/***/ }),

/***/ "(rsc)/./app/[locale]/page.module.scss":
/*!***************************************!*\
  !*** ./app/[locale]/page.module.scss ***!
  \***************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"page404container\": \"page_page404container__c1LIB\",\n\t\"page404\": \"page_page404__1cE3u\",\n\t\"page404__content\": \"page_page404__content__oco9m\",\n\t\"page404__buttons\": \"page_page404__buttons__8oWnx\",\n\t\"page404__buttons__back\": \"page_page404__buttons__back__z0MU7\",\n\t\"page404__buttons__home\": \"page_page404__buttons__home__sx9Sv\"\n};\n\nmodule.exports.__checksum = \"346715d5ce1e\"\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvW2xvY2FsZV0vcGFnZS5tb2R1bGUuc2NzcyIsIm1hcHBpbmdzIjoiQUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUEseUJBQXlCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vaW1jYW1fb2ZmaWNpYWxfc2l0ZS8uL2FwcC9bbG9jYWxlXS9wYWdlLm1vZHVsZS5zY3NzPzQ0MWUiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gRXhwb3J0c1xubW9kdWxlLmV4cG9ydHMgPSB7XG5cdFwicGFnZTQwNGNvbnRhaW5lclwiOiBcInBhZ2VfcGFnZTQwNGNvbnRhaW5lcl9fYzFMSUJcIixcblx0XCJwYWdlNDA0XCI6IFwicGFnZV9wYWdlNDA0X18xY0UzdVwiLFxuXHRcInBhZ2U0MDRfX2NvbnRlbnRcIjogXCJwYWdlX3BhZ2U0MDRfX2NvbnRlbnRfX29jbzltXCIsXG5cdFwicGFnZTQwNF9fYnV0dG9uc1wiOiBcInBhZ2VfcGFnZTQwNF9fYnV0dG9uc19fOG9XbnhcIixcblx0XCJwYWdlNDA0X19idXR0b25zX19iYWNrXCI6IFwicGFnZV9wYWdlNDA0X19idXR0b25zX19iYWNrX196ME1VN1wiLFxuXHRcInBhZ2U0MDRfX2J1dHRvbnNfX2hvbWVcIjogXCJwYWdlX3BhZ2U0MDRfX2J1dHRvbnNfX2hvbWVfX3N4OVN2XCJcbn07XG5cbm1vZHVsZS5leHBvcnRzLl9fY2hlY2tzdW0gPSBcIjM0NjcxNWQ1Y2UxZVwiXG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/[locale]/page.module.scss\n");

/***/ }),

/***/ "(ssr)/./app/[locale]/ui/about/about.module.scss":
/*!*************************************************!*\
  !*** ./app/[locale]/ui/about/about.module.scss ***!
  \*************************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"about\": \"about_about__wvePq\",\n\t\"about__image\": \"about_about__image__KxBNO\",\n\t\"about__content\": \"about_about__content__LS1_A\",\n\t\"about__content__main\": \"about_about__content__main__HF1T_\",\n\t\"about__content__main__header\": \"about_about__content__main__header__l3IFd\",\n\t\"about__content__main__header__dash\": \"about_about__content__main__header__dash__46adj\",\n\t\"about__content__main__header__subtitle\": \"about_about__content__main__header__subtitle__Q8v_4\",\n\t\"about__content__main__grid\": \"about_about__content__main__grid__IqWdA\",\n\t\"about__content__main__panel\": \"about_about__content__main__panel__FXe3v\",\n\t\"about__content__main__panel__title\": \"about_about__content__main__panel__title__oNH7i\",\n\t\"about__content__main__panel__title--title2\": \"about_about__content__main__panel__title--title2__oNe15\",\n\t\"about__content__main__panel__title--title3\": \"about_about__content__main__panel__title--title3__BoQ01\",\n\t\"about__content__main__panel__list\": \"about_about__content__main__panel__list__UwCfz\",\n\t\"about__content__main__panel__list--twoCols\": \"about_about__content__main__panel__list--twoCols__tV3fi\",\n\t\"about__content__main__panel__line\": \"about_about__content__main__panel__line__LTorV\",\n\t\"about__content__main__panel--span2\": \"about_about__content__main__panel--span2__Q1lRT\",\n\t\"about__content__main__panel--emphasis\": \"about_about__content__main__panel--emphasis__XSsj6\",\n\t\"about__content__main__view\": \"about_about__content__main__view__SDZLN\",\n\t\"about__content__main__quotes\": \"about_about__content__main__quotes__gj59l\",\n\t\"about__content__main__quotes__item\": \"about_about__content__main__quotes__item__fN1Rr\",\n\t\"about__content__career__companytime\": \"about_about__content__career__companytime__HUZ14\",\n\t\"about__content__career__timeline\": \"about_about__content__career__timeline__hCaJK\",\n\t\"about__content__career__timeline__item\": \"about_about__content__career__timeline__item__r5rr0\",\n\t\"about__content__career__timeline__item--reverse\": \"about_about__content__career__timeline__item--reverse__s8Qhz\",\n\t\"about__content__prides\": \"about_about__content__prides__0DhgW\",\n\t\"about__content__prides__list\": \"about_about__content__prides__list__LSiiG\",\n\t\"about__content__prides__list__item\": \"about_about__content__prides__list__item__BPyny\",\n\t\"about__content__prides__list__item--normal\": \"about_about__content__prides__list__item--normal__tpjHv\",\n\t\"about__content__prides__list__item--large\": \"about_about__content__prides__list__item--large__VfdUa\",\n\t\"about__content__prides__list__item--small\": \"about_about__content__prides__list__item--small__fRQpL\",\n\t\"about__content__prides__list__item__cover\": \"about_about__content__prides__list__item__cover__GN83j\",\n\t\"about__content__contacts\": \"about_about__content__contacts__S9y1Y\"\n};\n\nmodule.exports.__checksum = \"6024ea9158cf\"\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/[locale]/ui/about/about.module.scss\n");

/***/ }),

/***/ "(ssr)/./app/[locale]/ui/home/<USER>":
/*!***********************************************!*\
  !*** ./app/[locale]/ui/home/<USER>
  \***********************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"nav--scrolled\": \"home_nav--scrolled__f5oaX\",\n\t\"nav\": \"home_nav__gr65i\",\n\t\"nav__placeholder\": \"home_nav__placeholder__R_bDj\",\n\t\"nav__content\": \"home_nav__content__gXoig\",\n\t\"nav__list\": \"home_nav__list__dmRBz\",\n\t\"nav__list__item\": \"home_nav__list__item__Ti9E4\",\n\t\"nav__list__item--link\": \"home_nav__list__item--link__dx88I\",\n\t\"nav__list__item--active\": \"home_nav__list__item--active__oPRJX\",\n\t\"nav__right\": \"home_nav__right__4GRXj\",\n\t\"nav__right__language\": \"home_nav__right__language__YzU4O\",\n\t\"nav__right__language__text\": \"home_nav__right__language__text__yUNmB\",\n\t\"nav__right__language__text--active\": \"home_nav__right__language__text--active__e4h1y\",\n\t\"nav__right__search\": \"home_nav__right__search__QAvd_\",\n\t\"nav__right__menu\": \"home_nav__right__menu__tMG4s\",\n\t\"nav__drop\": \"home_nav__drop__RNd3y\",\n\t\"nav__mask\": \"home_nav__mask__YVj5E\",\n\t\"banner-slider\": \"home_banner-slider__UBj9I\",\n\t\"banner-slider__swiper\": \"home_banner-slider__swiper__9Bl8q\",\n\t\"banner-slider__slide\": \"home_banner-slider__slide__2U7Uu\",\n\t\"banner-slider__button\": \"home_banner-slider__button__GKjGy\",\n\t\"swiper-button-disabled\": \"home_swiper-button-disabled__siaDk\",\n\t\"banner-slider__button-prev\": \"home_banner-slider__button-prev__VeikB\",\n\t\"banner-slider__button-next\": \"home_banner-slider__button-next__UC5d2\",\n\t\"banner-slider__pagination\": \"home_banner-slider__pagination__J58et\",\n\t\"banner-slider__bullet\": \"home_banner-slider__bullet__c3a9X\",\n\t\"banner-slider__bullet--active\": \"home_banner-slider__bullet--active__5BpSZ\",\n\t\"banner-slider__switcher\": \"home_banner-slider__switcher__SoaxS\",\n\t\"banner-slider__switcher--right\": \"home_banner-slider__switcher--right___84yN\",\n\t\"banner-slider__indicator\": \"home_banner-slider__indicator__0OOU4\",\n\t\"banner-slider__indicator__item\": \"home_banner-slider__indicator__item__f8vBh\",\n\t\"hot-spot\": \"home_hot-spot__HmXBc\",\n\t\"hot-spot__captain\": \"home_hot-spot__captain__P7sAg\",\n\t\"hot-spot__captain__more\": \"home_hot-spot__captain__more__hoe30\",\n\t\"hot-spot__news\": \"home_hot-spot__news__mFPbX\",\n\t\"hot-spot__news__left\": \"home_hot-spot__news__left__bYNbF\",\n\t\"hot-spot__news__right\": \"home_hot-spot__news__right__IYxxG\",\n\t\"hot-spot__news__item\": \"home_hot-spot__news__item__i6svw\",\n\t\"hot-spot__news__item__info\": \"home_hot-spot__news__item__info__GSDkz\",\n\t\"hot-spot__news__item__image\": \"home_hot-spot__news__item__image__0Dj0A\",\n\t\"hot-spot__news__item__image--right\": \"home_hot-spot__news__item__image--right__scey9\",\n\t\"hot-spot__news__item--left\": \"home_hot-spot__news__item--left__W7YL9\",\n\t\"about\": \"home_about__vPbFi\",\n\t\"about__cover\": \"home_about__cover__SPvuD\",\n\t\"about__content\": \"home_about__content__EA9EW\",\n\t\"about__content__time\": \"home_about__content__time__HcHq6\",\n\t\"about__content__time__item\": \"home_about__content__time__item__n4W8C\",\n\t\"about__content__time--page\": \"home_about__content__time--page__Azkeq\",\n\t\"about__content__prides\": \"home_about__content__prides__zHCpT\",\n\t\"contacts\": \"home_contacts__TRH4N\",\n\t\"contacts--page\": \"home_contacts--page__0BV0w\",\n\t\"contacts__info\": \"home_contacts__info__pIGy0\",\n\t\"contacts__info__items\": \"home_contacts__info__items__qUSi9\",\n\t\"contacts__info__title\": \"home_contacts__info__title__3_UHT\",\n\t\"contacts__info__item\": \"home_contacts__info__item__eDIm0\",\n\t\"contacts__address\": \"home_contacts__address___ZQdr\",\n\t\"footer\": \"home_footer__qefFZ\",\n\t\"footer__logo\": \"home_footer__logo__jG71u\",\n\t\"footer__links\": \"home_footer__links__q5uiZ\",\n\t\"footer__links__item\": \"home_footer__links__item__gB0TO\",\n\t\"footer__links__follow\": \"home_footer__links__follow__jv8nP\",\n\t\"footer__links__follow__weixin\": \"home_footer__links__follow__weixin__yeCNp\",\n\t\"footer__copyright\": \"home_footer__copyright__M6lua\",\n\t\"footer__copyright__link\": \"home_footer__copyright__link__PBT0B\",\n\t\"banner-slider__indicator__item--active\": \"home_banner-slider__indicator__item--active__Pkcak\"\n};\n\nmodule.exports.__checksum = \"b3f4251f6407\"\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/[locale]/ui/home/<USER>");

/***/ }),

/***/ "(rsc)/./app/[locale]/about/page.tsx":
/*!*************************************!*\
  !*** ./app/[locale]/about/page.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Page),\n/* harmony export */   generateMetadata: () => (/* binding */ generateMetadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _ui_about_about_content__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../ui/about/about-content */ \"(rsc)/./app/[locale]/ui/about/about-content.tsx\");\n\n\nasync function generateMetadata({ params, searchParams }, parent) {\n    const locale = params.locale;\n    return {\n        title: locale === \"zh\" ? \"关于我们\" : \"About Us\",\n        description: \"赛蓝科技 引领生活\",\n        icons: {\n            icon: \"/favicon.ico\"\n        }\n    };\n}\nfunction Page() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_about_about_content__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {}, void 0, false, {\n        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\about\\\\page.tsx\",\n        lineNumber: 20,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvW2xvY2FsZV0vYWJvdXQvcGFnZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQW9EO0FBSTdDLGVBQWVDLGlCQUNwQixFQUFFQyxNQUFNLEVBQUVDLFlBQVksRUFBYSxFQUNuQ0MsTUFBeUI7SUFFekIsTUFBTUMsU0FBU0gsT0FBT0csTUFBTTtJQUM1QixPQUFPO1FBQ0xDLE9BQU9ELFdBQVcsT0FBTyxTQUFTO1FBQ2xDRSxhQUFhO1FBQ2JDLE9BQU87WUFDTEMsTUFBTTtRQUNSO0lBQ0Y7QUFDRjtBQUVlLFNBQVNDO0lBQ3RCLHFCQUFPLDhEQUFDViwrREFBWUE7Ozs7O0FBQ3RCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vaW1jYW1fb2ZmaWNpYWxfc2l0ZS8uL2FwcC9bbG9jYWxlXS9hYm91dC9wYWdlLnRzeD9hZmI3Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBBYm91dENvbnRlbnQgZnJvbSAnLi4vdWkvYWJvdXQvYWJvdXQtY29udGVudCdcclxuaW1wb3J0IHsgUGFnZVByb3BzIH0gZnJvbSAnQC9kYXRhL3R5cGUnXHJcbmltcG9ydCB0eXBlIHsgTWV0YWRhdGEsIFJlc29sdmluZ01ldGFkYXRhIH0gZnJvbSAnbmV4dCdcclxuXHJcbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBnZW5lcmF0ZU1ldGFkYXRhKFxyXG4gIHsgcGFyYW1zLCBzZWFyY2hQYXJhbXMgfTogUGFnZVByb3BzLFxyXG4gIHBhcmVudDogUmVzb2x2aW5nTWV0YWRhdGFcclxuKTogUHJvbWlzZTxNZXRhZGF0YT4ge1xyXG4gIGNvbnN0IGxvY2FsZSA9IHBhcmFtcy5sb2NhbGVcclxuICByZXR1cm4ge1xyXG4gICAgdGl0bGU6IGxvY2FsZSA9PT0gJ3poJyA/ICflhbPkuo7miJHku6wnIDogJ0Fib3V0IFVzJyxcclxuICAgIGRlc2NyaXB0aW9uOiAn6LWb6JOd56eR5oqAIOW8lemihueUn+a0uycsXHJcbiAgICBpY29uczoge1xyXG4gICAgICBpY29uOiAnL2Zhdmljb24uaWNvJyxcclxuICAgIH0sXHJcbiAgfVxyXG59XHJcblxyXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBQYWdlKCkge1xyXG4gIHJldHVybiA8QWJvdXRDb250ZW50IC8+XHJcbn1cclxuIl0sIm5hbWVzIjpbIkFib3V0Q29udGVudCIsImdlbmVyYXRlTWV0YWRhdGEiLCJwYXJhbXMiLCJzZWFyY2hQYXJhbXMiLCJwYXJlbnQiLCJsb2NhbGUiLCJ0aXRsZSIsImRlc2NyaXB0aW9uIiwiaWNvbnMiLCJpY29uIiwiUGFnZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/[locale]/about/page.tsx\n");

/***/ }),

/***/ "(rsc)/./app/[locale]/layout.tsx":
/*!*********************************!*\
  !*** ./app/[locale]/layout.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   generateMetadata: () => (/* binding */ generateMetadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_locale_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\[locale]\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\[locale]\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_locale_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_locale_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _app_globals_scss__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/app/globals.scss */ \"(rsc)/./app/globals.scss\");\n/* harmony import */ var _app_locale_ui_home_footer__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/app/[locale]/ui/home/<USER>/ \"(rsc)/./app/[locale]/ui/home/<USER>");\n/* harmony import */ var _app_locale_ui_home_nav__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/app/[locale]/ui/home/<USER>/ \"(rsc)/./app/[locale]/ui/home/<USER>");\n/* harmony import */ var _app_locale_ui_components_back_to_top__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/[locale]/ui/components/back-to-top */ \"(rsc)/./app/[locale]/ui/components/back-to-top.tsx\");\n\n\n\n\n\n\nasync function generateMetadata({ params, searchParams }, parent) {\n    const locale = params.locale;\n    return {\n        description: \"赛蓝科技 引领生活\",\n        icons: {\n            icon: \"/favicon.ico\"\n        }\n    };\n}\nasync function RootLayout({ children, params }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: params.locale,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_app_locale_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5___default().className),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_locale_ui_home_nav__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\layout.tsx\",\n                        lineNumber: 34,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\layout.tsx\",\n                    lineNumber: 33,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                    children: children\n                }, void 0, false, {\n                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\layout.tsx\",\n                    lineNumber: 36,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_locale_ui_home_footer__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\layout.tsx\",\n                        lineNumber: 38,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\layout.tsx\",\n                    lineNumber: 37,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_locale_ui_components_back_to_top__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\layout.tsx\",\n                    lineNumber: 40,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\layout.tsx\",\n            lineNumber: 32,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\layout.tsx\",\n        lineNumber: 31,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/[locale]/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/[locale]/not-found.tsx":
/*!************************************!*\
  !*** ./app/[locale]/not-found.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NotFound)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _app_locale_page_module_scss__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/[locale]/page.module.scss */ \"(rsc)/./app/[locale]/page.module.scss\");\n/* harmony import */ var _app_locale_page_module_scss__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_app_locale_page_module_scss__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ \"(rsc)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _locales_server__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/locales/server */ \"(rsc)/./locales/server.ts\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(rsc)/./node_modules/next/dist/api/link.js\");\n\n\n\n\n\nasync function NotFound() {\n    const t = await (0,_locales_server__WEBPACK_IMPORTED_MODULE_2__.getI18n)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (_app_locale_page_module_scss__WEBPACK_IMPORTED_MODULE_4___default().page404container),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: (_app_locale_page_module_scss__WEBPACK_IMPORTED_MODULE_4___default().page404),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_app_locale_page_module_scss__WEBPACK_IMPORTED_MODULE_4___default().page404__image),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                        src: \"/404.png\",\n                        width: 331,\n                        height: 151,\n                        alt: \"\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\not-found.tsx\",\n                        lineNumber: 13,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\not-found.tsx\",\n                    lineNumber: 12,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_app_locale_page_module_scss__WEBPACK_IMPORTED_MODULE_4___default().page404__content),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: t(\"page404Description\")\n                        }, void 0, false, {\n                            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\not-found.tsx\",\n                            lineNumber: 16,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: t(\"page404Tip1\")\n                        }, void 0, false, {\n                            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\not-found.tsx\",\n                            lineNumber: 17,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: t(\"page404Tip2\")\n                        }, void 0, false, {\n                            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\not-found.tsx\",\n                            lineNumber: 18,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\not-found.tsx\",\n                    lineNumber: 15,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_app_locale_page_module_scss__WEBPACK_IMPORTED_MODULE_4___default().page404__buttons),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        href: \"/\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: (_app_locale_page_module_scss__WEBPACK_IMPORTED_MODULE_4___default().page404__buttons__home),\n                            children: t(\"backToHome\")\n                        }, void 0, false, {\n                            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\not-found.tsx\",\n                            lineNumber: 22,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\not-found.tsx\",\n                        lineNumber: 21,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\not-found.tsx\",\n                    lineNumber: 20,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\not-found.tsx\",\n            lineNumber: 11,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\not-found.tsx\",\n        lineNumber: 10,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/[locale]/not-found.tsx\n");

/***/ }),

/***/ "(rsc)/./app/[locale]/ui/about/about-content.tsx":
/*!*************************************************!*\
  !*** ./app/[locale]/ui/about/about-content.tsx ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\---AProjects---\imcam-officialsite\fastoffical\app\[locale]\ui\about\about-content.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./app/[locale]/ui/components/back-to-top.tsx":
/*!****************************************************!*\
  !*** ./app/[locale]/ui/components/back-to-top.tsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\---AProjects---\imcam-officialsite\fastoffical\app\[locale]\ui\components\back-to-top.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./app/[locale]/ui/home/<USER>":
/*!*****************************************!*\
  !*** ./app/[locale]/ui/home/<USER>
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\---AProjects---\imcam-officialsite\fastoffical\app\[locale]\ui\home\footer.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./app/[locale]/ui/home/<USER>":
/*!**************************************!*\
  !*** ./app/[locale]/ui/home/<USER>
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\---AProjects---\imcam-officialsite\fastoffical\app\[locale]\ui\home\nav.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout)\n/* harmony export */ });\nasync function RootLayout({ children, params }) {\n    return children;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvbGF5b3V0LnRzeCIsIm1hcHBpbmdzIjoiOzs7O0FBQWUsZUFBZUEsV0FBVyxFQUN2Q0MsUUFBUSxFQUNSQyxNQUFNLEVBSU47SUFDQSxPQUFPRDtBQUNUIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vaW1jYW1fb2ZmaWNpYWxfc2l0ZS8uL2FwcC9sYXlvdXQudHN4Pzk5ODgiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgYXN5bmMgZnVuY3Rpb24gUm9vdExheW91dCh7XHJcbiAgY2hpbGRyZW4sXHJcbiAgcGFyYW1zLFxyXG59OiBSZWFkb25seTx7XHJcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZVxyXG4gIHBhcmFtczogeyBsb2NhbGU6IHN0cmluZyB9XHJcbn0+KSB7XHJcbiAgcmV0dXJuIGNoaWxkcmVuXHJcbn1cclxuIl0sIm5hbWVzIjpbIlJvb3RMYXlvdXQiLCJjaGlsZHJlbiIsInBhcmFtcyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/not-found.tsx":
/*!***************************!*\
  !*** ./app/not-found.tsx ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\---AProjects---\imcam-officialsite\fastoffical\app\not-found.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./locales/server.ts":
/*!***************************!*\
  !*** ./locales/server.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getCurrentLocale: () => (/* binding */ getCurrentLocale),\n/* harmony export */   getI18n: () => (/* binding */ getI18n),\n/* harmony export */   getScopedI18n: () => (/* binding */ getScopedI18n),\n/* harmony export */   getStaticParams: () => (/* binding */ getStaticParams)\n/* harmony export */ });\n/* harmony import */ var next_international_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-international/server */ \"(rsc)/./node_modules/next-international/dist/app/server/index.js\");\n/* harmony import */ var next_international_server__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_international_server__WEBPACK_IMPORTED_MODULE_0__);\n\nconst { getI18n, getScopedI18n, getStaticParams, getCurrentLocale } = (0,next_international_server__WEBPACK_IMPORTED_MODULE_0__.createI18nServer)({\n    en: ()=>__webpack_require__.e(/*! import() */ \"_rsc_locales_en_ts\").then(__webpack_require__.bind(__webpack_require__, /*! ./en */ \"(rsc)/./locales/en.ts\")),\n    zh: ()=>__webpack_require__.e(/*! import() */ \"_rsc_locales_zh_ts\").then(__webpack_require__.bind(__webpack_require__, /*! ./zh */ \"(rsc)/./locales/zh.ts\"))\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9sb2NhbGVzL3NlcnZlci50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBNkQ7QUFFdEQsTUFBTSxFQUFFQyxPQUFPLEVBQUVDLGFBQWEsRUFBRUMsZUFBZSxFQUFFQyxnQkFBZ0IsRUFBRSxHQUFHSiwyRUFBZ0JBLENBQUM7SUFDNUZLLElBQUksSUFBTSxvSkFBYztJQUN4QkMsSUFBSSxJQUFNLG9KQUFjO0FBQzFCLEdBQUciLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9pbWNhbV9vZmZpY2lhbF9zaXRlLy4vbG9jYWxlcy9zZXJ2ZXIudHM/MmZlYyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBjcmVhdGVJMThuU2VydmVyIH0gZnJvbSBcIm5leHQtaW50ZXJuYXRpb25hbC9zZXJ2ZXJcIjtcclxuXHJcbmV4cG9ydCBjb25zdCB7IGdldEkxOG4sIGdldFNjb3BlZEkxOG4sIGdldFN0YXRpY1BhcmFtcywgZ2V0Q3VycmVudExvY2FsZSB9ID0gY3JlYXRlSTE4blNlcnZlcih7XHJcbiAgZW46ICgpID0+IGltcG9ydChcIi4vZW5cIiksXHJcbiAgemg6ICgpID0+IGltcG9ydChcIi4vemhcIiksXHJcbn0pO1xyXG4iXSwibmFtZXMiOlsiY3JlYXRlSTE4blNlcnZlciIsImdldEkxOG4iLCJnZXRTY29wZWRJMThuIiwiZ2V0U3RhdGljUGFyYW1zIiwiZ2V0Q3VycmVudExvY2FsZSIsImVuIiwiemgiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./locales/server.ts\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__":
/*!**********************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__ ***!
  \**********************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"32x32\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9hcHAvZmF2aWNvbi5pY28/X19uZXh0X21ldGFkYXRhX18iLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQUEsRUFBaUY7O0FBRWpGLEVBQUUsaUVBQWU7QUFDakIsdUJBQXVCO0FBQ3ZCLHFCQUFxQiw4RkFBbUI7O0FBRXhDO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTCIsInNvdXJjZXMiOlsid2VicGFjazovL2ltY2FtX29mZmljaWFsX3NpdGUvLi9hcHAvZmF2aWNvbi5pY28/ZTEwMyJdLCJzb3VyY2VzQ29udGVudCI6WyIgIGltcG9ydCB7IGZpbGxNZXRhZGF0YVNlZ21lbnQgfSBmcm9tICduZXh0L2Rpc3QvbGliL21ldGFkYXRhL2dldC1tZXRhZGF0YS1yb3V0ZSdcblxuICBleHBvcnQgZGVmYXVsdCAocHJvcHMpID0+IHtcbiAgICBjb25zdCBpbWFnZURhdGEgPSB7XCJ0eXBlXCI6XCJpbWFnZS94LWljb25cIixcInNpemVzXCI6XCIzMngzMlwifVxuICAgIGNvbnN0IGltYWdlVXJsID0gZmlsbE1ldGFkYXRhU2VnbWVudChcIi5cIiwgcHJvcHMucGFyYW1zLCBcImZhdmljb24uaWNvXCIpXG5cbiAgICByZXR1cm4gW3tcbiAgICAgIC4uLmltYWdlRGF0YSxcbiAgICAgIHVybDogaW1hZ2VVcmwgKyBcIlwiLFxuICAgIH1dXG4gIH0iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/next-international"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F%5Blocale%5D%2Fabout%2Fpage&page=%2F%5Blocale%5D%2Fabout%2Fpage&appPaths=%2F%5Blocale%5D%2Fabout%2Fpage&pagePath=private-next-app-dir%2F%5Blocale%5D%2Fabout%2Fpage.tsx&appDir=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();