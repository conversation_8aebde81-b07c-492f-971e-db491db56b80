"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/about/page",{

/***/ "(app-pages-browser)/./app/[locale]/ui/about/about-content.tsx":
/*!*************************************************!*\
  !*** ./app/[locale]/ui/about/about-content.tsx ***!
  \*************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ AboutContentLayout; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _about_module_scss__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./about.module.scss */ \"(app-pages-browser)/./app/[locale]/ui/about/about.module.scss\");\n/* harmony import */ var _about_module_scss__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(_about_module_scss__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _components_page_tabs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../components/page-tabs */ \"(app-pages-browser)/./app/[locale]/ui/components/page-tabs.tsx\");\n/* harmony import */ var _home_about__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../home/<USER>/ \"(app-pages-browser)/./app/[locale]/ui/home/<USER>");\n/* harmony import */ var _components_certificates__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../components/certificates */ \"(app-pages-browser)/./app/[locale]/ui/components/certificates.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _locales_client__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/locales/client */ \"(app-pages-browser)/./locales/client.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$(), _s3 = $RefreshSig$(), _s4 = $RefreshSig$(), _s5 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction AboutContentLayout() {\n    _s();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_locales_client__WEBPACK_IMPORTED_MODULE_7__.I18nProviderClient, {\n        locale: (0,_locales_client__WEBPACK_IMPORTED_MODULE_7__.useCurrentLocale)(),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AboutContent, {}, void 0, false, {\n            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n            lineNumber: 15,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n        lineNumber: 14,\n        columnNumber: 5\n    }, this);\n}\n_s(AboutContentLayout, \"9zQ3KOL0Rwq6cPrON/AdiF1Ksas=\", false, function() {\n    return [\n        _locales_client__WEBPACK_IMPORTED_MODULE_7__.useCurrentLocale\n    ];\n});\n_c = AboutContentLayout;\nfunction AboutContent() {\n    _s1();\n    const t = (0,_locales_client__WEBPACK_IMPORTED_MODULE_7__.useI18n)();\n    const [tabs, setTabs] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)([\n        {\n            id: \"0\",\n            text: t(\"aboutCylan\")\n        },\n        {\n            id: \"1\",\n            text: t(\"cylanPrides\")\n        },\n        {\n            id: \"2\",\n            text: t(\"contactUs\")\n        }\n    ]);\n    const [currentTab, setCurrentTab] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(tabs[0].id);\n    const [trigger, setTrigger] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(currentTab);\n    const contentRef = (0,react__WEBPACK_IMPORTED_MODULE_5__.useRef)(null);\n    const handleTabChange = (tab)=>{\n        setTrigger(tab);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default().about),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"\".concat((_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default().about__image), \" hide-on-small\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                    src: \"/about-banner.webp\",\n                    width: 1920,\n                    height: 900,\n                    alt: \"\",\n                    style: {\n                        width: \"100%\",\n                        height: \"100%\",\n                        objectFit: \"fill\",\n                        objectPosition: \"center\"\n                    },\n                    unoptimized: true\n                }, void 0, false, {\n                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                    lineNumber: 48,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                lineNumber: 47,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h6\", {\n                id: \"about-cylan\"\n            }, void 0, false, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                lineNumber: 62,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"hide-on-small\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_page_tabs__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    title: t(\"aboutUs\"),\n                    iconSrc: \"/about-icon.svg\",\n                    currentTab: currentTab,\n                    tabs: tabs,\n                    showBanner: false,\n                    background: \"transparent\",\n                    onTabChange: (tab)=>{\n                        handleTabChange(tab);\n                    }\n                }, void 0, false, {\n                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                    lineNumber: 64,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                lineNumber: 63,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"hide-on-medium hide-on-large\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_page_tabs__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    title: t(\"aboutUs\"),\n                    iconSrc: \"/about-icon.svg\",\n                    currentTab: currentTab,\n                    tabs: tabs,\n                    bannerMobileSrc: \"/about-banner-mobile.jpg\",\n                    background: \"rgb(214,218,211)\",\n                    onTabChange: (tab)=>{\n                        handleTabChange(tab);\n                    }\n                }, void 0, false, {\n                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                    lineNumber: 77,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                lineNumber: 76,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_5__.Suspense, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Content, {\n                    contentRef: contentRef,\n                    tabs: tabs,\n                    setCurrentTab: setCurrentTab,\n                    trigger: trigger\n                }, void 0, false, {\n                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                    lineNumber: 90,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                lineNumber: 89,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n        lineNumber: 46,\n        columnNumber: 5\n    }, this);\n}\n_s1(AboutContent, \"IfBYfwtzUcCp2dyH1DY6EqbD0y4=\", false, function() {\n    return [\n        _locales_client__WEBPACK_IMPORTED_MODULE_7__.useI18n\n    ];\n});\n_c1 = AboutContent;\nfunction Content(param) {\n    let { tabs, setCurrentTab = ()=>{}, contentRef, trigger } = param;\n    _s2();\n    const aboutCylanRef = (0,react__WEBPACK_IMPORTED_MODULE_5__.useRef)(null);\n    const pridesRef = (0,react__WEBPACK_IMPORTED_MODULE_5__.useRef)(null);\n    const contactRef = (0,react__WEBPACK_IMPORTED_MODULE_5__.useRef)(null);\n    const isInitial = (0,react__WEBPACK_IMPORTED_MODULE_5__.useRef)(true);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_6__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_6__.usePathname)();\n    const handleTabChange = (0,react__WEBPACK_IMPORTED_MODULE_5__.useCallback)((tab)=>{\n        let jump;\n        if (tab === tabs[0].id) jump = \"about-cylan\";\n        else if (tab === tabs[1].id) jump = \"prides\";\n        else jump = \"contacts\";\n        setCurrentTab(tab);\n        router.replace(\"\".concat(pathname, \"/#\").concat(jump));\n    }, [\n        tabs,\n        setCurrentTab,\n        router,\n        pathname\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_5__.useEffect)(()=>{\n        const handleScroll = ()=>{\n            var _aboutCylanRef_current;\n            const scrollHeight = window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop;\n            const top = (_aboutCylanRef_current = aboutCylanRef.current) === null || _aboutCylanRef_current === void 0 ? void 0 : _aboutCylanRef_current.getBoundingClientRect().top;\n            if (top && scrollHeight < top) setCurrentTab(tabs[0].id);\n        };\n        window.addEventListener(\"scroll\", handleScroll);\n        return ()=>{\n            window.removeEventListener(\"scroll\", handleScroll);\n        };\n    }, [\n        handleTabChange,\n        tabs,\n        setCurrentTab\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_5__.useEffect)(()=>{\n        if (isInitial.current) {\n            isInitial.current = false;\n        } else {\n            handleTabChange(trigger);\n        }\n    }, [\n        trigger,\n        handleTabChange\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_locales_client__WEBPACK_IMPORTED_MODULE_7__.I18nProviderClient, {\n        locale: (0,_locales_client__WEBPACK_IMPORTED_MODULE_7__.useCurrentLocale)(),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: contentRef,\n                className: (_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default().about__content),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        ref: aboutCylanRef,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Main, {}, void 0, false, {\n                            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                            lineNumber: 160,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                        lineNumber: 159,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Career, {}, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                        lineNumber: 163,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        ref: pridesRef,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Prides, {}, void 0, false, {\n                            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                            lineNumber: 165,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                        lineNumber: 164,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h6\", {\n                        id: \"contacts\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                        lineNumber: 167,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        ref: contactRef,\n                        className: \"\".concat((_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default().about__content__contacts), \" hide-on-small\"),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_home_about__WEBPACK_IMPORTED_MODULE_3__.Contacts, {\n                            isAboutPage: true\n                        }, void 0, false, {\n                            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                            lineNumber: 173,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                        lineNumber: 169,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                lineNumber: 158,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"\".concat((_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default().about__content__contacts), \" hide-on-medium hide-on-large \"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_home_about__WEBPACK_IMPORTED_MODULE_3__.Contacts, {\n                    isAboutPage: true\n                }, void 0, false, {\n                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                    lineNumber: 179,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                lineNumber: 176,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n        lineNumber: 157,\n        columnNumber: 5\n    }, this);\n}\n_s2(Content, \"fobuBSBxj+j+UAIR5uA8Zn5wrJ4=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_6__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_6__.usePathname,\n        _locales_client__WEBPACK_IMPORTED_MODULE_7__.useCurrentLocale\n    ];\n});\n_c2 = Content;\nfunction Main() {\n    _s3();\n    const t = (0,_locales_client__WEBPACK_IMPORTED_MODULE_7__.useI18n)();\n    const overviewList = [\n        t(\"companySetTime\"),\n        t(\"companyMainLocation\"),\n        t(\"companyCoreTech\"),\n        t(\"companyService\")\n    ];\n    const coreProductList = [\n        t(\"companyCoreProductText1\"),\n        t(\"companyCoreProductText2\"),\n        t(\"companyCoreProductText3\"),\n        t(\"companyCoreProductText4\"),\n        t(\"companyCoreProductText5\"),\n        t(\"companyCoreProductText6\"),\n        t(\"companyCoreProductText7\"),\n        t(\"companyCoreProductText8\"),\n        t(\"companyCoreProductText9\"),\n        t(\"companyCoreProductText10\")\n    ];\n    const globalList = [\n        t(\"companyGlobalText1\"),\n        t(\"companyGlobalText2\"),\n        t(\"companyGlobalText3\"),\n        t(\"companyGlobalText4\"),\n        t(\"companyGlobalText5\")\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default().about__content__main),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default().about__content__main__header),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        children: t(\"aboutCylanTitle\")\n                    }, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                        lineNumber: 220,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: (_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default().about__content__main__header__dash)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                        lineNumber: 221,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: (_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default().about__content__main__header__subtitle),\n                        children: \"全球智能看护解决方案领导者\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                        lineNumber: 222,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                lineNumber: 219,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default().about__content__main__grid),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: (_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default().about__content__main__panel),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                className: \"\".concat((_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default().about__content__main__panel__title), \" \").concat((_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default()[\"about__content__main__panel__title--title1\"])),\n                                children: t(\"companyReview\")\n                            }, void 0, false, {\n                                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                lineNumber: 228,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default().about__content__main__panel__line)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                lineNumber: 230,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                className: (_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default().about__content__main__panel__list),\n                                children: overviewList.map((text, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: text\n                                    }, i, false, {\n                                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                        lineNumber: 234,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                lineNumber: 232,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                        lineNumber: 227,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"\".concat((_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default().about__content__main__panel), \" \").concat((_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default()[\"about__content__main__panel--emphasis\"])),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                className: \"\".concat((_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default().about__content__main__panel__title), \" \").concat((_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default()[\"about__content__main__panel__title--title2\"])),\n                                children: t(\"companyCoreProduct\")\n                            }, void 0, false, {\n                                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                lineNumber: 240,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                className: \"\".concat((_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default().about__content__main__panel__list), \" \").concat((_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default()[\"about__content__main__panel__list--twoCols\"])),\n                                children: coreProductList.map((text, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: text\n                                    }, i, false, {\n                                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                        lineNumber: 243,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                lineNumber: 241,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                        lineNumber: 239,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"\".concat((_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default().about__content__main__panel), \" \").concat((_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default()[\"about__content__main__panel--span2\"])),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                className: \"\".concat((_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default().about__content__main__panel__title), \" \").concat((_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default()[\"about__content__main__panel__title--title3\"])),\n                                children: t(\"companyGlobal\")\n                            }, void 0, false, {\n                                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                lineNumber: 249,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                className: (_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default().about__content__main__panel__list),\n                                children: globalList.map((text, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: text\n                                    }, i, false, {\n                                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                        lineNumber: 252,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                lineNumber: 250,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                        lineNumber: 248,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                lineNumber: 226,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n        lineNumber: 217,\n        columnNumber: 5\n    }, this);\n}\n_s3(Main, \"ZaixJaidTjOGprsR5fELIXmueMU=\", false, function() {\n    return [\n        _locales_client__WEBPACK_IMPORTED_MODULE_7__.useI18n\n    ];\n});\n_c3 = Main;\nfunction Career() {\n    _s4();\n    const t = (0,_locales_client__WEBPACK_IMPORTED_MODULE_7__.useI18n)();\n    let LineType;\n    (function(LineType) {\n        LineType[LineType[\"first\"] = 0] = \"first\";\n        LineType[LineType[\"normal\"] = 1] = \"normal\";\n        LineType[LineType[\"last\"] = 2] = \"last\";\n    })(LineType || (LineType = {}));\n    const items = [\n        {\n            title: \"参加2024年香港春季电子展\",\n            time: \"2024年4月\",\n            text: \"2024年香港春季电子产品展及香港国际资讯科技博览会推出二代触摸屏视频通话智能摄像机参展\",\n            imageSrc: \"/hotspot-image-1.png\"\n        },\n        {\n            title: \"参加2024年香港春季电子展\",\n            time: \"2024年4月\",\n            text: \"2024年香港春季电子产品展及香港国际资讯科技博览会推出二代触摸屏视频通话智能摄像机参展\",\n            imageSrc: \"/hotspot-image-1.png\"\n        },\n        {\n            title: \"参加2024年香港春季电子展\",\n            time: \"2024年4月\",\n            text: \"2024年香港春季电子产品展及香港国际资讯科技博览会推出二代触摸屏视频通话智能摄像机参展\",\n            imageSrc: \"/hotspot-image-1.png\"\n        },\n        {\n            title: \"参加2024年香港春季电子展\",\n            time: \"2024年4月\",\n            text: \"2024年香港春季电子产品展及香港国际资讯科技博览会推出二代触摸屏视频通话智能摄像机参展\",\n            imageSrc: \"/hotspot-image-1.png\"\n        },\n        {\n            title: \"参加2024年香港春季电子展\",\n            time: \"2024年4月\",\n            text: \"2024年香港春季电子产品展及香港国际资讯科技博览会推出二代触摸屏视频通话智能摄像机参展\",\n            imageSrc: \"/hotspot-image-1.png\"\n        }\n    ];\n    const TimeLineItem = (param)=>{\n        let { linetype = 0, isReverse = false, title, time, text, imageSrc } = param;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"\".concat((_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default().about__content__career__timeline__item), \" \").concat(isReverse ? (_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default()[\"about__content__career__timeline__item--reverse\"]) : \"\"),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"hide-on-small\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                        src: imageSrc,\n                                        width: 240,\n                                        height: 160,\n                                        alt: \"\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                        lineNumber: 329,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                    lineNumber: 328,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h6\", {\n                                    children: title\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                    lineNumber: 331,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                            lineNumber: 327,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                        lineNumber: 326,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {}, void 0, false, {\n                                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                lineNumber: 335,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {}, void 0, false, {\n                                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                    lineNumber: 337,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                lineNumber: 336,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {}, void 0, false, {\n                                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                lineNumber: 339,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                        lineNumber: 334,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"hide-on-medium hide-on-large\",\n                                children: time\n                            }, void 0, false, {\n                                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                lineNumber: 342,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"hide-on-medium hide-on-large\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                    src: imageSrc,\n                                    width: 285,\n                                    height: 190,\n                                    alt: \"\",\n                                    style: {\n                                        width: \"100%\",\n                                        height: \"100%\"\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                    lineNumber: 344,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                lineNumber: 343,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"hide-on-small\",\n                                children: time\n                            }, void 0, false, {\n                                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                lineNumber: 352,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h6\", {\n                                className: \"hide-on-medium hide-on-large\",\n                                children: title\n                            }, void 0, false, {\n                                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                lineNumber: 353,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: text\n                            }, void 0, false, {\n                                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                lineNumber: 354,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                        lineNumber: 341,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                lineNumber: 320,\n                columnNumber: 9\n            }, this)\n        }, void 0, false);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default().about__content__career),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                children: t(\"career\")\n            }, void 0, false, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                lineNumber: 363,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                children: t(\"careerDescription\")\n            }, void 0, false, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                lineNumber: 364,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default().about__content__career__companytime),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_home_about__WEBPACK_IMPORTED_MODULE_3__.CompanyTime, {\n                    isAboutPage: true\n                }, void 0, false, {\n                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                    lineNumber: 366,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                lineNumber: 365,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h6\", {\n                id: \"prides\"\n            }, void 0, false, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                lineNumber: 368,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n        lineNumber: 362,\n        columnNumber: 5\n    }, this);\n}\n_s4(Career, \"ZaixJaidTjOGprsR5fELIXmueMU=\", false, function() {\n    return [\n        _locales_client__WEBPACK_IMPORTED_MODULE_7__.useI18n\n    ];\n});\n_c4 = Career;\nfunction Prides() {\n    _s5();\n    const t = (0,_locales_client__WEBPACK_IMPORTED_MODULE_7__.useI18n)();\n    let ImageSize;\n    (function(ImageSize) {\n        ImageSize[ImageSize[\"small\"] = 0] = \"small\";\n        ImageSize[ImageSize[\"normal\"] = 1] = \"normal\";\n        ImageSize[ImageSize[\"large\"] = 2] = \"large\";\n    })(ImageSize || (ImageSize = {}));\n    const [showCoverList, setShowCoverList] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)([\n        {\n            text: t(\"pride1\"),\n            show: false\n        },\n        {\n            text: t(\"pride2\"),\n            show: false\n        },\n        {\n            text: t(\"pride3\"),\n            show: false\n        },\n        {\n            text: t(\"pride4\"),\n            show: false\n        },\n        {\n            text: t(\"pride5\"),\n            show: false\n        },\n        {\n            text: t(\"pride6\"),\n            show: false\n        },\n        {\n            text: t(\"pride7\"),\n            show: false\n        },\n        {\n            text: t(\"pride8\"),\n            show: false\n        },\n        {\n            text: t(\"pride9\"),\n            show: false\n        }\n    ]);\n    const ImageItem = (param)=>{\n        let { src, size = 1, index } = param;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            onClick: ()=>{\n                setShowCoverList(showCoverList.map((item, idx)=>{\n                    if (idx === index) item.show = !item.show;\n                    return item;\n                }));\n            },\n            className: \"\".concat((_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default().about__content__prides__list__item), \" \").concat(size === 2 ? (_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default()[\"about__content__prides__list__item--large\"]) : size === 1 ? (_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default()[\"about__content__prides__list__item--normal\"]) : (_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default()[\"about__content__prides__list__item--small\"])),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                    src: src,\n                    width: size === 2 ? 478 : 231,\n                    height: size === 0 ? 154 : 326,\n                    alt: \"\",\n                    style: {\n                        height: \"100%\",\n                        width: \"100%\",\n                        objectFit: \"fill\"\n                    },\n                    unoptimized: true\n                }, void 0, false, {\n                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                    lineNumber: 463,\n                    columnNumber: 9\n                }, this),\n                showCoverList[index].show && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default().about__content__prides__list__item__cover),\n                    children: showCoverList[index].text\n                }, void 0, false, {\n                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                    lineNumber: 472,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n            lineNumber: 447,\n            columnNumber: 7\n        }, this);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"\".concat((_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default().about__content__prides)),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    children: t(\"cylanPrides\")\n                }, void 0, false, {\n                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                    lineNumber: 483,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"hide-on-small\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default().about__content__prides__list),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ImageItem, {\n                                    src: \"/pride-image-1.jpg\",\n                                    index: 0\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                    lineNumber: 486,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ImageItem, {\n                                    src: \"/pride-image-2.jpg\",\n                                    size: 2,\n                                    index: 1\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                    lineNumber: 487,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ImageItem, {\n                                    src: \"/pride-image-3.jpg\",\n                                    index: 2\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                    lineNumber: 492,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ImageItem, {\n                                    src: \"/pride-image-4.jpg\",\n                                    index: 3\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                    lineNumber: 493,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                            lineNumber: 485,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default().about__content__prides__list),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ImageItem, {\n                                    src: \"/pride-image-5.jpg\",\n                                    size: 0,\n                                    index: 4\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                    lineNumber: 496,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ImageItem, {\n                                    src: \"/pride-image-6.jpg\",\n                                    size: 0,\n                                    index: 5\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                    lineNumber: 501,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ImageItem, {\n                                    src: \"/pride-image-7.jpg\",\n                                    size: 0,\n                                    index: 6\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                    lineNumber: 506,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ImageItem, {\n                                    src: \"/pride-image-8.jpg\",\n                                    size: 0,\n                                    index: 7\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                    lineNumber: 511,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ImageItem, {\n                                    src: \"/pride-image-9.jpg\",\n                                    size: 0,\n                                    index: 8\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                    lineNumber: 516,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                            lineNumber: 495,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                    lineNumber: 484,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_certificates__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                    lineNumber: 524,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n            lineNumber: 482,\n            columnNumber: 7\n        }, this)\n    }, void 0, false);\n}\n_s5(Prides, \"/VTK/rcuaULTrNtxGRekcBaFSek=\", false, function() {\n    return [\n        _locales_client__WEBPACK_IMPORTED_MODULE_7__.useI18n\n    ];\n});\n_c5 = Prides;\nvar _c, _c1, _c2, _c3, _c4, _c5;\n$RefreshReg$(_c, \"AboutContentLayout\");\n$RefreshReg$(_c1, \"AboutContent\");\n$RefreshReg$(_c2, \"Content\");\n$RefreshReg$(_c3, \"Main\");\n$RefreshReg$(_c4, \"Career\");\n$RefreshReg$(_c5, \"Prides\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/[locale]/ui/about/about-content.tsx\n"));

/***/ })

});