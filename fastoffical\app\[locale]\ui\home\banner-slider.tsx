'use client'

import { Swiper, SwiperSlide } from 'swiper/react'
import { Navigation, Pagination, Autoplay } from 'swiper/modules'
import Image from 'next/image'
import Link from 'next/link'
import { useCurrentLocale } from '@/locales/client'

// Import Swiper styles
import 'swiper/css'
import 'swiper/css/navigation'
import 'swiper/css/pagination'

import styles from './home.module.scss'

export default function BannerSlider() {
  const locale = useCurrentLocale()
  const images = [
    locale === 'zh' ? '/home/<USER>' : '/home/<USER>',
    locale === 'zh' ? '/home/<USER>' : '/home/<USER>',
  ]

  const urls = [
    '/product/c31',
    '/product/t1pro'
  ]

  return (
    <div className={styles['banner-slider']}>
      <Swiper
        modules={[Navigation, Pagination, Autoplay]}
        spaceBetween={0}
        slidesPerView={1}
        navigation={{
          nextEl: `.${styles['banner-slider__button-next']}`,
          prevEl: `.${styles['banner-slider__button-prev']}`,
        }}
        pagination={{
          el: `.${styles['banner-slider__pagination']}`,
          clickable: true,
          bulletClass: styles['banner-slider__bullet'],
          bulletActiveClass: styles['banner-slider__bullet--active'],
        }}
        autoplay={{
          delay: 5000,
          disableOnInteraction: false,
          pauseOnMouseEnter: true,
        }}
        loop={images.length > 1}
        className={styles['banner-slider__swiper']}
      >
        {images.map((item, index) => (
          <SwiperSlide key={index}>
            <div className={styles['banner-slider__slide']}>
              <Link style={{
                height: "100%",
                width: "100%"
              }} href={urls[index]}>
                <Image
                  src={item}
                  width={1920}
                  height={500}
                  alt={`Banner ${index + 1}`}
                  unoptimized
                  style={{
                    width: '100%',
                    height: '100%',
                    objectFit: 'cover',
                    objectPosition: 'center',
                  }}
                />
              </Link>
            </div>
          </SwiperSlide>
        ))}
      </Swiper>

      {images.length > 1 && (
        <>
          {/* 自定义导航按钮 */}
          <div className={`${styles['banner-slider__button-prev']} ${styles['banner-slider__button']}`}>
            <Image
              src="/slider-left.svg"
              width={44}
              height={44}
              alt="Previous"
            />
          </div>
          <div className={`${styles['banner-slider__button-next']} ${styles['banner-slider__button']}`}>
            <Image
              src="/slider-right.svg"
              width={44}
              height={44}
              alt="Next"
            />
          </div>

          {/* 自定义分页器 */}
          <div className={styles['banner-slider__pagination']}></div>
        </>
      )}
    </div>
  )
}


