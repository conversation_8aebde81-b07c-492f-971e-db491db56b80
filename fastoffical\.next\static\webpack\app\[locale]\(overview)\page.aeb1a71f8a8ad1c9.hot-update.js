"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(overview)/page",{

/***/ "(app-pages-browser)/./app/[locale]/ui/home/<USER>":
/*!************************************************!*\
  !*** ./app/[locale]/ui/home/<USER>
  \************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ BannerSlider; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var swiper_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! swiper/react */ \"(app-pages-browser)/./node_modules/swiper/swiper-react.mjs\");\n/* harmony import */ var swiper_modules__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! swiper/modules */ \"(app-pages-browser)/./node_modules/swiper/modules/index.mjs\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _locales_client__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/locales/client */ \"(app-pages-browser)/./locales/client.ts\");\n/* harmony import */ var swiper_css__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! swiper/css */ \"(app-pages-browser)/./node_modules/swiper/swiper.css\");\n/* harmony import */ var swiper_css_navigation__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! swiper/css/navigation */ \"(app-pages-browser)/./node_modules/swiper/modules/navigation.css\");\n/* harmony import */ var swiper_css_pagination__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! swiper/css/pagination */ \"(app-pages-browser)/./node_modules/swiper/modules/pagination.css\");\n/* harmony import */ var _home_module_scss__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./home.module.scss */ \"(app-pages-browser)/./app/[locale]/ui/home/<USER>");\n/* harmony import */ var _home_module_scss__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(_home_module_scss__WEBPACK_IMPORTED_MODULE_9__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n// Import Swiper styles\n\n\n\n\nfunction BannerSlider() {\n    _s();\n    const locale = (0,_locales_client__WEBPACK_IMPORTED_MODULE_5__.useCurrentLocale)();\n    const images = [\n        locale === \"zh\" ? \"/home/<USER>" : \"/home/<USER>",\n        locale === \"zh\" ? \"/home/<USER>" : \"/home/<USER>"\n    ];\n    const urls = [\n        \"/product/c31\",\n        \"/product/t1pro\"\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (_home_module_scss__WEBPACK_IMPORTED_MODULE_9___default()[\"banner-slider\"]),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(swiper_react__WEBPACK_IMPORTED_MODULE_1__.Swiper, {\n                modules: [\n                    swiper_modules__WEBPACK_IMPORTED_MODULE_2__.Navigation,\n                    swiper_modules__WEBPACK_IMPORTED_MODULE_2__.Pagination,\n                    swiper_modules__WEBPACK_IMPORTED_MODULE_2__.Autoplay\n                ],\n                spaceBetween: 0,\n                slidesPerView: 1,\n                navigation: {\n                    nextEl: \".\".concat((_home_module_scss__WEBPACK_IMPORTED_MODULE_9___default()[\"banner-slider__button-next\"])),\n                    prevEl: \".\".concat((_home_module_scss__WEBPACK_IMPORTED_MODULE_9___default()[\"banner-slider__button-prev\"]))\n                },\n                pagination: {\n                    el: \".\".concat((_home_module_scss__WEBPACK_IMPORTED_MODULE_9___default()[\"banner-slider__pagination\"])),\n                    clickable: true,\n                    bulletClass: (_home_module_scss__WEBPACK_IMPORTED_MODULE_9___default()[\"banner-slider__bullet\"]),\n                    bulletActiveClass: (_home_module_scss__WEBPACK_IMPORTED_MODULE_9___default()[\"banner-slider__bullet--active\"])\n                },\n                autoplay: {\n                    delay: 5000,\n                    disableOnInteraction: false,\n                    pauseOnMouseEnter: true\n                },\n                loop: images.length > 1,\n                className: (_home_module_scss__WEBPACK_IMPORTED_MODULE_9___default()[\"banner-slider__swiper\"]),\n                children: images.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(swiper_react__WEBPACK_IMPORTED_MODULE_1__.SwiperSlide, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (_home_module_scss__WEBPACK_IMPORTED_MODULE_9___default()[\"banner-slider__slide\"]),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                style: {\n                                    height: \"100%\",\n                                    width: \"100%\"\n                                },\n                                href: urls[index],\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    src: item,\n                                    width: 1920,\n                                    height: 500,\n                                    alt: \"Banner \".concat(index + 1),\n                                    unoptimized: true,\n                                    style: {\n                                        width: \"100%\",\n                                        height: \"100%\",\n                                        objectFit: \"cover\",\n                                        objectPosition: \"center\"\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\banner-slider.tsx\",\n                                    lineNumber: 59,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\banner-slider.tsx\",\n                                lineNumber: 55,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\banner-slider.tsx\",\n                            lineNumber: 54,\n                            columnNumber: 13\n                        }, this)\n                    }, index, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\banner-slider.tsx\",\n                        lineNumber: 53,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\banner-slider.tsx\",\n                lineNumber: 30,\n                columnNumber: 7\n            }, this),\n            images.length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"\".concat((_home_module_scss__WEBPACK_IMPORTED_MODULE_9___default()[\"banner-slider__button-prev\"]), \" \").concat((_home_module_scss__WEBPACK_IMPORTED_MODULE_9___default()[\"banner-slider__button\"])),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            src: \"/slider-left.svg\",\n                            width: 44,\n                            height: 44,\n                            alt: \"Previous\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\banner-slider.tsx\",\n                            lineNumber: 82,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\banner-slider.tsx\",\n                        lineNumber: 81,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"\".concat((_home_module_scss__WEBPACK_IMPORTED_MODULE_9___default()[\"banner-slider__button-next\"]), \" \").concat((_home_module_scss__WEBPACK_IMPORTED_MODULE_9___default()[\"banner-slider__button\"])),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            src: \"/slider-right.svg\",\n                            width: 44,\n                            height: 44,\n                            alt: \"Next\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\banner-slider.tsx\",\n                            lineNumber: 90,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\banner-slider.tsx\",\n                        lineNumber: 89,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_home_module_scss__WEBPACK_IMPORTED_MODULE_9___default()[\"banner-slider__pagination\"])\n                    }, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\banner-slider.tsx\",\n                        lineNumber: 99,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\banner-slider.tsx\",\n        lineNumber: 29,\n        columnNumber: 5\n    }, this);\n}\n_s(BannerSlider, \"0bgUYh39Ymd94mXW1RH411vtZ4A=\", false, function() {\n    return [\n        _locales_client__WEBPACK_IMPORTED_MODULE_5__.useCurrentLocale\n    ];\n});\n_c = BannerSlider;\nvar _c;\n$RefreshReg$(_c, \"BannerSlider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/[locale]/ui/home/<USER>"));

/***/ })

});