"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/about/page",{

/***/ "(app-pages-browser)/./app/[locale]/ui/about/about-content.tsx":
/*!*************************************************!*\
  !*** ./app/[locale]/ui/about/about-content.tsx ***!
  \*************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ AboutContentLayout; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _about_module_scss__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./about.module.scss */ \"(app-pages-browser)/./app/[locale]/ui/about/about.module.scss\");\n/* harmony import */ var _about_module_scss__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(_about_module_scss__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _components_page_tabs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../components/page-tabs */ \"(app-pages-browser)/./app/[locale]/ui/components/page-tabs.tsx\");\n/* harmony import */ var _home_about__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../home/<USER>/ \"(app-pages-browser)/./app/[locale]/ui/home/<USER>");\n/* harmony import */ var _components_certificates__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../components/certificates */ \"(app-pages-browser)/./app/[locale]/ui/components/certificates.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _locales_client__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/locales/client */ \"(app-pages-browser)/./locales/client.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$(), _s3 = $RefreshSig$(), _s4 = $RefreshSig$(), _s5 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction AboutContentLayout() {\n    _s();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_locales_client__WEBPACK_IMPORTED_MODULE_7__.I18nProviderClient, {\n        locale: (0,_locales_client__WEBPACK_IMPORTED_MODULE_7__.useCurrentLocale)(),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AboutContent, {}, void 0, false, {\n            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n            lineNumber: 15,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n        lineNumber: 14,\n        columnNumber: 5\n    }, this);\n}\n_s(AboutContentLayout, \"9zQ3KOL0Rwq6cPrON/AdiF1Ksas=\", false, function() {\n    return [\n        _locales_client__WEBPACK_IMPORTED_MODULE_7__.useCurrentLocale\n    ];\n});\n_c = AboutContentLayout;\nfunction AboutContent() {\n    _s1();\n    const t = (0,_locales_client__WEBPACK_IMPORTED_MODULE_7__.useI18n)();\n    const [tabs, setTabs] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)([\n        {\n            id: \"0\",\n            text: t(\"aboutCylan\")\n        },\n        {\n            id: \"1\",\n            text: t(\"cylanPrides\")\n        },\n        {\n            id: \"2\",\n            text: t(\"contactUs\")\n        }\n    ]);\n    const [currentTab, setCurrentTab] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(tabs[0].id);\n    const [trigger, setTrigger] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(currentTab);\n    const contentRef = (0,react__WEBPACK_IMPORTED_MODULE_5__.useRef)(null);\n    const handleTabChange = (tab)=>{\n        setTrigger(tab);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default().about),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"\".concat((_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default().about__image), \" hide-on-small\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                    src: \"/about-banner.webp\",\n                    width: 1920,\n                    height: 900,\n                    alt: \"\",\n                    style: {\n                        width: \"100%\",\n                        height: \"100%\",\n                        objectFit: \"fill\",\n                        objectPosition: \"center\"\n                    },\n                    unoptimized: true\n                }, void 0, false, {\n                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                    lineNumber: 48,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                lineNumber: 47,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h6\", {\n                id: \"about-cylan\"\n            }, void 0, false, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                lineNumber: 62,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"hide-on-small\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_page_tabs__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    title: t(\"aboutUs\"),\n                    iconSrc: \"/about-icon.svg\",\n                    currentTab: currentTab,\n                    tabs: tabs,\n                    showBanner: false,\n                    background: \"transparent\",\n                    onTabChange: (tab)=>{\n                        handleTabChange(tab);\n                    }\n                }, void 0, false, {\n                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                    lineNumber: 64,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                lineNumber: 63,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"hide-on-medium hide-on-large\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_page_tabs__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    title: t(\"aboutUs\"),\n                    iconSrc: \"/about-icon.svg\",\n                    currentTab: currentTab,\n                    tabs: tabs,\n                    bannerMobileSrc: \"/about-banner-mobile.jpg\",\n                    background: \"rgb(214,218,211)\",\n                    onTabChange: (tab)=>{\n                        handleTabChange(tab);\n                    }\n                }, void 0, false, {\n                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                    lineNumber: 77,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                lineNumber: 76,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_5__.Suspense, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Content, {\n                    contentRef: contentRef,\n                    tabs: tabs,\n                    setCurrentTab: setCurrentTab,\n                    trigger: trigger\n                }, void 0, false, {\n                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                    lineNumber: 90,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                lineNumber: 89,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n        lineNumber: 46,\n        columnNumber: 5\n    }, this);\n}\n_s1(AboutContent, \"IfBYfwtzUcCp2dyH1DY6EqbD0y4=\", false, function() {\n    return [\n        _locales_client__WEBPACK_IMPORTED_MODULE_7__.useI18n\n    ];\n});\n_c1 = AboutContent;\nfunction Content(param) {\n    let { tabs, setCurrentTab = ()=>{}, contentRef, trigger } = param;\n    _s2();\n    const aboutCylanRef = (0,react__WEBPACK_IMPORTED_MODULE_5__.useRef)(null);\n    const pridesRef = (0,react__WEBPACK_IMPORTED_MODULE_5__.useRef)(null);\n    const contactRef = (0,react__WEBPACK_IMPORTED_MODULE_5__.useRef)(null);\n    const isInitial = (0,react__WEBPACK_IMPORTED_MODULE_5__.useRef)(true);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_6__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_6__.usePathname)();\n    const handleTabChange = (0,react__WEBPACK_IMPORTED_MODULE_5__.useCallback)((tab)=>{\n        let jump;\n        if (tab === tabs[0].id) jump = \"about-cylan\";\n        else if (tab === tabs[1].id) jump = \"prides\";\n        else jump = \"contacts\";\n        setCurrentTab(tab);\n        router.replace(\"\".concat(pathname, \"/#\").concat(jump));\n    }, [\n        tabs,\n        setCurrentTab,\n        router,\n        pathname\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_5__.useEffect)(()=>{\n        const handleScroll = ()=>{\n            var _aboutCylanRef_current;\n            const scrollHeight = window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop;\n            const top = (_aboutCylanRef_current = aboutCylanRef.current) === null || _aboutCylanRef_current === void 0 ? void 0 : _aboutCylanRef_current.getBoundingClientRect().top;\n            if (top && scrollHeight < top) setCurrentTab(tabs[0].id);\n        };\n        window.addEventListener(\"scroll\", handleScroll);\n        return ()=>{\n            window.removeEventListener(\"scroll\", handleScroll);\n        };\n    }, [\n        handleTabChange,\n        tabs,\n        setCurrentTab\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_5__.useEffect)(()=>{\n        if (isInitial.current) {\n            isInitial.current = false;\n        } else {\n            handleTabChange(trigger);\n        }\n    }, [\n        trigger,\n        handleTabChange\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_locales_client__WEBPACK_IMPORTED_MODULE_7__.I18nProviderClient, {\n        locale: (0,_locales_client__WEBPACK_IMPORTED_MODULE_7__.useCurrentLocale)(),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: contentRef,\n                className: (_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default().about__content),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        ref: aboutCylanRef,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Main, {}, void 0, false, {\n                            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                            lineNumber: 160,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                        lineNumber: 159,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Career, {}, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                        lineNumber: 163,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        ref: pridesRef,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Prides, {}, void 0, false, {\n                            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                            lineNumber: 165,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                        lineNumber: 164,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h6\", {\n                        id: \"contacts\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                        lineNumber: 167,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        ref: contactRef,\n                        className: \"\".concat((_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default().about__content__contacts), \" hide-on-small\"),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_home_about__WEBPACK_IMPORTED_MODULE_3__.Contacts, {\n                            isAboutPage: true\n                        }, void 0, false, {\n                            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                            lineNumber: 173,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                        lineNumber: 169,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                lineNumber: 158,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"\".concat((_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default().about__content__contacts), \" hide-on-medium hide-on-large \"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_home_about__WEBPACK_IMPORTED_MODULE_3__.Contacts, {\n                    isAboutPage: true\n                }, void 0, false, {\n                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                    lineNumber: 179,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                lineNumber: 176,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n        lineNumber: 157,\n        columnNumber: 5\n    }, this);\n}\n_s2(Content, \"fobuBSBxj+j+UAIR5uA8Zn5wrJ4=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_6__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_6__.usePathname,\n        _locales_client__WEBPACK_IMPORTED_MODULE_7__.useCurrentLocale\n    ];\n});\n_c2 = Content;\nfunction Main() {\n    _s3();\n    const t = (0,_locales_client__WEBPACK_IMPORTED_MODULE_7__.useI18n)();\n    const items = [\n        {\n            imageSrc: \"/hotspot-image-1.png\",\n            title: t(\"aboutCylanQuotesTitle1\"),\n            text: t(\"aboutCylanQuotesText1\")\n        },\n        {\n            imageSrc: \"/hotspot-image-1.png\",\n            title: t(\"aboutCylanQuotesTitle2\"),\n            text: t(\"aboutCylanQuotesText2\")\n        },\n        {\n            imageSrc: \"/hotspot-image-1.png\",\n            title: t(\"aboutCylanQuotesTitle3\"),\n            text: t(\"aboutCylanQuotesText3\")\n        }\n    ];\n    const ViewCard = (param)=>{\n        let { title, textList } = param;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: (_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default().about__content__main__view__card),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                    children: title\n                }, void 0, false, {\n                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                    lineNumber: 208,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default().about__content__main__view__card__content),\n                    children: textList.map((text, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: text\n                        }, index, false, {\n                            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                            lineNumber: 210,\n                            columnNumber: 42\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                    lineNumber: 209,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n            lineNumber: 207,\n            columnNumber: 7\n        }, this);\n    };\n    const textList1 = [\n        t(\"companySetTime\"),\n        t(\"companyMainLocation\"),\n        t(\"companyCoreTech\"),\n        t(\"companyService\")\n    ];\n    const textList2 = [\n        t(\"companyCoreProductText1\"),\n        t(\"companyCoreProductText2\"),\n        t(\"companyCoreProductText3\"),\n        t(\"companyCoreProductText4\"),\n        t(\"companyCoreProductText5\"),\n        t(\"companyCoreProductText6\"),\n        t(\"companyCoreProductText7\"),\n        t(\"companyCoreProductText8\"),\n        t(\"companyCoreProductText9\"),\n        t(\"companyCoreProductText10\")\n    ];\n    const textList3 = [\n        t(\"companyGlobalText1\"),\n        t(\"companyGlobalText2\"),\n        t(\"companyGlobalText3\"),\n        t(\"companyGlobalText4\"),\n        t(\"companyGlobalText5\")\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default().about__content__main),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                children: t(\"aboutCylanTitle\")\n            }, void 0, false, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                lineNumber: 246,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default().about__content__main__view),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ViewCard, {\n                        title: t(\"companyReview\"),\n                        textList: textList1\n                    }, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                        lineNumber: 248,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ViewCard, {\n                        title: t(\"companyCoreProduct\"),\n                        textList: textList2\n                    }, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                        lineNumber: 249,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ViewCard, {\n                        title: t(\"companyGlobal\"),\n                        textList: textList3\n                    }, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                        lineNumber: 250,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                lineNumber: 247,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n        lineNumber: 245,\n        columnNumber: 5\n    }, this);\n}\n_s3(Main, \"ZaixJaidTjOGprsR5fELIXmueMU=\", false, function() {\n    return [\n        _locales_client__WEBPACK_IMPORTED_MODULE_7__.useI18n\n    ];\n});\n_c3 = Main;\nfunction Career() {\n    _s4();\n    const t = (0,_locales_client__WEBPACK_IMPORTED_MODULE_7__.useI18n)();\n    let LineType;\n    (function(LineType) {\n        LineType[LineType[\"first\"] = 0] = \"first\";\n        LineType[LineType[\"normal\"] = 1] = \"normal\";\n        LineType[LineType[\"last\"] = 2] = \"last\";\n    })(LineType || (LineType = {}));\n    const items = [\n        {\n            title: \"参加2024年香港春季电子展\",\n            time: \"2024年4月\",\n            text: \"2024年香港春季电子产品展及香港国际资讯科技博览会推出二代触摸屏视频通话智能摄像机参展\",\n            imageSrc: \"/hotspot-image-1.png\"\n        },\n        {\n            title: \"参加2024年香港春季电子展\",\n            time: \"2024年4月\",\n            text: \"2024年香港春季电子产品展及香港国际资讯科技博览会推出二代触摸屏视频通话智能摄像机参展\",\n            imageSrc: \"/hotspot-image-1.png\"\n        },\n        {\n            title: \"参加2024年香港春季电子展\",\n            time: \"2024年4月\",\n            text: \"2024年香港春季电子产品展及香港国际资讯科技博览会推出二代触摸屏视频通话智能摄像机参展\",\n            imageSrc: \"/hotspot-image-1.png\"\n        },\n        {\n            title: \"参加2024年香港春季电子展\",\n            time: \"2024年4月\",\n            text: \"2024年香港春季电子产品展及香港国际资讯科技博览会推出二代触摸屏视频通话智能摄像机参展\",\n            imageSrc: \"/hotspot-image-1.png\"\n        },\n        {\n            title: \"参加2024年香港春季电子展\",\n            time: \"2024年4月\",\n            text: \"2024年香港春季电子产品展及香港国际资讯科技博览会推出二代触摸屏视频通话智能摄像机参展\",\n            imageSrc: \"/hotspot-image-1.png\"\n        }\n    ];\n    const TimeLineItem = (param)=>{\n        let { linetype = 0, isReverse = false, title, time, text, imageSrc } = param;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"\".concat((_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default().about__content__career__timeline__item), \" \").concat(isReverse ? (_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default()[\"about__content__career__timeline__item--reverse\"]) : \"\"),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"hide-on-small\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                        src: imageSrc,\n                                        width: 240,\n                                        height: 160,\n                                        alt: \"\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                        lineNumber: 324,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                    lineNumber: 323,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h6\", {\n                                    children: title\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                    lineNumber: 326,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                            lineNumber: 322,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                        lineNumber: 321,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {}, void 0, false, {\n                                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                lineNumber: 330,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {}, void 0, false, {\n                                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                    lineNumber: 332,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                lineNumber: 331,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {}, void 0, false, {\n                                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                lineNumber: 334,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                        lineNumber: 329,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"hide-on-medium hide-on-large\",\n                                children: time\n                            }, void 0, false, {\n                                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                lineNumber: 337,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"hide-on-medium hide-on-large\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                    src: imageSrc,\n                                    width: 285,\n                                    height: 190,\n                                    alt: \"\",\n                                    style: {\n                                        width: \"100%\",\n                                        height: \"100%\"\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                    lineNumber: 339,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                lineNumber: 338,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"hide-on-small\",\n                                children: time\n                            }, void 0, false, {\n                                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                lineNumber: 347,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h6\", {\n                                className: \"hide-on-medium hide-on-large\",\n                                children: title\n                            }, void 0, false, {\n                                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                lineNumber: 348,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: text\n                            }, void 0, false, {\n                                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                lineNumber: 349,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                        lineNumber: 336,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                lineNumber: 315,\n                columnNumber: 9\n            }, this)\n        }, void 0, false);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default().about__content__career),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                children: t(\"career\")\n            }, void 0, false, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                lineNumber: 358,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                children: t(\"careerDescription\")\n            }, void 0, false, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                lineNumber: 359,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default().about__content__career__companytime),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_home_about__WEBPACK_IMPORTED_MODULE_3__.CompanyTime, {\n                    isAboutPage: true\n                }, void 0, false, {\n                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                    lineNumber: 361,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                lineNumber: 360,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h6\", {\n                id: \"prides\"\n            }, void 0, false, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                lineNumber: 363,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n        lineNumber: 357,\n        columnNumber: 5\n    }, this);\n}\n_s4(Career, \"ZaixJaidTjOGprsR5fELIXmueMU=\", false, function() {\n    return [\n        _locales_client__WEBPACK_IMPORTED_MODULE_7__.useI18n\n    ];\n});\n_c4 = Career;\nfunction Prides() {\n    _s5();\n    const t = (0,_locales_client__WEBPACK_IMPORTED_MODULE_7__.useI18n)();\n    let ImageSize;\n    (function(ImageSize) {\n        ImageSize[ImageSize[\"small\"] = 0] = \"small\";\n        ImageSize[ImageSize[\"normal\"] = 1] = \"normal\";\n        ImageSize[ImageSize[\"large\"] = 2] = \"large\";\n    })(ImageSize || (ImageSize = {}));\n    const [showCoverList, setShowCoverList] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)([\n        {\n            text: t(\"pride1\"),\n            show: false\n        },\n        {\n            text: t(\"pride2\"),\n            show: false\n        },\n        {\n            text: t(\"pride3\"),\n            show: false\n        },\n        {\n            text: t(\"pride4\"),\n            show: false\n        },\n        {\n            text: t(\"pride5\"),\n            show: false\n        },\n        {\n            text: t(\"pride6\"),\n            show: false\n        },\n        {\n            text: t(\"pride7\"),\n            show: false\n        },\n        {\n            text: t(\"pride8\"),\n            show: false\n        },\n        {\n            text: t(\"pride9\"),\n            show: false\n        }\n    ]);\n    const ImageItem = (param)=>{\n        let { src, size = 1, index } = param;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            onClick: ()=>{\n                setShowCoverList(showCoverList.map((item, idx)=>{\n                    if (idx === index) item.show = !item.show;\n                    return item;\n                }));\n            },\n            className: \"\".concat((_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default().about__content__prides__list__item), \" \").concat(size === 2 ? (_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default()[\"about__content__prides__list__item--large\"]) : size === 1 ? (_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default()[\"about__content__prides__list__item--normal\"]) : (_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default()[\"about__content__prides__list__item--small\"])),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                    src: src,\n                    width: size === 2 ? 478 : 231,\n                    height: size === 0 ? 154 : 326,\n                    alt: \"\",\n                    style: {\n                        height: \"100%\",\n                        width: \"100%\",\n                        objectFit: \"fill\"\n                    },\n                    unoptimized: true\n                }, void 0, false, {\n                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                    lineNumber: 458,\n                    columnNumber: 9\n                }, this),\n                showCoverList[index].show && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default().about__content__prides__list__item__cover),\n                    children: showCoverList[index].text\n                }, void 0, false, {\n                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                    lineNumber: 467,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n            lineNumber: 442,\n            columnNumber: 7\n        }, this);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"\".concat((_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default().about__content__prides)),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    children: t(\"cylanPrides\")\n                }, void 0, false, {\n                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                    lineNumber: 478,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"hide-on-small\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default().about__content__prides__list),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ImageItem, {\n                                    src: \"/pride-image-1.jpg\",\n                                    index: 0\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                    lineNumber: 481,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ImageItem, {\n                                    src: \"/pride-image-2.jpg\",\n                                    size: 2,\n                                    index: 1\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                    lineNumber: 482,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ImageItem, {\n                                    src: \"/pride-image-3.jpg\",\n                                    index: 2\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                    lineNumber: 487,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ImageItem, {\n                                    src: \"/pride-image-4.jpg\",\n                                    index: 3\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                    lineNumber: 488,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                            lineNumber: 480,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (_about_module_scss__WEBPACK_IMPORTED_MODULE_8___default().about__content__prides__list),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ImageItem, {\n                                    src: \"/pride-image-5.jpg\",\n                                    size: 0,\n                                    index: 4\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                    lineNumber: 491,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ImageItem, {\n                                    src: \"/pride-image-6.jpg\",\n                                    size: 0,\n                                    index: 5\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                    lineNumber: 496,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ImageItem, {\n                                    src: \"/pride-image-7.jpg\",\n                                    size: 0,\n                                    index: 6\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                    lineNumber: 501,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ImageItem, {\n                                    src: \"/pride-image-8.jpg\",\n                                    size: 0,\n                                    index: 7\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                    lineNumber: 506,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ImageItem, {\n                                    src: \"/pride-image-9.jpg\",\n                                    size: 0,\n                                    index: 8\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                                    lineNumber: 511,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                            lineNumber: 490,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                    lineNumber: 479,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_certificates__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n                    lineNumber: 519,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\about\\\\about-content.tsx\",\n            lineNumber: 477,\n            columnNumber: 7\n        }, this)\n    }, void 0, false);\n}\n_s5(Prides, \"/VTK/rcuaULTrNtxGRekcBaFSek=\", false, function() {\n    return [\n        _locales_client__WEBPACK_IMPORTED_MODULE_7__.useI18n\n    ];\n});\n_c5 = Prides;\nvar _c, _c1, _c2, _c3, _c4, _c5;\n$RefreshReg$(_c, \"AboutContentLayout\");\n$RefreshReg$(_c1, \"AboutContent\");\n$RefreshReg$(_c2, \"Content\");\n$RefreshReg$(_c3, \"Main\");\n$RefreshReg$(_c4, \"Career\");\n$RefreshReg$(_c5, \"Prides\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/[locale]/ui/about/about-content.tsx\n"));

/***/ })

});