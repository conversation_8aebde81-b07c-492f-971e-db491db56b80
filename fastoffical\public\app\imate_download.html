﻿<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="textml; charset=utf-8"/>
<title>Im <PERSON><PERSON></title>
</head>
<body>
    <script type="text/javascript"> 
        var language = navigator.language || navigator.userLanguage;
        var userAgent = navigator.userAgent;
        let phoneType = GetQueryString('phoneType')
        if(phoneType) {
            userAgent = phoneType
        }
        if (language.indexOf('zh') > -1) {
            document.title = '悦办客户端'
            //自动跳转
            if (/iPad|iPhone|iPod/.test(userAgent)) {
                document.location = "https://apps.apple.com/cn/app/%E6%82%A6%E5%8A%9E/id6743839622";
            } else if (/Android/.test(userAgent)) {
                // 判断是否为中文繁体
                if (language.toLowerCase() === 'zh-tw' || language.toLowerCase() === 'zh-hk') {
                    console.log('中文繁体');
                    document.location.href = "https://play.google.com/store/apps/details?id=com.cylan.yueban";
                } else {
                  var characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
                  var charactersLength = characters.length;
                  var result = "";
                  for (var i = 0; i < 6; i++) {
                    result += characters.charAt(Math.floor(Math.random() * charactersLength));
                  }
                  document.location.href = "http://www.jfgou.com/app/imate_download_and.html#" + result;
                }
            } else {
                document.location.href = "http://www.jfgou.com/app/imate_download_type.html";
            }
        } else {
            document.title = 'Im Mate App'
            if (/iPad|iPhone|iPod/.test(userAgent)) {
                document.location = "https://apps.apple.com/us/app/im-mate/id6743839622";
            } else if (/Android/.test(userAgent)) {
                document.location.href = "https://play.google.com/store/apps/details?id=com.cylan.yueban";
            } else {
                document.location.href = "http://www.jfgou.com/app/imate_download_type.html";
            }
        }



        function GetQueryString(name) {
          var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
          var r = window.location.search.substr(1).match(reg);
          if (r != null) return unescape(r[2]); return null;
        }
    </script> 
</body>
<html>